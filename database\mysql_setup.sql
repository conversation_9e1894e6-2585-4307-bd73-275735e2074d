-- Elite Transfer System - MySQL Database Setup
-- Created: 2025-01-15
-- Version: 7.0

-- Create database
CREATE DATABASE IF NOT EXISTS elite_transfer 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE elite_transfer;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    role ENUM('customer', 'agent', 'admin') DEFAULT 'customer',
    kyc_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    kyc_level ENUM('none', 'basic', 'enhanced', 'full') DEFAULT 'none',
    kyc_verified_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_kyc_status (kyc_status)
);

-- Create countries table
CREATE TABLE IF NOT EXISTS countries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(3) NOT NULL UNIQUE,
    currency VARCHAR(3) NOT NULL,
    flag_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_currency (currency)
);

-- Create transfers table
CREATE TABLE IF NOT EXISTS transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_code VARCHAR(20) UNIQUE NOT NULL,
    pickup_code VARCHAR(10) NOT NULL,
    sender_id INT NOT NULL,
    sender_name VARCHAR(255) NOT NULL,
    sender_phone VARCHAR(20) NOT NULL,
    sender_country_id INT NOT NULL,
    receiver_name VARCHAR(255) NOT NULL,
    receiver_phone VARCHAR(20) NOT NULL,
    receiver_country_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    converted_amount DECIMAL(15,2) NOT NULL,
    exchange_rate DECIMAL(10,6) NOT NULL,
    fee_amount DECIMAL(15,2) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    sender_currency VARCHAR(3) NOT NULL,
    receiver_currency VARCHAR(3) NOT NULL,
    status ENUM('pending', 'pending_payment', 'paid', 'processing', 'ready_for_pickup', 'completed', 'cancelled', 'refunded', 'pending_review') DEFAULT 'pending',
    payment_method ENUM('cash', 'card', 'bank_transfer', 'mobile_wallet') DEFAULT 'cash',
    pickup_method ENUM('cash', 'bank_deposit', 'mobile_wallet') DEFAULT 'cash',
    agent_id INT NULL,
    notes TEXT,
    risk_score INT DEFAULT 0,
    requires_kyc BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_country_id) REFERENCES countries(id),
    FOREIGN KEY (receiver_country_id) REFERENCES countries(id),
    FOREIGN KEY (agent_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_transfer_code (transfer_code),
    INDEX idx_pickup_code (pickup_code),
    INDEX idx_sender_id (sender_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_sender_phone (sender_phone),
    INDEX idx_receiver_phone (receiver_phone)
);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL,
    payment_provider VARCHAR(50),
    transaction_id VARCHAR(255),
    payment_intent_id VARCHAR(255),
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    response_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE,
    INDEX idx_transfer_id (transfer_id),
    INDEX idx_status (status),
    INDEX idx_transaction_id (transaction_id)
);

-- Create payment_intents table
CREATE TABLE IF NOT EXISTS payment_intents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_id INT NOT NULL,
    client_secret VARCHAR(255) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    status ENUM('pending', 'processing', 'succeeded', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE,
    INDEX idx_transfer_id (transfer_id)
);

-- Create exchange_rates table
CREATE TABLE IF NOT EXISTS exchange_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(15,6) NOT NULL,
    provider VARCHAR(50) DEFAULT 'fallback',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_currencies (from_currency, to_currency),
    INDEX idx_created_at (created_at)
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transfer_id INT NULL,
    type ENUM('sms', 'email', 'push') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_type (type)
);

-- Create external_api_logs table
CREATE TABLE IF NOT EXISTS external_api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    provider VARCHAR(100) NOT NULL,
    endpoint VARCHAR(255),
    request_data JSON,
    response_data JSON,
    status_code INT,
    response_time DECIMAL(10,3),
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_service_name (service_name),
    INDEX idx_provider (provider),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
);

-- Create aml_checks table
CREATE TABLE IF NOT EXISTS aml_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_id INT NULL,
    user_id INT NOT NULL,
    risk_score INT DEFAULT 0,
    action ENUM('approve', 'review', 'block', 'monitor') NOT NULL,
    checks_data JSON,
    reviewed_by INT NULL,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_transfer_id (transfer_id),
    INDEX idx_user_id (user_id),
    INDEX idx_risk_score (risk_score),
    INDEX idx_action (action)
);

-- Create kyc_documents table
CREATE TABLE IF NOT EXISTS kyc_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    document_type ENUM('id_document', 'proof_of_address', 'proof_of_income', 'bank_statement') NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_document_type (document_type),
    INDEX idx_status (status)
);

-- Create suspicious_activity_reports table
CREATE TABLE IF NOT EXISTS suspicious_activity_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sar_id VARCHAR(50) UNIQUE NOT NULL,
    transfer_id INT NULL,
    user_id INT NOT NULL,
    reason VARCHAR(255) NOT NULL,
    description TEXT,
    amount DECIMAL(15,2),
    status ENUM('pending', 'filed', 'closed') DEFAULT 'pending',
    filed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sar_id (sar_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Create currency_transaction_reports table
CREATE TABLE IF NOT EXISTS currency_transaction_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ctr_id VARCHAR(50) UNIQUE NOT NULL,
    transfer_id INT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    reason VARCHAR(255) NOT NULL,
    transaction_date DATE NOT NULL,
    status ENUM('pending', 'filed', 'closed') DEFAULT 'pending',
    filed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ctr_id (ctr_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Create system_health_checks table
CREATE TABLE IF NOT EXISTS system_health_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    overall_status ENUM('healthy', 'warning', 'unhealthy') NOT NULL,
    components_data JSON,
    metrics_data JSON,
    alerts_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_overall_status (overall_status),
    INDEX idx_created_at (created_at)
);

-- Create login_attempts table
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    success BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_ip_address (ip_address),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
);

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(255) UNIQUE NOT NULL,
    `value` TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (`key`)
);

-- Create performance_metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,4),
    metric_date DATE,
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_metric_date (metric_date),
    INDEX idx_category (category)
);

-- Create chargebacks table
CREATE TABLE IF NOT EXISTS chargebacks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    charge_id VARCHAR(255) NOT NULL,
    provider VARCHAR(100) NOT NULL,
    amount DECIMAL(15,2),
    reason VARCHAR(255),
    status ENUM('open', 'under_review', 'won', 'lost', 'warning_closed') DEFAULT 'open',
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_charge_id (charge_id),
    INDEX idx_provider (provider),
    INDEX idx_status (status)
);

-- Insert default countries
INSERT INTO countries (name, code, currency, is_active) VALUES
('Saudi Arabia', 'SA', 'SAR', TRUE),
('United Arab Emirates', 'AE', 'AED', TRUE),
('Egypt', 'EG', 'EGP', TRUE),
('Kuwait', 'KW', 'KWD', TRUE),
('Jordan', 'JO', 'JOD', TRUE),
('United States', 'US', 'USD', TRUE),
('United Kingdom', 'GB', 'GBP', TRUE),
('European Union', 'EU', 'EUR', TRUE),
('Qatar', 'QA', 'QAR', TRUE),
('Bahrain', 'BH', 'BHD', TRUE),
('Oman', 'OM', 'OMR', TRUE),
('Lebanon', 'LB', 'LBP', TRUE),
('Syria', 'SY', 'SYP', TRUE),
('Iraq', 'IQ', 'IQD', TRUE),
('Yemen', 'YE', 'YER', TRUE),
('Morocco', 'MA', 'MAD', TRUE),
('Tunisia', 'TN', 'TND', TRUE),
('Algeria', 'DZ', 'DZD', TRUE),
('Libya', 'LY', 'LYD', TRUE),
('Sudan', 'SD', 'SDG', TRUE)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- Insert default admin user
INSERT INTO users (name, email, phone, password, role, kyc_status, kyc_level, is_active) VALUES
('System Administrator', '<EMAIL>', '+966501234567', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'verified', 'full', TRUE)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- Insert default system settings
INSERT INTO system_settings (`key`, `value`, description) VALUES
('app_name', 'Elite Transfer System', 'Application name'),
('app_version', '7.0', 'Application version'),
('maintenance_mode', 'false', 'Maintenance mode status'),
('max_transfer_amount', '50000', 'Maximum transfer amount in USD'),
('min_transfer_amount', '1', 'Minimum transfer amount in USD'),
('default_transfer_fee_percentage', '2.5', 'Default transfer fee percentage'),
('default_transfer_fee_fixed', '5.00', 'Default fixed transfer fee'),
('aml_threshold_amount', '10000', 'AML check threshold amount'),
('kyc_required_amount', '3000', 'KYC required threshold amount'),
('sms_provider', 'local', 'SMS service provider'),
('email_provider', 'local', 'Email service provider'),
('exchange_rate_provider', 'fallback', 'Exchange rate service provider')
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);

-- Insert sample exchange rates
INSERT INTO exchange_rates (from_currency, to_currency, rate, provider) VALUES
('USD', 'SAR', 3.7500, 'fallback'),
('USD', 'AED', 3.6725, 'fallback'),
('USD', 'EGP', 30.9000, 'fallback'),
('USD', 'KWD', 0.3000, 'fallback'),
('USD', 'JOD', 0.7090, 'fallback'),
('USD', 'QAR', 3.6400, 'fallback'),
('USD', 'BHD', 0.3760, 'fallback'),
('USD', 'OMR', 0.3845, 'fallback'),
('EUR', 'USD', 1.0800, 'fallback'),
('GBP', 'USD', 1.2400, 'fallback'),
('SAR', 'EGP', 8.2400, 'fallback'),
('AED', 'EGP', 8.4200, 'fallback')
ON DUPLICATE KEY UPDATE rate = VALUES(rate);

-- Create indexes for better performance
CREATE INDEX idx_transfers_status_created ON transfers(status, created_at);
CREATE INDEX idx_transfers_sender_country ON transfers(sender_country_id, created_at);
CREATE INDEX idx_transfers_receiver_country ON transfers(receiver_country_id, created_at);
CREATE INDEX idx_payments_method_status ON payments(payment_method, status);
CREATE INDEX idx_notifications_user_type ON notifications(user_id, type, status);

-- Create views for reporting
CREATE OR REPLACE VIEW transfer_summary AS
SELECT 
    DATE(created_at) as transfer_date,
    COUNT(*) as total_transfers,
    SUM(amount) as total_amount,
    SUM(fee_amount) as total_fees,
    AVG(amount) as avg_amount,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_transfers
FROM transfers 
GROUP BY DATE(created_at)
ORDER BY transfer_date DESC;

CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    u.id,
    u.name,
    u.email,
    u.role,
    u.kyc_status,
    COUNT(t.id) as total_transfers,
    SUM(t.amount) as total_sent,
    SUM(t.fee_amount) as total_fees_paid,
    MAX(t.created_at) as last_transfer_date,
    u.created_at as registration_date
FROM users u
LEFT JOIN transfers t ON u.id = t.sender_id
GROUP BY u.id, u.name, u.email, u.role, u.kyc_status, u.created_at;

-- Grant privileges (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON elite_transfer.* TO 'elite_user'@'localhost' IDENTIFIED BY 'elite_password_2025';
-- FLUSH PRIVILEGES;

-- Show table status
SELECT 
    TABLE_NAME as 'Table',
    TABLE_ROWS as 'Rows',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'elite_transfer'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;
