<?php

class CacheService {
    private $cacheDir;
    private $defaultTTL = 3600; // 1 hour
    
    public function __construct($cacheDirectory = null) {
        $this->cacheDir = $cacheDirectory ?: __DIR__ . '/../../storage/cache';
        $this->ensureCacheDirectory();
    }
    
    private function ensureCacheDirectory() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Get cached data
     */
    public function get($key, $default = null) {
        $filename = $this->getCacheFilename($key);
        
        if (!file_exists($filename)) {
            return $default;
        }
        
        $data = file_get_contents($filename);
        $cached = json_decode($data, true);
        
        if (!$cached || !isset($cached['expires_at']) || !isset($cached['data'])) {
            return $default;
        }
        
        // Check if expired
        if (time() > $cached['expires_at']) {
            $this->forget($key);
            return $default;
        }
        
        return $cached['data'];
    }
    
    /**
     * Store data in cache
     */
    public function put($key, $data, $ttl = null) {
        $ttl = $ttl ?: $this->defaultTTL;
        $filename = $this->getCacheFilename($key);
        
        $cached = [
            'data' => $data,
            'created_at' => time(),
            'expires_at' => time() + $ttl
        ];
        
        return file_put_contents($filename, json_encode($cached)) !== false;
    }
    
    /**
     * Remember data (get from cache or execute callback)
     */
    public function remember($key, $callback, $ttl = null) {
        $cached = $this->get($key);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $data = $callback();
        $this->put($key, $data, $ttl);
        
        return $data;
    }
    
    /**
     * Remove item from cache
     */
    public function forget($key) {
        $filename = $this->getCacheFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public function flush() {
        $files = glob($this->cacheDir . '/*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Clean expired cache files
     */
    public function cleanExpired() {
        $files = glob($this->cacheDir . '/*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cached = json_decode($data, true);
            
            if (!$cached || !isset($cached['expires_at'])) {
                unlink($file);
                $cleaned++;
                continue;
            }
            
            if (time() > $cached['expires_at']) {
                unlink($file);
                $cleaned++;
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $expiredCount = 0;
        $validCount = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            $cached = json_decode($data, true);
            
            if (!$cached || !isset($cached['expires_at'])) {
                $expiredCount++;
                continue;
            }
            
            if (time() > $cached['expires_at']) {
                $expiredCount++;
            } else {
                $validCount++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_files' => $validCount,
            'expired_files' => $expiredCount,
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2)
        ];
    }
    
    private function getCacheFilename($key) {
        $hash = md5($key);
        return $this->cacheDir . '/' . $hash . '.cache';
    }
    
    /**
     * Cache database query results
     */
    public function cacheQuery($key, $callback, $ttl = 300) {
        return $this->remember("query:" . $key, $callback, $ttl);
    }
    
    /**
     * Cache user data
     */
    public function cacheUser($userId, $userData, $ttl = 1800) {
        return $this->put("user:" . $userId, $userData, $ttl);
    }
    
    /**
     * Get cached user data
     */
    public function getCachedUser($userId) {
        return $this->get("user:" . $userId);
    }
    
    /**
     * Cache transfer data
     */
    public function cacheTransfer($transferId, $transferData, $ttl = 600) {
        return $this->put("transfer:" . $transferId, $transferData, $ttl);
    }
    
    /**
     * Get cached transfer data
     */
    public function getCachedTransfer($transferId) {
        return $this->get("transfer:" . $transferId);
    }
    
    /**
     * Cache dashboard statistics
     */
    public function cacheDashboardStats($userId, $role, $stats, $ttl = 300) {
        $key = "dashboard_stats:" . $userId . ":" . $role;
        return $this->put($key, $stats, $ttl);
    }
    
    /**
     * Get cached dashboard statistics
     */
    public function getCachedDashboardStats($userId, $role) {
        $key = "dashboard_stats:" . $userId . ":" . $role;
        return $this->get($key);
    }
    
    /**
     * Cache exchange rates
     */
    public function cacheExchangeRates($rates, $ttl = 3600) {
        return $this->put("exchange_rates", $rates, $ttl);
    }
    
    /**
     * Get cached exchange rates
     */
    public function getCachedExchangeRates() {
        return $this->get("exchange_rates");
    }
    
    /**
     * Invalidate user-related cache
     */
    public function invalidateUserCache($userId) {
        $patterns = [
            "user:" . $userId,
            "dashboard_stats:" . $userId . ":*",
            "user_transfers:" . $userId,
            "user_notifications:" . $userId
        ];
        
        $invalidated = 0;
        foreach ($patterns as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // Handle wildcard patterns
                $prefix = str_replace('*', '', $pattern);
                $files = glob($this->cacheDir . '/*.cache');
                
                foreach ($files as $file) {
                    $key = $this->getKeyFromFilename($file);
                    if (strpos($key, $prefix) === 0) {
                        if (unlink($file)) {
                            $invalidated++;
                        }
                    }
                }
            } else {
                if ($this->forget($pattern)) {
                    $invalidated++;
                }
            }
        }
        
        return $invalidated;
    }
    
    private function getKeyFromFilename($filename) {
        // This is a simplified approach - in production, you'd want to store key mappings
        return basename($filename, '.cache');
    }
    
    /**
     * Warm up cache with frequently accessed data
     */
    public function warmUp($db) {
        $warmedUp = 0;
        
        try {
            // Cache countries
            $stmt = $db->query("SELECT * FROM countries WHERE is_active = 1");
            $countries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if ($this->put("countries", $countries, 7200)) {
                $warmedUp++;
            }
            
            // Cache system settings
            $stmt = $db->query("SELECT key, value FROM system_settings");
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            if ($this->put("system_settings", $settings, 3600)) {
                $warmedUp++;
            }
            
            // Cache exchange rates (mock data)
            $exchangeRates = [
                '1-2' => 1.02, // SAR to AED
                '1-3' => 8.25, // SAR to EGP
                '2-3' => 8.08, // AED to EGP
                '6-1' => 3.75, // USD to SAR
                '6-2' => 3.67, // USD to AED
                '6-3' => 30.90, // USD to EGP
            ];
            if ($this->cacheExchangeRates($exchangeRates)) {
                $warmedUp++;
            }
            
            // Cache recent transfer statistics
            $stmt = $db->query("
                SELECT 
                    COUNT(*) as total_transfers,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_amount
                FROM transfers 
                WHERE created_at > DATE('now', '-24 hours')
            ");
            $dailyStats = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($this->put("daily_stats", $dailyStats, 1800)) {
                $warmedUp++;
            }
            
        } catch (Exception $e) {
            error_log("Cache warm-up failed: " . $e->getMessage());
        }
        
        return $warmedUp;
    }
    
    /**
     * Get cache hit ratio
     */
    public function getHitRatio() {
        // This would require tracking hits/misses in production
        // For now, return a mock ratio
        return [
            'hits' => rand(800, 950),
            'misses' => rand(50, 200),
            'ratio' => rand(80, 95) / 100
        ];
    }
}
