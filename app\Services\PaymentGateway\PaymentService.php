<?php

class PaymentService {
    private $providers = [];
    private $defaultProvider = 'stripe';
    private $config;
    private $db;
    
    public function __construct($config = [], $database = null) {
        $this->config = $config;
        $this->db = $database;
        $this->initializeProviders();
    }
    
    private function initializeProviders() {
        // Stripe Payment Provider
        $this->providers['stripe'] = [
            'class' => 'StripePaymentProvider',
            'config' => [
                'public_key' => $this->config['stripe']['public_key'] ?? '',
                'secret_key' => $this->config['stripe']['secret_key'] ?? '',
                'webhook_secret' => $this->config['stripe']['webhook_secret'] ?? ''
            ]
        ];
        
        // PayPal Payment Provider
        $this->providers['paypal'] = [
            'class' => 'PayPalPaymentProvider',
            'config' => [
                'client_id' => $this->config['paypal']['client_id'] ?? '',
                'client_secret' => $this->config['paypal']['client_secret'] ?? '',
                'mode' => $this->config['paypal']['mode'] ?? 'sandbox'
            ]
        ];
        
        // Bank Transfer Provider
        $this->providers['bank_transfer'] = [
            'class' => 'BankTransferProvider',
            'config' => [
                'api_endpoint' => $this->config['bank']['api_endpoint'] ?? '',
                'api_key' => $this->config['bank']['api_key'] ?? '',
                'api_secret' => $this->config['bank']['api_secret'] ?? ''
            ]
        ];
        
        // Cash Pickup Provider
        $this->providers['cash_pickup'] = [
            'class' => 'CashPickupProvider',
            'config' => []
        ];
    }
    
    /**
     * Process payment for transfer
     */
    public function processPayment($transferId, $amount, $currency, $paymentMethod, $paymentData = []) {
        try {
            // Start payment transaction
            $this->startPaymentTransaction($transferId, $amount, $currency, $paymentMethod);
            
            $provider = $this->getProviderForMethod($paymentMethod);
            $providerInstance = $this->createProvider($provider);
            
            $result = $providerInstance->processPayment($amount, $currency, $paymentData);
            
            if ($result['success']) {
                // Update payment status
                $this->updatePaymentStatus($transferId, 'completed', $result);
                
                // Update transfer status
                $this->updateTransferStatus($transferId, 'paid');
                
                return [
                    'success' => true,
                    'payment_id' => $result['payment_id'],
                    'transaction_id' => $result['transaction_id'] ?? null,
                    'status' => 'completed',
                    'message' => 'تم الدفع بنجاح'
                ];
            } else {
                // Update payment status as failed
                $this->updatePaymentStatus($transferId, 'failed', $result);
                
                return [
                    'success' => false,
                    'error' => $result['error'] ?? 'فشل في معالجة الدفع',
                    'error_code' => $result['error_code'] ?? 'PAYMENT_FAILED'
                ];
            }
            
        } catch (Exception $e) {
            // Log error and update status
            $this->updatePaymentStatus($transferId, 'error', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => 'حدث خطأ أثناء معالجة الدفع',
                'error_code' => 'SYSTEM_ERROR'
            ];
        }
    }
    
    /**
     * Create payment intent (for card payments)
     */
    public function createPaymentIntent($transferId, $amount, $currency, $paymentMethod = 'stripe') {
        try {
            $provider = $this->getProviderForMethod($paymentMethod);
            $providerInstance = $this->createProvider($provider);
            
            $result = $providerInstance->createPaymentIntent($amount, $currency, [
                'transfer_id' => $transferId,
                'metadata' => [
                    'transfer_id' => $transferId,
                    'service' => 'elite_transfer'
                ]
            ]);
            
            if ($result['success']) {
                // Store payment intent
                $this->storePaymentIntent($transferId, $result['client_secret'], $paymentMethod);
                
                return [
                    'success' => true,
                    'client_secret' => $result['client_secret'],
                    'payment_intent_id' => $result['payment_intent_id']
                ];
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Confirm payment
     */
    public function confirmPayment($paymentIntentId, $paymentMethod = 'stripe') {
        try {
            $provider = $this->getProviderForMethod($paymentMethod);
            $providerInstance = $this->createProvider($provider);
            
            return $providerInstance->confirmPayment($paymentIntentId);
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process refund
     */
    public function processRefund($transferId, $amount = null, $reason = 'requested_by_customer') {
        try {
            // Get payment details
            $payment = $this->getPaymentByTransferId($transferId);
            
            if (!$payment) {
                throw new Exception('Payment not found for transfer');
            }
            
            $provider = $payment['payment_method'];
            $providerInstance = $this->createProvider($provider);
            
            $refundAmount = $amount ?? $payment['amount'];
            
            $result = $providerInstance->processRefund($payment['transaction_id'], $refundAmount, $reason);
            
            if ($result['success']) {
                // Update payment status
                $this->updatePaymentStatus($transferId, 'refunded', $result);
                
                // Update transfer status
                $this->updateTransferStatus($transferId, 'refunded');
                
                return [
                    'success' => true,
                    'refund_id' => $result['refund_id'],
                    'amount' => $refundAmount,
                    'message' => 'تم استرداد المبلغ بنجاح'
                ];
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get payment status
     */
    public function getPaymentStatus($transferId) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM payments 
                WHERE transfer_id = ? 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$transferId]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Handle webhook from payment provider
     */
    public function handleWebhook($provider, $payload, $signature = null) {
        try {
            $providerInstance = $this->createProvider($provider);
            
            $event = $providerInstance->verifyWebhook($payload, $signature);
            
            if ($event) {
                return $this->processWebhookEvent($event, $provider);
            }
            
            return ['success' => false, 'error' => 'Invalid webhook signature'];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    private function processWebhookEvent($event, $provider) {
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                return $this->handlePaymentSuccess($event['data'], $provider);
                
            case 'payment_intent.payment_failed':
                return $this->handlePaymentFailure($event['data'], $provider);
                
            case 'charge.dispute.created':
                return $this->handleChargeback($event['data'], $provider);
                
            default:
                return ['success' => true, 'message' => 'Event processed'];
        }
    }
    
    private function handlePaymentSuccess($paymentData, $provider) {
        $transferId = $paymentData['metadata']['transfer_id'] ?? null;
        
        if ($transferId) {
            $this->updatePaymentStatus($transferId, 'completed', $paymentData);
            $this->updateTransferStatus($transferId, 'paid');
        }
        
        return ['success' => true, 'message' => 'Payment success processed'];
    }
    
    private function handlePaymentFailure($paymentData, $provider) {
        $transferId = $paymentData['metadata']['transfer_id'] ?? null;
        
        if ($transferId) {
            $this->updatePaymentStatus($transferId, 'failed', $paymentData);
            $this->updateTransferStatus($transferId, 'payment_failed');
        }
        
        return ['success' => true, 'message' => 'Payment failure processed'];
    }
    
    private function handleChargeback($chargeData, $provider) {
        // Handle chargeback/dispute
        $this->logChargeback($chargeData, $provider);
        
        return ['success' => true, 'message' => 'Chargeback processed'];
    }
    
    private function getProviderForMethod($paymentMethod) {
        $methodProviderMap = [
            'card' => 'stripe',
            'stripe' => 'stripe',
            'paypal' => 'paypal',
            'bank_transfer' => 'bank_transfer',
            'cash_pickup' => 'cash_pickup'
        ];
        
        return $methodProviderMap[$paymentMethod] ?? $this->defaultProvider;
    }
    
    private function createProvider($provider) {
        if (!isset($this->providers[$provider])) {
            throw new Exception("Payment provider '{$provider}' not found");
        }
        
        $providerConfig = $this->providers[$provider];
        $className = $providerConfig['class'];
        
        switch ($className) {
            case 'StripePaymentProvider':
                return new StripePaymentProvider($providerConfig['config']);
            case 'PayPalPaymentProvider':
                return new PayPalPaymentProvider($providerConfig['config']);
            case 'BankTransferProvider':
                return new BankTransferProvider($providerConfig['config']);
            case 'CashPickupProvider':
                return new CashPickupProvider($providerConfig['config']);
            default:
                throw new Exception("Unknown provider class: {$className}");
        }
    }
    
    private function startPaymentTransaction($transferId, $amount, $currency, $paymentMethod) {
        $stmt = $this->db->prepare("
            INSERT INTO payments (
                transfer_id, amount, currency, payment_method, status, created_at
            ) VALUES (?, ?, ?, ?, 'pending', ?)
        ");
        $stmt->execute([$transferId, $amount, $currency, $paymentMethod, date('Y-m-d H:i:s')]);
    }
    
    private function updatePaymentStatus($transferId, $status, $data = []) {
        $stmt = $this->db->prepare("
            UPDATE payments 
            SET status = ?, response_data = ?, updated_at = ?
            WHERE transfer_id = ?
        ");
        $stmt->execute([$status, json_encode($data), date('Y-m-d H:i:s'), $transferId]);
    }
    
    private function updateTransferStatus($transferId, $status) {
        $stmt = $this->db->prepare("
            UPDATE transfers 
            SET status = ?, updated_at = ?
            WHERE id = ?
        ");
        $stmt->execute([$status, date('Y-m-d H:i:s'), $transferId]);
    }
    
    private function storePaymentIntent($transferId, $clientSecret, $paymentMethod) {
        $stmt = $this->db->prepare("
            INSERT INTO payment_intents (
                transfer_id, client_secret, payment_method, created_at
            ) VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$transferId, $clientSecret, $paymentMethod, date('Y-m-d H:i:s')]);
    }
    
    private function getPaymentByTransferId($transferId) {
        $stmt = $this->db->prepare("
            SELECT * FROM payments 
            WHERE transfer_id = ? AND status = 'completed'
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$transferId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function logChargeback($chargeData, $provider) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO chargebacks (
                    charge_id, provider, amount, reason, status, data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $chargeData['id'],
                $provider,
                $chargeData['amount'] ?? 0,
                $chargeData['reason'] ?? 'unknown',
                'open',
                json_encode($chargeData),
                date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log chargeback: " . $e->getMessage());
        }
    }
    
    /**
     * Get payment methods available for country
     */
    public function getAvailablePaymentMethods($countryCode) {
        $methods = [
            'US' => ['card', 'paypal', 'bank_transfer'],
            'SA' => ['card', 'bank_transfer', 'cash_pickup'],
            'AE' => ['card', 'bank_transfer', 'cash_pickup'],
            'EG' => ['cash_pickup', 'bank_transfer'],
            'default' => ['card', 'cash_pickup']
        ];
        
        return $methods[$countryCode] ?? $methods['default'];
    }
    
    /**
     * Calculate payment processing fees
     */
    public function calculateProcessingFees($amount, $paymentMethod, $currency = 'USD') {
        $feeStructure = [
            'card' => ['percentage' => 2.9, 'fixed' => 0.30],
            'paypal' => ['percentage' => 3.4, 'fixed' => 0.30],
            'bank_transfer' => ['percentage' => 0.8, 'fixed' => 0.00],
            'cash_pickup' => ['percentage' => 0.0, 'fixed' => 0.00]
        ];
        
        $fees = $feeStructure[$paymentMethod] ?? $feeStructure['card'];
        
        $percentageFee = $amount * ($fees['percentage'] / 100);
        $fixedFee = $fees['fixed'];
        $totalFee = $percentageFee + $fixedFee;
        
        return [
            'percentage_fee' => $percentageFee,
            'fixed_fee' => $fixedFee,
            'total_fee' => $totalFee,
            'net_amount' => $amount - $totalFee
        ];
    }
}
