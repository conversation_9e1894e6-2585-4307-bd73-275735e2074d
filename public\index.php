<?php

// Elite Financial Transfer System v6.0
// Simple routing system

$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove leading slash
$path = ltrim($path, '/');

// Simple routing
switch ($path) {
    case '':
    case 'home':
        include 'welcome.php';
        break;

    case 'login':
        include 'login.php';
        break;

    case 'register':
        include 'register.php';
        break;

    case 'dashboard':
        include 'dashboard.php';
        break;

    case 'transfers/create':
        include 'transfer-create.php';
        break;

    case 'track':
        include 'track.php';
        break;

    case 'demo.html':
        include 'demo.html';
        break;

    default:
        // Redirect to demo page for now
        header('Location: /demo.html');
        exit;
}
