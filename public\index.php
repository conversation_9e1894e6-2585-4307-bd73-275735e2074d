<?php

// Elite Transfer System - Simple Router
// Version 7.0

// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Get the requested URI
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove leading slash
$path = ltrim($path, '/');

// Define routes
$routes = [
    '' => 'home.php',
    'home' => 'home.php',
    'login' => 'login.php',
    'logout' => 'logout.php',
    'register' => 'register.php',
    'dashboard' => 'dashboard.php',
    'create-transfer' => 'create-transfer.php',
    'track-transfer' => 'track-transfer.php',
    'admin/users' => 'admin/users.php',
    'admin/transfers' => 'admin/transfers.php',
    'admin/reports' => 'admin/reports.php',
    'admin/settings' => 'admin/settings.php',
    'admin/monitoring' => 'admin/monitoring.php',
    'compliance/dashboard' => 'compliance/dashboard.php',
    'api/health' => 'api/health.php'
];

// Check if route exists
if (array_key_exists($path, $routes)) {
    $file = __DIR__ . '/' . $routes[$path];

    if (file_exists($file)) {
        include $file;
    } else {
        // File not found, show 404
        http_response_code(404);
        include __DIR__ . '/404.php';
    }
} else {
    // Route not found, show 404
    http_response_code(404);
    include __DIR__ . '/404.php';
}
