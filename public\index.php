<?php

// Elite Financial Transfer System v6.0
// Enhanced system with database support

define('LARAVEL_START', microtime(true));

try {
    // Load the enhanced application
    $app = require_once __DIR__.'/../bootstrap/simple-app.php';

    // Run the application
    $app->run();

} catch (Exception $e) {
    // Fallback to demo page if there's an error
    error_log("Application error: " . $e->getMessage());

    if (file_exists(__DIR__ . '/demo.html')) {
        header('Location: /demo.html');
        exit;
    } else {
        echo "<h1>System Error</h1>";
        echo "<p>The system is temporarily unavailable. Please try again later.</p>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}
