<?php

echo "🔧 Database Fix - Adding missing updated_at column\n\n";

try {
    // Connect to database
    $db = new PDO('sqlite:database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful\n\n";
    
    // Check if updated_at column exists
    $result = $db->query("PRAGMA table_info(transfers)");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    $hasUpdatedAt = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'updated_at') {
            $hasUpdatedAt = true;
            break;
        }
    }
    
    if ($hasUpdatedAt) {
        echo "✅ Column 'updated_at' already exists\n";
    } else {
        echo "❌ Column 'updated_at' is missing. Adding it now...\n";
        
        // Add the missing column (SQLite doesn't support CURRENT_TIMESTAMP as default in ALTER TABLE)
        $db->exec("ALTER TABLE transfers ADD COLUMN updated_at DATETIME");

        // Update existing records to have updated_at = created_at
        $db->exec("UPDATE transfers SET updated_at = created_at WHERE updated_at IS NULL");
        
        echo "✅ Column 'updated_at' added successfully\n";
    }
    
    echo "\n";
    
    // Verify the fix
    echo "🔍 Verifying the fix:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $result = $db->query("PRAGMA table_info(transfers)");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Transfers table columns:\n";
    foreach ($columns as $column) {
        $marker = $column['name'] === 'updated_at' ? ' ✅' : '';
        echo sprintf("  %-20s %-15s%s\n", 
            $column['name'], 
            $column['type'],
            $marker
        );
    }
    
    echo "\n";
    
    // Test insert with updated_at
    echo "🧪 Testing INSERT with updated_at:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    try {
        $testStmt = $db->prepare("
            INSERT INTO transfers (
                transfer_code, pickup_code, sender_id, sender_name, sender_phone, sender_country_id,
                receiver_name, receiver_phone, receiver_country_id, amount, converted_amount,
                exchange_rate, fee_amount, total_amount, sender_currency, receiver_currency,
                status, payment_method, pickup_method, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ");
        
        // This is just a test - we won't actually execute it
        echo "✅ INSERT statement with updated_at is valid\n";
        
    } catch (Exception $e) {
        echo "❌ INSERT test failed: " . $e->getMessage() . "\n";
    }
    
    // Show current transfers count
    $count = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
    echo "\nCurrent transfers in database: $count\n";
    
    if ($count > 0) {
        echo "\nSample transfer with updated_at:\n";
        $sample = $db->query("SELECT transfer_code, created_at, updated_at FROM transfers LIMIT 1")->fetch(PDO::FETCH_ASSOC);
        if ($sample) {
            echo "  Transfer Code: {$sample['transfer_code']}\n";
            echo "  Created At: {$sample['created_at']}\n";
            echo "  Updated At: {$sample['updated_at']}\n";
        }
    }
    
    echo "\n✅ Database fix completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    
    // If the error is about the column already existing, that's actually good
    if (strpos($e->getMessage(), 'duplicate column name') !== false) {
        echo "✅ Column already exists - no action needed\n";
    }
}

echo "\n🎉 You can now use the transfer creation form without errors!\n";

?>
