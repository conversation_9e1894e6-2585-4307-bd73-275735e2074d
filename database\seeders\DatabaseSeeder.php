<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Branch;
use App\Models\TransferFee;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create currencies first
        $this->createCurrencies();
        
        // Create countries
        $this->createCountries();
        
        // Create branches
        $this->createBranches();
        
        // Create users
        $this->createUsers();
        
        // Create transfer fees
        $this->createTransferFees();
    }

    private function createCurrencies()
    {
        $currencies = [
            [
                'name' => 'US Dollar',
                'name_ar' => 'الدولار الأمريكي',
                'code' => 'USD',
                'symbol' => '$',
                'decimal_places' => 2,
                'exchange_rate_to_usd' => 1.00000000,
                'is_active' => true,
                'is_base_currency' => true,
            ],
            [
                'name' => 'Euro',
                'name_ar' => 'اليورو',
                'code' => 'EUR',
                'symbol' => '€',
                'decimal_places' => 2,
                'exchange_rate_to_usd' => 1.08500000,
                'is_active' => true,
            ],
            [
                'name' => 'Saudi Riyal',
                'name_ar' => 'الريال السعودي',
                'code' => 'SAR',
                'symbol' => 'ر.س',
                'decimal_places' => 2,
                'exchange_rate_to_usd' => 0.26666667,
                'is_active' => true,
            ],
            [
                'name' => 'UAE Dirham',
                'name_ar' => 'الدرهم الإماراتي',
                'code' => 'AED',
                'symbol' => 'د.إ',
                'decimal_places' => 2,
                'exchange_rate_to_usd' => 0.27225000,
                'is_active' => true,
            ],
            [
                'name' => 'Egyptian Pound',
                'name_ar' => 'الجنيه المصري',
                'code' => 'EGP',
                'symbol' => 'ج.م',
                'decimal_places' => 2,
                'exchange_rate_to_usd' => 0.03225806,
                'is_active' => true,
            ],
            [
                'name' => 'British Pound',
                'name_ar' => 'الجنيه الإسترليني',
                'code' => 'GBP',
                'symbol' => '£',
                'decimal_places' => 2,
                'exchange_rate_to_usd' => 1.27000000,
                'is_active' => true,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::create($currency);
        }
    }

    private function createCountries()
    {
        $usd = Currency::where('code', 'USD')->first();
        $eur = Currency::where('code', 'EUR')->first();
        $sar = Currency::where('code', 'SAR')->first();
        $aed = Currency::where('code', 'AED')->first();
        $egp = Currency::where('code', 'EGP')->first();
        $gbp = Currency::where('code', 'GBP')->first();

        $countries = [
            [
                'name' => 'United States',
                'name_ar' => 'الولايات المتحدة',
                'code' => 'USA',
                'iso_code' => 'US',
                'phone_code' => '+1',
                'currency_id' => $usd->id,
                'is_active' => true,
                'is_sender_country' => true,
                'is_receiver_country' => true,
                'timezone' => 'America/New_York',
                'processing_time_hours' => 24,
                'supported_languages' => ['en', 'es'],
            ],
            [
                'name' => 'Saudi Arabia',
                'name_ar' => 'المملكة العربية السعودية',
                'code' => 'SAU',
                'iso_code' => 'SA',
                'phone_code' => '+966',
                'currency_id' => $sar->id,
                'is_active' => true,
                'is_sender_country' => true,
                'is_receiver_country' => true,
                'timezone' => 'Asia/Riyadh',
                'processing_time_hours' => 12,
                'supported_languages' => ['ar', 'en'],
            ],
            [
                'name' => 'United Arab Emirates',
                'name_ar' => 'الإمارات العربية المتحدة',
                'code' => 'UAE',
                'iso_code' => 'AE',
                'phone_code' => '+971',
                'currency_id' => $aed->id,
                'is_active' => true,
                'is_sender_country' => true,
                'is_receiver_country' => true,
                'timezone' => 'Asia/Dubai',
                'processing_time_hours' => 12,
                'supported_languages' => ['ar', 'en'],
            ],
            [
                'name' => 'Egypt',
                'name_ar' => 'مصر',
                'code' => 'EGY',
                'iso_code' => 'EG',
                'phone_code' => '+20',
                'currency_id' => $egp->id,
                'is_active' => true,
                'is_sender_country' => true,
                'is_receiver_country' => true,
                'timezone' => 'Africa/Cairo',
                'processing_time_hours' => 24,
                'supported_languages' => ['ar', 'en'],
            ],
            [
                'name' => 'United Kingdom',
                'name_ar' => 'المملكة المتحدة',
                'code' => 'GBR',
                'iso_code' => 'GB',
                'phone_code' => '+44',
                'currency_id' => $gbp->id,
                'is_active' => true,
                'is_sender_country' => true,
                'is_receiver_country' => true,
                'timezone' => 'Europe/London',
                'processing_time_hours' => 24,
                'supported_languages' => ['en'],
            ],
            [
                'name' => 'Germany',
                'name_ar' => 'ألمانيا',
                'code' => 'DEU',
                'iso_code' => 'DE',
                'phone_code' => '+49',
                'currency_id' => $eur->id,
                'is_active' => true,
                'is_sender_country' => true,
                'is_receiver_country' => true,
                'timezone' => 'Europe/Berlin',
                'processing_time_hours' => 24,
                'supported_languages' => ['de', 'en'],
            ],
        ];

        foreach ($countries as $country) {
            Country::create($country);
        }
    }

    private function createBranches()
    {
        $usa = Country::where('code', 'USA')->first();
        $sau = Country::where('code', 'SAU')->first();
        $uae = Country::where('code', 'UAE')->first();

        $branches = [
            [
                'name' => 'Elite Transfer - New York',
                'code' => 'ET-NY-001',
                'country_id' => $usa->id,
                'city' => 'New York',
                'address' => '123 Broadway, New York, NY 10001',
                'phone' => '******-555-0001',
                'email' => '<EMAIL>',
                'is_active' => true,
                'operating_hours' => [
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '18:00'],
                    'friday' => ['open' => '09:00', 'close' => '18:00'],
                    'saturday' => ['open' => '10:00', 'close' => '16:00'],
                    'sunday' => ['closed' => true],
                ],
                'supported_currencies' => ['USD', 'EUR', 'GBP'],
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'timezone' => 'America/New_York',
            ],
            [
                'name' => 'Elite Transfer - Riyadh',
                'code' => 'ET-RY-001',
                'country_id' => $sau->id,
                'city' => 'Riyadh',
                'address' => 'King Fahd Road, Riyadh 12345, Saudi Arabia',
                'phone' => '+966-11-555-0001',
                'email' => '<EMAIL>',
                'is_active' => true,
                'operating_hours' => [
                    'sunday' => ['open' => '08:00', 'close' => '17:00'],
                    'monday' => ['open' => '08:00', 'close' => '17:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '17:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '17:00'],
                    'thursday' => ['open' => '08:00', 'close' => '17:00'],
                    'friday' => ['closed' => true],
                    'saturday' => ['open' => '09:00', 'close' => '15:00'],
                ],
                'supported_currencies' => ['SAR', 'USD', 'EUR'],
                'latitude' => 24.7136,
                'longitude' => 46.6753,
                'timezone' => 'Asia/Riyadh',
            ],
            [
                'name' => 'Elite Transfer - Dubai',
                'code' => 'ET-DB-001',
                'country_id' => $uae->id,
                'city' => 'Dubai',
                'address' => 'Sheikh Zayed Road, Dubai, UAE',
                'phone' => '+971-4-555-0001',
                'email' => '<EMAIL>',
                'is_active' => true,
                'operating_hours' => [
                    'sunday' => ['open' => '08:00', 'close' => '18:00'],
                    'monday' => ['open' => '08:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '18:00'],
                    'thursday' => ['open' => '08:00', 'close' => '18:00'],
                    'friday' => ['closed' => true],
                    'saturday' => ['open' => '09:00', 'close' => '16:00'],
                ],
                'supported_currencies' => ['AED', 'USD', 'EUR', 'SAR'],
                'latitude' => 25.2048,
                'longitude' => 55.2708,
                'timezone' => 'Asia/Dubai',
            ],
        ];

        foreach ($branches as $branch) {
            Branch::create($branch);
        }
    }

    private function createUsers()
    {
        $riyadhBranch = Branch::where('code', 'ET-RY-001')->first();
        $dubaiBranch = Branch::where('code', 'ET-DB-001')->first();
        $nyBranch = Branch::where('code', 'ET-NY-001')->first();
        
        $saudiArabia = Country::where('code', 'SAU')->first();
        $uae = Country::where('code', 'UAE')->first();
        $usa = Country::where('code', 'USA')->first();

        // Super Admin
        User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => Hash::make('password123'),
            'role' => 'super_admin',
            'country_id' => $saudiArabia->id,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'two_factor_enabled' => true,
            'daily_limit' => 100000,
            'monthly_limit' => 1000000,
        ]);

        // Branch Managers
        $riyadhManager = User::create([
            'name' => 'Ahmed Al-Rashid',
            'email' => '<EMAIL>',
            'phone' => '+966501234568',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'branch_id' => $riyadhBranch->id,
            'country_id' => $saudiArabia->id,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'daily_limit' => 50000,
            'monthly_limit' => 500000,
        ]);

        $dubaiManager = User::create([
            'name' => 'Mohammed Al-Maktoum',
            'email' => '<EMAIL>',
            'phone' => '+971501234567',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'branch_id' => $dubaiBranch->id,
            'country_id' => $uae->id,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'daily_limit' => 50000,
            'monthly_limit' => 500000,
        ]);

        // Update branch managers
        $riyadhBranch->update(['manager_id' => $riyadhManager->id]);
        $dubaiBranch->update(['manager_id' => $dubaiManager->id]);

        // Agents
        $agent1 = User::create([
            'name' => 'Fatima Al-Zahra',
            'email' => '<EMAIL>',
            'phone' => '+966501234569',
            'password' => Hash::make('password123'),
            'role' => 'agent',
            'branch_id' => $riyadhBranch->id,
            'country_id' => $saudiArabia->id,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'commission_rate' => 1.50,
            'daily_limit' => 25000,
            'monthly_limit' => 250000,
        ]);
        $agent1->generateAgentCode();

        $agent2 = User::create([
            'name' => 'Sarah Al-Mansouri',
            'email' => '<EMAIL>',
            'phone' => '+971501234568',
            'password' => Hash::make('password123'),
            'role' => 'agent',
            'branch_id' => $dubaiBranch->id,
            'country_id' => $uae->id,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'commission_rate' => 1.50,
            'daily_limit' => 25000,
            'monthly_limit' => 250000,
        ]);
        $agent2->generateAgentCode();

        // Customers
        User::create([
            'name' => 'Omar Abdullah',
            'email' => '<EMAIL>',
            'phone' => '+966501234570',
            'password' => Hash::make('password123'),
            'role' => 'customer',
            'country_id' => $saudiArabia->id,
            'national_id' => '1234567890',
            'date_of_birth' => '1990-01-15',
            'gender' => 'male',
            'address' => 'Riyadh, Saudi Arabia',
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'daily_limit' => 5000,
            'monthly_limit' => 50000,
        ]);

        User::create([
            'name' => 'Aisha Hassan',
            'email' => '<EMAIL>',
            'phone' => '+971501234569',
            'password' => Hash::make('password123'),
            'role' => 'customer',
            'country_id' => $uae->id,
            'national_id' => '784199012345678',
            'date_of_birth' => '1985-03-22',
            'gender' => 'female',
            'address' => 'Dubai, UAE',
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_status' => 'approved',
            'daily_limit' => 5000,
            'monthly_limit' => 50000,
        ]);
    }

    private function createTransferFees()
    {
        $countries = Country::all();
        $currencies = Currency::all();

        // Create some sample fee structures
        foreach ($countries as $senderCountry) {
            foreach ($countries as $receiverCountry) {
                if ($senderCountry->id !== $receiverCountry->id) {
                    foreach ($currencies as $senderCurrency) {
                        foreach ($currencies as $receiverCurrency) {
                            TransferFee::create([
                                'sender_country_id' => $senderCountry->id,
                                'receiver_country_id' => $receiverCountry->id,
                                'sender_currency_id' => $senderCurrency->id,
                                'receiver_currency_id' => $receiverCurrency->id,
                                'min_amount' => 1.00,
                                'max_amount' => 999999.99,
                                'fixed_fee' => 5.00,
                                'percentage_fee' => 2.50,
                                'agent_commission' => 0.50,
                                'branch_commission' => 0.25,
                                'fee_type' => 'standard',
                                'is_active' => true,
                            ]);
                        }
                    }
                }
            }
        }
    }
}
