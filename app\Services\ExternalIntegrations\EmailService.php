<?php

class EmailService {
    private $providers = [];
    private $defaultProvider = 'sendgrid';
    private $config;
    
    public function __construct($config = []) {
        $this->config = $config;
        $this->initializeProviders();
    }
    
    private function initializeProviders() {
        // SendGrid Provider
        $this->providers['sendgrid'] = [
            'class' => 'SendGridEmailProvider',
            'config' => [
                'api_key' => $this->config['sendgrid']['api_key'] ?? '',
                'from_email' => $this->config['sendgrid']['from_email'] ?? '<EMAIL>',
                'from_name' => $this->config['sendgrid']['from_name'] ?? 'Elite Transfer System'
            ]
        ];
        
        // AWS SES Provider
        $this->providers['aws_ses'] = [
            'class' => 'AWSSESProvider',
            'config' => [
                'access_key' => $this->config['aws']['access_key'] ?? '',
                'secret_key' => $this->config['aws']['secret_key'] ?? '',
                'region' => $this->config['aws']['region'] ?? 'us-east-1',
                'from_email' => $this->config['aws']['from_email'] ?? '<EMAIL>'
            ]
        ];
        
        // SMTP Provider
        $this->providers['smtp'] = [
            'class' => 'SMTPEmailProvider',
            'config' => [
                'host' => $this->config['smtp']['host'] ?? '',
                'port' => $this->config['smtp']['port'] ?? 587,
                'username' => $this->config['smtp']['username'] ?? '',
                'password' => $this->config['smtp']['password'] ?? '',
                'encryption' => $this->config['smtp']['encryption'] ?? 'tls',
                'from_email' => $this->config['smtp']['from_email'] ?? '<EMAIL>',
                'from_name' => $this->config['smtp']['from_name'] ?? 'Elite Transfer System'
            ]
        ];
        
        // Local Email Provider (for testing)
        $this->providers['local'] = [
            'class' => 'LocalEmailProvider',
            'config' => []
        ];
    }
    
    /**
     * Send email using configured provider
     */
    public function sendEmail($to, $subject, $body, $isHtml = true, $provider = null) {
        $provider = $provider ?: $this->defaultProvider;
        
        if (!isset($this->providers[$provider])) {
            throw new Exception("Email Provider '{$provider}' not found");
        }
        
        $providerConfig = $this->providers[$provider];
        
        try {
            $providerInstance = $this->createProvider($providerConfig['class'], $providerConfig['config']);
            return $providerInstance->send($to, $subject, $body, $isHtml);
        } catch (Exception $e) {
            // Fallback to next available provider
            return $this->sendWithFallback($to, $subject, $body, $isHtml, $provider);
        }
    }
    
    private function sendWithFallback($to, $subject, $body, $isHtml, $failedProvider) {
        $availableProviders = array_keys($this->providers);
        $remainingProviders = array_diff($availableProviders, [$failedProvider]);
        
        foreach ($remainingProviders as $provider) {
            try {
                $providerConfig = $this->providers[$provider];
                $providerInstance = $this->createProvider($providerConfig['class'], $providerConfig['config']);
                
                $result = $providerInstance->send($to, $subject, $body, $isHtml);
                $result['fallback_used'] = true;
                $result['fallback_provider'] = $provider;
                
                return $result;
            } catch (Exception $e) {
                continue;
            }
        }
        
        throw new Exception("All email providers failed");
    }
    
    private function createProvider($className, $config) {
        switch ($className) {
            case 'SendGridEmailProvider':
                return new SendGridEmailProvider($config);
            case 'AWSSESProvider':
                return new AWSSESProvider($config);
            case 'SMTPEmailProvider':
                return new SMTPEmailProvider($config);
            case 'LocalEmailProvider':
            default:
                return new LocalEmailProvider($config);
        }
    }
    
    /**
     * Send templated email
     */
    public function sendTemplatedEmail($to, $templateId, $templateData, $provider = null) {
        $template = $this->getEmailTemplate($templateId);
        
        if (!$template) {
            throw new Exception("Email template '{$templateId}' not found");
        }
        
        $subject = $this->processTemplate($template['subject'], $templateData);
        $body = $this->processTemplate($template['body'], $templateData);
        
        return $this->sendEmail($to, $subject, $body, true, $provider);
    }
    
    private function getEmailTemplate($templateId) {
        $templates = [
            'transfer_created' => [
                'subject' => 'تأكيد إنشاء التحويل - رمز التحويل: {{transfer_code}}',
                'body' => $this->getTransferCreatedTemplate()
            ],
            'transfer_completed' => [
                'subject' => 'تم إكمال التحويل - رمز التحويل: {{transfer_code}}',
                'body' => $this->getTransferCompletedTemplate()
            ],
            'otp_verification' => [
                'subject' => 'رمز التحقق - Elite Transfer System',
                'body' => $this->getOTPTemplate()
            ],
            'welcome' => [
                'subject' => 'أهلاً بك في Elite Transfer System',
                'body' => $this->getWelcomeTemplate()
            ]
        ];
        
        return $templates[$templateId] ?? null;
    }
    
    private function processTemplate($template, $data) {
        foreach ($data as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        return $template;
    }
    
    private function getTransferCreatedTemplate() {
        return '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { padding: 30px; background: #f8f9fa; }
                .footer { padding: 20px; text-align: center; color: #666; background: #e9ecef; border-radius: 0 0 10px 10px; }
                .highlight { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
                .button { display: inline-block; padding: 12px 30px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Elite Transfer System</h1>
                    <h2>تأكيد إنشاء التحويل</h2>
                </div>
                <div class="content">
                    <p>عزيزي {{sender_name}},</p>
                    <p>تم إنشاء تحويلك بنجاح. إليك تفاصيل التحويل:</p>
                    
                    <div class="highlight">
                        <h3>معلومات التحويل</h3>
                        <p><strong>رمز التحويل:</strong> {{transfer_code}}</p>
                        <p><strong>رمز الاستلام:</strong> {{pickup_code}}</p>
                        <p><strong>المبلغ المرسل:</strong> {{amount}} {{sender_currency}}</p>
                        <p><strong>المبلغ المستلم:</strong> {{converted_amount}} {{receiver_currency}}</p>
                        <p><strong>المستلم:</strong> {{receiver_name}}</p>
                        <p><strong>الحالة:</strong> {{status}}</p>
                        <p><strong>تاريخ الإنشاء:</strong> {{created_at}}</p>
                    </div>
                    
                    <p>يمكنك تتبع التحويل في أي وقت باستخدام رمز التحويل.</p>
                    
                    <div style="text-align: center;">
                        <a href="{{track_url}}" class="button">تتبع التحويل</a>
                    </div>
                    
                    <p>شكراً لاختيارك Elite Transfer System.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2025 Elite Transfer System. جميع الحقوق محفوظة.</p>
                    <p>للدعم: <EMAIL> | +966112345678</p>
                </div>
            </div>
        </body>
        </html>';
    }
    
    private function getTransferCompletedTemplate() {
        return '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { padding: 30px; background: #f8f9fa; }
                .footer { padding: 20px; text-align: center; color: #666; background: #e9ecef; border-radius: 0 0 10px 10px; }
                .success { background: #d1fae5; padding: 20px; border-radius: 8px; margin: 20px 0; border-right: 4px solid #10b981; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 تم إكمال التحويل بنجاح</h1>
                </div>
                <div class="content">
                    <div class="success">
                        <h3>تهانينا! تم تسليم الأموال بنجاح</h3>
                        <p><strong>رمز التحويل:</strong> {{transfer_code}}</p>
                        <p><strong>المبلغ المسلم:</strong> {{converted_amount}} {{receiver_currency}}</p>
                        <p><strong>تاريخ التسليم:</strong> {{completed_at}}</p>
                    </div>
                    
                    <p>عزيزي {{sender_name}},</p>
                    <p>نسعد بإعلامك أن تحويلك قد تم تسليمه بنجاح إلى {{receiver_name}}.</p>
                    
                    <p>شكراً لثقتك في Elite Transfer System.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2025 Elite Transfer System. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </body>
        </html>';
    }
    
    private function getOTPTemplate() {
        return '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { padding: 30px; background: #f8f9fa; text-align: center; }
                .otp-code { font-size: 2rem; font-weight: bold; color: #2563eb; background: white; padding: 20px; border-radius: 10px; margin: 20px 0; letter-spacing: 5px; }
                .warning { background: #fef3c7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>رمز التحقق</h1>
                </div>
                <div class="content">
                    <p>رمز التحقق الخاص بك:</p>
                    <div class="otp-code">{{otp_code}}</div>
                    <p>هذا الرمز صالح لمدة 10 دقائق فقط.</p>
                    
                    <div class="warning">
                        <strong>تحذير:</strong> لا تشارك هذا الرمز مع أي شخص آخر.
                    </div>
                </div>
            </div>
        </body>
        </html>';
    }
    
    private function getWelcomeTemplate() {
        return '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { padding: 30px; background: #f8f9fa; }
                .features { display: flex; justify-content: space-around; margin: 20px 0; }
                .feature { text-align: center; padding: 20px; }
                .button { display: inline-block; padding: 12px 30px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; margin: 10px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>أهلاً بك في Elite Transfer System</h1>
                </div>
                <div class="content">
                    <p>عزيزي {{user_name}},</p>
                    <p>مرحباً بك في Elite Transfer System - أسرع وأأمن طريقة لتحويل الأموال عبر العالم.</p>
                    
                    <div class="features">
                        <div class="feature">
                            <h3>🚀 سرعة فائقة</h3>
                            <p>تحويلات فورية</p>
                        </div>
                        <div class="feature">
                            <h3>🔒 أمان عالي</h3>
                            <p>حماية متقدمة</p>
                        </div>
                        <div class="feature">
                            <h3>💰 رسوم منخفضة</h3>
                            <p>أفضل الأسعار</p>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <a href="{{dashboard_url}}" class="button">ابدأ الآن</a>
                    </div>
                </div>
            </div>
        </body>
        </html>';
    }
}

// SendGrid Email Provider
class SendGridEmailProvider {
    private $apiKey;
    private $fromEmail;
    private $fromName;
    
    public function __construct($config) {
        $this->apiKey = $config['api_key'];
        $this->fromEmail = $config['from_email'];
        $this->fromName = $config['from_name'];
    }
    
    public function send($to, $subject, $body, $isHtml = true) {
        if (empty($this->apiKey)) {
            throw new Exception("SendGrid API key not configured");
        }
        
        $url = 'https://api.sendgrid.com/v3/mail/send';
        
        $data = [
            'personalizations' => [[
                'to' => [['email' => $to]],
                'subject' => $subject
            ]],
            'from' => [
                'email' => $this->fromEmail,
                'name' => $this->fromName
            ],
            'content' => [[
                'type' => $isHtml ? 'text/html' : 'text/plain',
                'value' => $body
            ]]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 202) {
            throw new Exception("SendGrid email failed: " . $response);
        }
        
        return [
            'success' => true,
            'message_id' => 'sendgrid_' . uniqid(),
            'provider' => 'sendgrid',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
}

// AWS SES Provider
class AWSSESProvider {
    private $accessKey;
    private $secretKey;
    private $region;
    private $fromEmail;
    
    public function __construct($config) {
        $this->accessKey = $config['access_key'];
        $this->secretKey = $config['secret_key'];
        $this->region = $config['region'];
        $this->fromEmail = $config['from_email'];
    }
    
    public function send($to, $subject, $body, $isHtml = true) {
        if (empty($this->accessKey) || empty($this->secretKey)) {
            throw new Exception("AWS credentials not configured");
        }
        
        // AWS SES implementation would go here
        // For now, simulate the call
        
        return [
            'success' => true,
            'message_id' => 'aws_ses_' . uniqid(),
            'provider' => 'aws_ses',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
}

// SMTP Email Provider
class SMTPEmailProvider {
    private $config;
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    public function send($to, $subject, $body, $isHtml = true) {
        // SMTP implementation would use PHPMailer or similar
        // For now, simulate the call
        
        return [
            'success' => true,
            'message_id' => 'smtp_' . uniqid(),
            'provider' => 'smtp',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
}

// Local Email Provider (for testing)
class LocalEmailProvider {
    public function send($to, $subject, $body, $isHtml = true) {
        // Log email to file for testing
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'to' => $to,
            'subject' => $subject,
            'body' => $body,
            'is_html' => $isHtml,
            'provider' => 'local'
        ];
        
        $logFile = __DIR__ . '/../../../storage/logs/email.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND);
        
        return [
            'success' => true,
            'message_id' => 'local_' . uniqid(),
            'provider' => 'local',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
}
