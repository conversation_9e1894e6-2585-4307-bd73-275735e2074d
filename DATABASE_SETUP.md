# 🗄️ دليل إعداد قاعدة البيانات - Elite Transfer System

## 📋 نظرة عامة

يدعم نظام Elite Transfer نوعين من قواعد البيانات:
- **MySQL** (الخيار المفضل للإنتاج)
- **SQLite** (خيار احتياطي للتطوير)

## 🚀 الإعداد السريع (SQLite)

إذا كنت تريد البدء بسرعة، استخدم SQLite:

```bash
php setup_sqlite.php
php -S localhost:8000 -t public
```

## 🏗️ إعداد MySQL (الموصى به للإنتاج)

### المتطلبات الأساسية

1. **XAMPP/WAMP/MAMP** أو **MySQL Server** مثبت ويعمل
2. **phpMyAdmin** (اختياري لإدارة قاعدة البيانات)
3. **PHP 8.2+** مع امتداد PDO MySQL

### خطوات الإعداد

#### 1. تشغيل خادم MySQL

**XAMPP:**
```bash
# تشغيل XAMPP Control Panel
# تفعيل Apache و MySQL
```

**خادم MySQL منفصل:**
```bash
# Windows
net start mysql

# Linux/Mac
sudo systemctl start mysql
# أو
sudo service mysql start
```

#### 2. إنشاء قاعدة البيانات والمستخدم

**الطريقة الأولى: استخدام phpMyAdmin**

1. افتح phpMyAdmin: `http://localhost/phpmyadmin`
2. سجل دخول كـ root (بدون كلمة مرور عادة في XAMPP)
3. انقر على "قواعد البيانات" → "إنشاء قاعدة بيانات جديدة"
4. اسم قاعدة البيانات: `elite_transfer`
5. الترميز: `utf8mb4_unicode_ci`
6. انقر "إنشاء"

**إنشاء مستخدم جديد:**
1. انتقل إلى "حسابات المستخدمين"
2. انقر "إضافة حساب مستخدم"
3. اسم المستخدم: `elite_user`
4. كلمة المرور: `elite_password_2025`
5. منح جميع الصلاحيات على قاعدة البيانات `elite_transfer`

**الطريقة الثانية: استخدام سطر الأوامر**

```sql
-- الاتصال بـ MySQL كـ root
mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE elite_transfer CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء المستخدم
CREATE USER 'elite_user'@'localhost' IDENTIFIED BY 'elite_password_2025';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON elite_transfer.* TO 'elite_user'@'localhost';
FLUSH PRIVILEGES;

-- الخروج
EXIT;
```

#### 3. تكوين ملف البيئة

تأكد من أن ملف `.env` يحتوي على:

```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=elite_transfer
DB_USERNAME=elite_user
DB_PASSWORD=elite_password_2025
DB_CHARSET=utf8mb4
```

#### 4. تشغيل سكريبت إنشاء الجداول

```bash
# إنشاء الجداول والبيانات الأولية
php create_tables.php
```

#### 5. تشغيل النظام

```bash
php -S localhost:8000 -t public
```

## 🔧 استكشاف الأخطاء وإصلاحها

### خطأ: "Connection refused"

**السبب:** خادم MySQL غير مشغل

**الحل:**
```bash
# تحقق من حالة MySQL
# Windows (XAMPP)
# تشغيل XAMPP Control Panel وتفعيل MySQL

# Linux
sudo systemctl status mysql
sudo systemctl start mysql

# Mac
brew services start mysql
```

### خطأ: "Access denied for user"

**السبب:** بيانات اعتماد خاطئة

**الحل:**
1. تحقق من اسم المستخدم وكلمة المرور في `.env`
2. تأكد من إنشاء المستخدم بالصلاحيات الصحيحة
3. جرب الاتصال يدوياً:

```bash
mysql -u elite_user -p elite_transfer
```

### خطأ: "Unknown database"

**السبب:** قاعدة البيانات غير موجودة

**الحل:**
```sql
mysql -u root -p
CREATE DATABASE elite_transfer CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### خطأ: "Table doesn't exist"

**السبب:** الجداول لم يتم إنشاؤها

**الحل:**
```bash
php create_tables.php
```

## 📊 إدارة قاعدة البيانات

### الوصول إلى phpMyAdmin

```
URL: http://localhost/phpmyadmin
Username: elite_user
Password: elite_password_2025
Database: elite_transfer
```

### النسخ الاحتياطي

**تصدير قاعدة البيانات:**
```bash
mysqldump -u elite_user -p elite_transfer > backup_$(date +%Y%m%d).sql
```

**استيراد قاعدة البيانات:**
```bash
mysql -u elite_user -p elite_transfer < backup_20250115.sql
```

### مراقبة الأداء

**عرض الجداول وأحجامها:**
```sql
SELECT 
    TABLE_NAME as 'Table',
    TABLE_ROWS as 'Rows',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'elite_transfer'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;
```

**تحسين الجداول:**
```sql
OPTIMIZE TABLE users, transfers, payments, notifications;
```

## 🔄 التبديل بين قواعد البيانات

النظام يدعم التبديل التلقائي:

1. **MySQL متاح:** يستخدم MySQL تلقائياً
2. **MySQL غير متاح:** يتحول إلى SQLite كخيار احتياطي

لفرض استخدام SQLite:
```env
DB_CONNECTION=sqlite
```

## 📈 تحسين الأداء

### فهرسة الجداول

```sql
-- فهارس إضافية لتحسين الأداء
CREATE INDEX idx_transfers_status_date ON transfers(status, created_at);
CREATE INDEX idx_transfers_sender_country ON transfers(sender_country_id, created_at);
CREATE INDEX idx_payments_method_status ON payments(payment_method, status);
CREATE INDEX idx_notifications_user_type ON notifications(user_id, type, status);
```

### إعدادات MySQL المحسنة

في ملف `my.cnf` أو `my.ini`:

```ini
[mysqld]
# تحسين الذاكرة
innodb_buffer_pool_size = 256M
key_buffer_size = 64M
max_connections = 200

# تحسين الاستعلامات
query_cache_type = 1
query_cache_size = 64M

# تحسين السجلات
slow_query_log = 1
long_query_time = 2
```

## 🛡️ الأمان

### تأمين قاعدة البيانات

1. **تغيير كلمات المرور الافتراضية:**
```sql
ALTER USER 'elite_user'@'localhost' IDENTIFIED BY 'new_secure_password_2025';
```

2. **إزالة المستخدمين غير المستخدمين:**
```sql
DROP USER 'test'@'localhost';
DROP USER ''@'localhost';
```

3. **تقييد الوصول:**
```sql
-- السماح بالوصول من IP محدد فقط
CREATE USER 'elite_user'@'*************' IDENTIFIED BY 'password';
```

### تشفير البيانات الحساسة

النظام يشفر تلقائياً:
- كلمات المرور (bcrypt)
- البيانات الشخصية الحساسة
- معلومات الدفع

## 📞 الدعم

إذا واجهت مشاكل:

1. **تحقق من سجلات الأخطاء:**
   - `storage/logs/`
   - MySQL error log
   - PHP error log

2. **اختبر الاتصال:**
```bash
php -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=elite_transfer', 'elite_user', 'elite_password_2025');
    echo 'Connection successful!';
} catch (Exception \$e) {
    echo 'Connection failed: ' . \$e->getMessage();
}
"
```

3. **تشغيل اختبار النظام:**
```bash
php simple_test.php
```

## 🎯 الخلاصة

- ✅ **للتطوير السريع:** استخدم SQLite (`php setup_sqlite.php`)
- ✅ **للإنتاج:** استخدم MySQL مع الخطوات المذكورة أعلاه
- ✅ **للنسخ الاحتياطي:** استخدم mysqldump بانتظام
- ✅ **للمراقبة:** استخدم phpMyAdmin أو أدوات المراقبة المتقدمة

النظام الآن جاهز للعمل مع قاعدة بيانات محسنة وآمنة! 🚀
