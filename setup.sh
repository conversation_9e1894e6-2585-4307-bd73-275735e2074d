#!/bin/bash

echo "========================================"
echo "Elite Financial Transfer System v6.0"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PHP is installed
print_status "[1/8] Checking PHP version..."
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed or not in PATH"
    exit 1
fi

PHP_VERSION=$(php -r "echo PHP_VERSION;")
print_success "PHP version: $PHP_VERSION"

# Check if Composer is installed
print_status "[2/8] Checking Composer..."
if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed or not in PATH"
    exit 1
fi

COMPOSER_VERSION=$(composer --version)
print_success "$COMPOSER_VERSION"

# Install PHP dependencies
print_status "[3/8] Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader
if [ $? -ne 0 ]; then
    print_error "Failed to install PHP dependencies"
    exit 1
fi
print_success "PHP dependencies installed"

# Set up environment file
print_status "[4/8] Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    print_success "Environment file created"
else
    print_warning "Environment file already exists"
fi

# Generate application key
print_status "[5/8] Generating application key..."
php artisan key:generate --force
print_success "Application key generated"

# Set up database
print_status "[6/8] Setting up database..."
if [ ! -f database/database.sqlite ]; then
    touch database/database.sqlite
    print_success "SQLite database file created"
else
    print_warning "Database file already exists"
fi

# Run migrations
print_status "[7/8] Running database migrations..."
php artisan migrate --force
if [ $? -ne 0 ]; then
    print_error "Database migration failed"
    exit 1
fi
print_success "Database migrations completed"

# Seed database
print_status "[8/8] Seeding database with sample data..."
php artisan db:seed --force
if [ $? -ne 0 ]; then
    print_warning "Database seeding failed, but system can still work"
else
    print_success "Database seeded with sample data"
fi

echo
echo "========================================"
echo -e "${GREEN}Setup completed successfully!${NC}"
echo "========================================"
echo
echo "Default login credentials:"
echo
echo -e "${BLUE}Super Admin:${NC}"
echo "  Email: <EMAIL>"
echo "  Password: password123"
echo
echo -e "${BLUE}Customer:${NC}"
echo "  Email: <EMAIL>"
echo "  Password: password123"
echo
echo -e "${BLUE}Agent:${NC}"
echo "  Email: <EMAIL>"
echo "  Password: password123"
echo
echo "========================================"
echo -e "${YELLOW}To start the application, run:${NC}"
echo "  php artisan serve"
echo
echo -e "${YELLOW}Then open your browser and go to:${NC}"
echo "  http://localhost:8000"
echo "========================================"
echo

# Make the script executable
chmod +x setup.sh
