<?php
// Check if user is logged in and has permission
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

if ($_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'agent') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مستخدم';
$userRole = $_SESSION['user_role'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .tab-content {
            padding: 20px 0;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-graph-up me-2"></i>
                            التقارير والإحصائيات
                        </h2>
                        <p class="text-white-50 mb-0">
                            تقارير شاملة وتحليلات متقدمة للتحويلات المالية
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-outline-light me-2" onclick="exportReport('pdf')">
                            <i class="bi bi-file-pdf me-2"></i>
                            تصدير PDF
                        </button>
                        <button class="btn btn-outline-light" onclick="exportReport('excel')">
                            <i class="bi bi-file-excel me-2"></i>
                            تصدير Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4" id="summaryCards">
            <!-- Cards will be populated by JavaScript -->
        </div>

        <!-- Report Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="reportTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                    <i class="bi bi-pie-chart me-2"></i>نظرة عامة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="transfers-tab" data-bs-toggle="tab" data-bs-target="#transfers" type="button" role="tab">
                                    <i class="bi bi-list-ul me-2"></i>تقرير التحويلات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab">
                                    <i class="bi bi-currency-dollar me-2"></i>التقرير المالي
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="countries-tab" data-bs-toggle="tab" data-bs-target="#countries" type="button" role="tab">
                                    <i class="bi bi-globe me-2"></i>إحصائيات الدول
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="reportTabContent">
                            <!-- Overview Tab -->
                            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <h5>نشاط التحويلات (آخر 30 يوم)</h5>
                                        <div class="chart-container">
                                            <canvas id="transfersChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <h5>توزيع حالات التحويل</h5>
                                        <div class="chart-container">
                                            <canvas id="statusChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Transfers Tab -->
                            <div class="tab-pane fade" id="transfers" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="dateFrom" class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="dateFrom">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateTo" class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="dateTo">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="statusFilter" class="form-label">الحالة</label>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">جميع الحالات</option>
                                            <option value="pending">معلق</option>
                                            <option value="processing">قيد المعالجة</option>
                                            <option value="ready_for_pickup">جاهز للاستلام</option>
                                            <option value="completed">مكتمل</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary d-block w-100" onclick="loadTransfersReport()">
                                            <i class="bi bi-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-striped" id="transfersTable">
                                        <thead>
                                            <tr>
                                                <th>رمز التحويل</th>
                                                <th>المرسل</th>
                                                <th>المستلم</th>
                                                <th>من/إلى</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Financial Tab -->
                            <div class="tab-pane fade" id="financial" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>الملخص المالي</h5>
                                        <div id="financialSummary">
                                            <!-- Will be populated by JavaScript -->
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>الإيرادات الشهرية</h5>
                                        <div class="chart-container">
                                            <canvas id="revenueChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Countries Tab -->
                            <div class="tab-pane fade" id="countries" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="table-responsive">
                                            <table class="table table-striped" id="countriesTable">
                                                <thead>
                                                    <tr>
                                                        <th>الدولة</th>
                                                        <th>التحويلات المرسلة</th>
                                                        <th>التحويلات المستلمة</th>
                                                        <th>المبلغ المرسل</th>
                                                        <th>المبلغ المستلم</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be populated by JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h5>أكثر الدول نشاطاً</h5>
                                        <div class="chart-container">
                                            <canvas id="countriesChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script>
        // Initialize reports
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardStats();
            loadOverviewCharts();
            
            // Set default date range (last 30 days)
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
            
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
        });
        
        function loadDashboardStats() {
            fetch('/api/dashboard-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSummaryCards(data.data);
                    }
                })
                .catch(error => console.error('Error loading stats:', error));
        }
        
        function updateSummaryCards(stats) {
            const role = '<?= $userRole ?>';
            let cards = '';
            
            if (role === 'admin') {
                cards = `
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-primary">
                            <i class="bi bi-send fs-1 mb-3"></i>
                            <h3>${stats.total_transfers || 0}</h3>
                            <p class="mb-0">إجمالي التحويلات</p>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-success">
                            <i class="bi bi-calendar-day fs-1 mb-3"></i>
                            <h3>${stats.today_transfers || 0}</h3>
                            <p class="mb-0">تحويلات اليوم</p>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-warning">
                            <i class="bi bi-currency-dollar fs-1 mb-3"></i>
                            <h3>$${stats.total_amount || 0}</h3>
                            <p class="mb-0">إجمالي المبلغ</p>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-info">
                            <i class="bi bi-graph-up fs-1 mb-3"></i>
                            <h3>$${stats.monthly_revenue || 0}</h3>
                            <p class="mb-0">الإيرادات الشهرية</p>
                        </div>
                    </div>
                `;
            } else {
                cards = `
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="stats-card bg-primary">
                            <i class="bi bi-send fs-1 mb-3"></i>
                            <h3>${stats.total_transfers || 0}</h3>
                            <p class="mb-0">تحويلاتي</p>
                        </div>
                    </div>
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="stats-card bg-success">
                            <i class="bi bi-check-circle fs-1 mb-3"></i>
                            <h3>${stats.completed_transfers || 0}</h3>
                            <p class="mb-0">مكتملة</p>
                        </div>
                    </div>
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="stats-card bg-info">
                            <i class="bi bi-wallet fs-1 mb-3"></i>
                            <h3>$${stats.monthly_commission || 0}</h3>
                            <p class="mb-0">العمولة الشهرية</p>
                        </div>
                    </div>
                `;
            }
            
            document.getElementById('summaryCards').innerHTML = cards;
        }
        
        function loadOverviewCharts() {
            // Load transfers chart
            fetch('/api/chart-data?period=30days')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        createTransfersChart(data.data);
                    }
                })
                .catch(error => console.error('Error loading chart data:', error));
            
            // Create status chart with sample data
            createStatusChart();
        }
        
        function createTransfersChart(chartData) {
            const ctx = document.getElementById('transfersChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.map(item => item.label),
                    datasets: [{
                        label: 'عدد التحويلات',
                        data: chartData.map(item => item.count),
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        }
        
        function createStatusChart() {
            const ctx = document.getElementById('statusChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مكتمل', 'قيد المعالجة', 'معلق', 'جاهز للاستلام'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: [
                            '#10b981',
                            '#3b82f6',
                            '#f59e0b',
                            '#06b6d4'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function loadTransfersReport() {
            // This would load transfer data based on filters
            console.log('Loading transfers report...');
        }
        
        function exportReport(format) {
            alert(`تصدير التقرير بصيغة ${format} - قريباً!`);
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
