<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع التحويل - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .timeline-line {
            width: 2px;
            height: 60px;
            background-color: #dee2e6;
            margin: 5px auto 0;
        }

        .timeline-content {
            padding-bottom: 30px;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-processing { background: #dbeafe; color: #1e40af; }
        .status-ready-for-pickup { background: #0dcaf0; color: white; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/">
                    <i class="bi bi-house me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link text-white" href="/login">
                    <i class="bi bi-box-arrow-in-right me-1"></i>
                    تسجيل الدخول
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h2 class="text-white mb-3">
                        <i class="bi bi-search me-2"></i>
                        تتبع التحويل
                    </h2>
                    <p class="text-white-50">أدخل رمز التحويل لتتبع حالة تحويل الأموال</p>
                </div>

                <div class="card">
                    <div class="card-body p-5">
                        <form id="trackingForm">
                            <div class="mb-4">
                                <label for="transfer_code" class="form-label">
                                    <i class="bi bi-qr-code me-1"></i>
                                    رمز التحويل
                                </label>
                                <input type="text" 
                                       class="form-control form-control-lg text-center" 
                                       id="transfer_code" 
                                       name="transfer_code" 
                                       placeholder="TRF20250125ABC123"
                                       style="font-size: 1.2rem; letter-spacing: 2px;"
                                       required>
                                <div class="form-text">
                                    أدخل رمز التحويل المقدم عند إرسال الأموال
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-search me-2"></i>
                                    تتبع التحويل
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Tracking Results -->
                <div id="trackingResults" class="d-none mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                معلومات التحويل
                            </h5>
                        </div>
                        <div class="card-body" id="transferInfo">
                            <!-- Transfer info will be populated here -->
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-clock-history me-2"></i>
                                الخط الزمني للتحويل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="transferTimeline">
                                <!-- Timeline will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-question-circle me-2"></i>
                            تحتاج مساعدة؟
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>أين تجد رمز التحويل؟</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check text-success me-2"></i>رسالة التأكيد بالبريد الإلكتروني</li>
                                    <li><i class="bi bi-check text-success me-2"></i>رسالة نصية</li>
                                    <li><i class="bi bi-check text-success me-2"></i>إيصال من الوكيل</li>
                                    <li><i class="bi bi-check text-success me-2"></i>لوحة تحكم حسابك</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>معاني حالات التحويل</h6>
                                <ul class="list-unstyled">
                                    <li><span class="status-badge status-pending me-2">معلق</span>قيد المعالجة</li>
                                    <li><span class="status-badge status-processing me-2">قيد المعالجة</span>في التنفيذ</li>
                                    <li><span class="status-badge status-ready-for-pickup me-2">جاهز</span>جاهز للاستلام</li>
                                    <li><span class="status-badge status-completed me-2">مكتمل</span>تم التسليم بنجاح</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <p class="mb-2">لا تزال تحتاج مساعدة؟ تواصل مع فريق الدعم</p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="tel:+966112345678" class="btn btn-outline-primary">
                                    <i class="bi bi-telephone me-2"></i>
                                    اتصل بالدعم
                                </a>
                                <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                                    <i class="bi bi-envelope me-2"></i>
                                    راسل الدعم
                                </a>
                                <button class="btn btn-outline-primary" onclick="openLiveChat()">
                                    <i class="bi bi-chat-dots me-2"></i>
                                    دردشة مباشرة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Sample transfer data for demo
        const sampleTransfers = {
            'TRF20250125001': {
                transfer_code: 'TRF20250125001',
                status: 'completed',
                amount: '1000.00',
                converted_amount: '3750.00',
                sender_currency: 'USD',
                receiver_currency: 'SAR',
                sender_country: 'الولايات المتحدة',
                receiver_country: 'السعودية',
                receiver_name: 'أحمد محمد',
                created_at: '2025-01-25T10:30:00',
                completed_at: '2025-01-25T11:00:00',
                pickup_code: 'PK123456',
                timeline: [
                    {
                        status: 'pending',
                        timestamp: '2025-01-25T10:30:00',
                        notes: 'تم إنشاء التحويل',
                        updated_by: 'النظام'
                    },
                    {
                        status: 'processing',
                        timestamp: '2025-01-25T10:35:00',
                        notes: 'تم التحقق من الهوية',
                        updated_by: 'فاطمة الزهراء'
                    },
                    {
                        status: 'ready_for_pickup',
                        timestamp: '2025-01-25T10:45:00',
                        notes: 'جاهز للاستلام',
                        updated_by: 'فاطمة الزهراء'
                    },
                    {
                        status: 'completed',
                        timestamp: '2025-01-25T11:00:00',
                        notes: 'تم التسليم بنجاح',
                        updated_by: 'فاطمة الزهراء'
                    }
                ]
            },
            'TRF20250125002': {
                transfer_code: 'TRF20250125002',
                status: 'processing',
                amount: '750.00',
                converted_amount: '2812.50',
                sender_currency: 'USD',
                receiver_currency: 'SAR',
                sender_country: 'الإمارات',
                receiver_country: 'مصر',
                receiver_name: 'فاطمة علي',
                created_at: '2025-01-25T12:15:00',
                pickup_code: 'PK789012',
                timeline: [
                    {
                        status: 'pending',
                        timestamp: '2025-01-25T12:15:00',
                        notes: 'تم إنشاء التحويل',
                        updated_by: 'النظام'
                    },
                    {
                        status: 'processing',
                        timestamp: '2025-01-25T12:20:00',
                        notes: 'قيد المراجعة',
                        updated_by: 'سارة المنصوري'
                    }
                ]
            }
        };

        document.getElementById('trackingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const transferCode = document.getElementById('transfer_code').value.toUpperCase();
            
            // Check if transfer exists in sample data
            if (sampleTransfers[transferCode]) {
                displayTrackingResults(sampleTransfers[transferCode]);
                document.getElementById('trackingResults').classList.remove('d-none');
                
                // Scroll to results
                document.getElementById('trackingResults').scrollIntoView({ 
                    behavior: 'smooth' 
                });
            } else {
                alert('التحويل غير موجود. جرب: TRF20250125001 أو TRF20250125002');
            }
        });

        function displayTrackingResults(data) {
            // Display transfer information
            const transferInfo = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>تفاصيل التحويل</h6>
                        <p><strong>رمز التحويل:</strong> ${data.transfer_code}</p>
                        <p><strong>الحالة:</strong> <span class="status-badge status-${data.status}">${getStatusText(data.status)}</span></p>
                        <p><strong>المبلغ المرسل:</strong> ${data.amount} ${data.sender_currency}</p>
                        <p><strong>المبلغ المستلم:</strong> ${data.converted_amount} ${data.receiver_currency}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${formatDate(data.created_at)}</p>
                        ${data.completed_at ? `<p><strong>تاريخ الإكمال:</strong> ${formatDate(data.completed_at)}</p>` : ''}
                    </div>
                    <div class="col-md-6">
                        <h6>مسار التحويل</h6>
                        <p><strong>من:</strong> ${data.sender_country}</p>
                        <p><strong>إلى:</strong> ${data.receiver_country}</p>
                        <p><strong>المستلم:</strong> ${data.receiver_name}</p>
                        
                        ${data.status === 'ready_for_pickup' || data.status === 'completed' ? `
                            <div class="alert alert-info mt-3">
                                <h6><i class="bi bi-info-circle me-2"></i>معلومات الاستلام</h6>
                                <p class="mb-1"><strong>رمز الاستلام:</strong> <code>${data.pickup_code}</code></p>
                                <p class="mb-0"><small>قدم هذا الرمز وهوية صالحة لاستلام الأموال</small></p>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            
            document.getElementById('transferInfo').innerHTML = transferInfo;
            
            // Display timeline
            const timeline = data.timeline.map((item, index) => {
                const isLast = index === data.timeline.length - 1;
                return `
                    <div class="d-flex">
                        <div class="flex-shrink-0">
                            <div class="timeline-icon ${isLast ? 'bg-primary' : 'bg-secondary'}">
                                <i class="bi bi-${getStatusIcon(item.status)} text-white"></i>
                            </div>
                            ${!isLast ? '<div class="timeline-line"></div>' : ''}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="timeline-content">
                                <h6 class="mb-1">${getStatusText(item.status)}</h6>
                                <p class="text-muted mb-1">${formatDate(item.timestamp)}</p>
                                ${item.notes ? `<p class="mb-0"><small>${item.notes}</small></p>` : ''}
                                ${item.updated_by ? `<p class="mb-0"><small>بواسطة: ${item.updated_by}</small></p>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('transferTimeline').innerHTML = timeline;
        }

        function getStatusIcon(status) {
            const icons = {
                'pending': 'clock',
                'processing': 'gear',
                'ready_for_pickup': 'check-circle',
                'completed': 'check-circle-fill',
                'cancelled': 'x-circle',
                'failed': 'exclamation-triangle'
            };
            return icons[status] || 'circle';
        }

        function getStatusText(status) {
            const statuses = {
                'pending': 'معلق',
                'processing': 'قيد المعالجة',
                'ready_for_pickup': 'جاهز للاستلام',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'failed': 'فشل'
            };
            return statuses[status] || status;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function openLiveChat() {
            alert('ميزة الدردشة المباشرة قريباً!');
        }

        // Auto-format transfer code input
        document.getElementById('transfer_code').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            e.target.value = value;
        });

        // Auto-fill sample transfer code for demo
        setTimeout(() => {
            document.getElementById('transfer_code').value = 'TRF20250125001';
        }, 2000);
    </script>
</body>
</html>
