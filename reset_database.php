<?php

echo "🔄 Resetting Production Database - Elite Transfer System\n\n";

try {
    // Connect to production database
    $db = new PDO('sqlite:database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to production database\n\n";
    
    // Clear all data
    echo "🗑️ Clearing existing data...\n";
    
    $tables = ['transfers', 'exchange_rates', 'system_settings', 'users', 'countries'];
    
    foreach ($tables as $table) {
        $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
        if ($count > 0) {
            $db->exec("DELETE FROM $table");
            echo "  Cleared $table: $count records\n";
        } else {
            echo "  $table: already empty\n";
        }
    }
    
    // Reset auto-increment counters
    echo "\n🔄 Resetting auto-increment counters...\n";
    foreach ($tables as $table) {
        $db->exec("DELETE FROM sqlite_sequence WHERE name='$table'");
        echo "  Reset $table counter\n";
    }
    
    echo "\n✅ Database reset completed!\n";
    echo "🔧 Ready for fresh data insertion\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
