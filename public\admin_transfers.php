<?php
// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مدير النظام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dbeafe; color: #1e40af; }
        .status-processing { background: #e0e7ff; color: #3730a3; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-cancelled { background: #fee2e2; color: #991b1b; }
        .status-refunded { background: #f3e8ff; color: #7c3aed; }
        
        .amount-display {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            background: #f8fafc;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .btn-action {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 8px;
            border: none;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/dashboard">
                <i class="bi bi-arrow-left-right me-2"></i>
                Elite Transfer - إدارة التحويلات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/admin/users">
                    <i class="bi bi-people me-1"></i>
                    المستخدمين
                </a>
                <a class="nav-link text-white" href="/admin/reports">
                    <i class="bi bi-graph-up me-1"></i>
                    التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            إدارة التحويلات
                        </h2>
                        <p class="text-white-50 mb-0">
                            مراجعة وإدارة جميع التحويلات المالية
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-outline-light me-2" onclick="exportTransfers()">
                            <i class="bi bi-download me-2"></i>
                            تصدير البيانات
                        </button>
                        <button class="btn btn-outline-light" onclick="refreshTransfers()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                                <i class="bi bi-arrow-left-right"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="totalTransfers">0</h5>
                                <p class="card-text text-muted mb-0">إجمالي التحويلات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #059669, #047857);">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="completedTransfers">0</h5>
                                <p class="card-text text-muted mb-0">التحويلات المكتملة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="pendingTransfers">0</h5>
                                <p class="card-text text-muted mb-0">في الانتظار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #7c3aed, #6d28d9);">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="totalVolume">$0</h5>
                                <p class="card-text text-muted mb-0">إجمالي المبلغ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة التحويل</label>
                                <select class="form-control" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="paid">مدفوع</option>
                                    <option value="processing">قيد المعالجة</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                    <option value="refunded">مسترد</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="fromDate">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="toDate">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary" onclick="applyFilters()">
                                        <i class="bi bi-funnel me-2"></i>تطبيق المرشحات
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="bi bi-x-circle me-2"></i>مسح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-table me-2"></i>
                            قائمة التحويلات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="transfersTable">
                                <thead>
                                    <tr>
                                        <th>رمز التحويل</th>
                                        <th>المرسل</th>
                                        <th>المستقبل</th>
                                        <th>المبلغ</th>
                                        <th>الرسوم</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="transfersTableBody">
                                    <!-- Transfers will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Details Modal -->
    <div class="modal fade" id="transferDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>
                        تفاصيل التحويل
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transferDetailsContent">
                    <!-- Transfer details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-success" onclick="approveTransfer()">
                        <i class="bi bi-check-lg me-2"></i>موافقة
                    </button>
                    <button type="button" class="btn btn-danger" onclick="rejectTransfer()">
                        <i class="bi bi-x-lg me-2"></i>رفض
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- DataTables -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadTransfers();
            initializeDataTable();
        });
        
        let transfersTable;
        let currentTransferId = null;
        
        function initializeDataTable() {
            transfersTable = $('#transfersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                pageLength: 10,
                order: [[6, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        }
        
        function loadTransfers() {
            // Mock data - replace with actual API call
            const mockTransfers = [
                {
                    id: 1,
                    transfer_code: 'ET2025001',
                    pickup_code: 'PK001',
                    sender_name: 'أحمد محمد',
                    sender_phone: '+966501234567',
                    receiver_name: 'محمد أحمد',
                    receiver_phone: '+201234567890',
                    amount: 1000.00,
                    fee_amount: 25.00,
                    total_amount: 1025.00,
                    sender_currency: 'SAR',
                    receiver_currency: 'EGP',
                    status: 'completed',
                    created_at: '2025-01-15 10:30:00'
                },
                {
                    id: 2,
                    transfer_code: 'ET2025002',
                    pickup_code: 'PK002',
                    sender_name: 'فاطمة علي',
                    sender_phone: '+966501234568',
                    receiver_name: 'علي فاطمة',
                    receiver_phone: '+971501234567',
                    amount: 500.00,
                    fee_amount: 15.00,
                    total_amount: 515.00,
                    sender_currency: 'SAR',
                    receiver_currency: 'AED',
                    status: 'pending',
                    created_at: '2025-01-15 14:20:00'
                },
                {
                    id: 3,
                    transfer_code: 'ET2025003',
                    pickup_code: 'PK003',
                    sender_name: 'خالد سعد',
                    sender_phone: '+966501234569',
                    receiver_name: 'سعد خالد',
                    receiver_phone: '+962791234567',
                    amount: 2000.00,
                    fee_amount: 50.00,
                    total_amount: 2050.00,
                    sender_currency: 'SAR',
                    receiver_currency: 'JOD',
                    status: 'processing',
                    created_at: '2025-01-15 16:45:00'
                }
            ];
            
            displayTransfers(mockTransfers);
            updateStatistics(mockTransfers);
        }
        
        function displayTransfers(transfers) {
            const tbody = document.getElementById('transfersTableBody');
            tbody.innerHTML = '';
            
            transfers.forEach(transfer => {
                const row = `
                    <tr>
                        <td>
                            <span class="transfer-code">${transfer.transfer_code}</span>
                        </td>
                        <td>
                            <div>
                                <strong>${transfer.sender_name}</strong><br>
                                <small class="text-muted">${transfer.sender_phone}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${transfer.receiver_name}</strong><br>
                                <small class="text-muted">${transfer.receiver_phone}</small>
                            </div>
                        </td>
                        <td>
                            <div class="amount-display">
                                ${transfer.amount.toFixed(2)} ${transfer.sender_currency}
                            </div>
                            <small class="text-muted">
                                → ${(transfer.amount * getExchangeRate(transfer.sender_currency, transfer.receiver_currency)).toFixed(2)} ${transfer.receiver_currency}
                            </small>
                        </td>
                        <td>
                            <span class="text-warning">
                                ${transfer.fee_amount.toFixed(2)} ${transfer.sender_currency}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${transfer.status}">
                                ${getStatusText(transfer.status)}
                            </span>
                        </td>
                        <td>${formatDateTime(transfer.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-info btn-action" onclick="viewTransfer(${transfer.id})" title="عرض التفاصيل">
                                <i class="bi bi-eye"></i>
                            </button>
                            ${transfer.status === 'pending' ? `
                            <button class="btn btn-sm btn-success btn-action" onclick="approveTransferQuick(${transfer.id})" title="موافقة">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-sm btn-danger btn-action" onclick="rejectTransferQuick(${transfer.id})" title="رفض">
                                <i class="bi bi-x"></i>
                            </button>
                            ` : ''}
                            <button class="btn btn-sm btn-warning btn-action" onclick="editTransfer(${transfer.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
            
            // Refresh DataTable
            if (transfersTable) {
                transfersTable.destroy();
                initializeDataTable();
            }
        }
        
        function updateStatistics(transfers) {
            document.getElementById('totalTransfers').textContent = transfers.length;
            document.getElementById('completedTransfers').textContent = transfers.filter(t => t.status === 'completed').length;
            document.getElementById('pendingTransfers').textContent = transfers.filter(t => t.status === 'pending').length;
            
            const totalVolume = transfers.reduce((sum, t) => sum + t.amount, 0);
            document.getElementById('totalVolume').textContent = '$' + totalVolume.toLocaleString();
        }
        
        function getStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'paid': 'مدفوع',
                'processing': 'قيد المعالجة',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'refunded': 'مسترد'
            };
            return statuses[status] || status;
        }
        
        function getExchangeRate(from, to) {
            // Mock exchange rates
            const rates = {
                'SAR_EGP': 8.24,
                'SAR_AED': 0.98,
                'SAR_JOD': 0.19,
                'SAR_USD': 0.27
            };
            return rates[`${from}_${to}`] || 1;
        }
        
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
        }
        
        function viewTransfer(transferId) {
            currentTransferId = transferId;
            
            // Mock transfer details
            const transferDetails = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات المرسل</h6>
                        <p><strong>الاسم:</strong> أحمد محمد</p>
                        <p><strong>الهاتف:</strong> +966501234567</p>
                        <p><strong>البلد:</strong> السعودية</p>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات المستقبل</h6>
                        <p><strong>الاسم:</strong> محمد أحمد</p>
                        <p><strong>الهاتف:</strong> +201234567890</p>
                        <p><strong>البلد:</strong> مصر</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-4">
                        <h6>تفاصيل المبلغ</h6>
                        <p><strong>المبلغ المرسل:</strong> 1,000.00 ريال</p>
                        <p><strong>الرسوم:</strong> 25.00 ريال</p>
                        <p><strong>المجموع:</strong> 1,025.00 ريال</p>
                    </div>
                    <div class="col-md-4">
                        <h6>المبلغ المستلم</h6>
                        <p><strong>سعر الصرف:</strong> 8.24</p>
                        <p><strong>المبلغ المستلم:</strong> 8,240.00 جنيه</p>
                    </div>
                    <div class="col-md-4">
                        <h6>معلومات إضافية</h6>
                        <p><strong>رمز التحويل:</strong> ET2025001</p>
                        <p><strong>رمز الاستلام:</strong> PK001</p>
                        <p><strong>التاريخ:</strong> 2025-01-15</p>
                    </div>
                </div>
            `;
            
            document.getElementById('transferDetailsContent').innerHTML = transferDetails;
            $('#transferDetailsModal').modal('show');
        }
        
        function approveTransfer() {
            if (currentTransferId) {
                alert(`تم الموافقة على التحويل رقم: ${currentTransferId}`);
                $('#transferDetailsModal').modal('hide');
                loadTransfers();
            }
        }
        
        function rejectTransfer() {
            if (currentTransferId) {
                const reason = prompt('سبب الرفض:');
                if (reason) {
                    alert(`تم رفض التحويل رقم: ${currentTransferId}\nالسبب: ${reason}`);
                    $('#transferDetailsModal').modal('hide');
                    loadTransfers();
                }
            }
        }
        
        function approveTransferQuick(transferId) {
            if (confirm('هل أنت متأكد من الموافقة على هذا التحويل؟')) {
                alert(`تم الموافقة على التحويل رقم: ${transferId}`);
                loadTransfers();
            }
        }
        
        function rejectTransferQuick(transferId) {
            const reason = prompt('سبب الرفض:');
            if (reason) {
                alert(`تم رفض التحويل رقم: ${transferId}\nالسبب: ${reason}`);
                loadTransfers();
            }
        }
        
        function editTransfer(transferId) {
            alert(`تعديل التحويل رقم: ${transferId}`);
        }
        
        function applyFilters() {
            const status = document.getElementById('statusFilter').value;
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            
            alert(`تطبيق المرشحات:\nالحالة: ${status || 'الكل'}\nمن: ${fromDate || 'غير محدد'}\nإلى: ${toDate || 'غير محدد'}`);
            loadTransfers();
        }
        
        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('fromDate').value = '';
            document.getElementById('toDate').value = '';
            loadTransfers();
        }
        
        function exportTransfers() {
            alert('تصدير البيانات...');
        }
        
        function refreshTransfers() {
            loadTransfers();
            alert('تم تحديث قائمة التحويلات');
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
