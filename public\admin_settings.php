<?php
// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مدير النظام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .settings-nav {
            background: #f8fafc;
            border-radius: 10px;
            padding: 1rem;
        }
        
        .settings-nav .nav-link {
            color: #6b7280;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .settings-nav .nav-link.active {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
        }
        
        .settings-nav .nav-link:hover {
            background: #e5e7eb;
            color: #374151;
        }
        
        .settings-nav .nav-link.active:hover {
            background: linear-gradient(135deg, #b91c1c, #991b1b);
            color: white;
        }
        
        .form-control:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #b91c1c, #991b1b);
        }
        
        .setting-item {
            border-bottom: 1px solid #e5e7eb;
            padding: 1.5rem 0;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .currency-rate {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-active { background: #059669; }
        .status-inactive { background: #dc2626; }
        .status-warning { background: #f59e0b; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/dashboard">
                <i class="bi bi-gear me-2"></i>
                Elite Transfer - إعدادات النظام
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/admin/users">
                    <i class="bi bi-people me-1"></i>
                    المستخدمين
                </a>
                <a class="nav-link text-white" href="/admin/transfers">
                    <i class="bi bi-arrow-left-right me-1"></i>
                    التحويلات
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-gear me-2"></i>
                            إعدادات النظام
                        </h2>
                        <p class="text-white-50 mb-0">
                            إدارة وتكوين إعدادات النظام العامة
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-outline-light me-2" onclick="backupSettings()">
                            <i class="bi bi-download me-2"></i>
                            نسخ احتياطي
                        </button>
                        <button class="btn btn-outline-light" onclick="restoreSettings()">
                            <i class="bi bi-upload me-2"></i>
                            استعادة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Settings Navigation -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="settings-nav">
                            <div class="nav flex-column nav-pills">
                                <a class="nav-link active" data-bs-toggle="pill" href="#general-settings">
                                    <i class="bi bi-gear me-2"></i>الإعدادات العامة
                                </a>
                                <a class="nav-link" data-bs-toggle="pill" href="#exchange-rates">
                                    <i class="bi bi-currency-exchange me-2"></i>أسعار الصرف
                                </a>
                                <a class="nav-link" data-bs-toggle="pill" href="#fees-settings">
                                    <i class="bi bi-calculator me-2"></i>إعدادات الرسوم
                                </a>
                                <a class="nav-link" data-bs-toggle="pill" href="#security-settings">
                                    <i class="bi bi-shield-check me-2"></i>إعدادات الأمان
                                </a>
                                <a class="nav-link" data-bs-toggle="pill" href="#notification-settings">
                                    <i class="bi bi-bell me-2"></i>إعدادات الإشعارات
                                </a>
                                <a class="nav-link" data-bs-toggle="pill" href="#api-settings">
                                    <i class="bi bi-cloud me-2"></i>إعدادات API
                                </a>
                                <a class="nav-link" data-bs-toggle="pill" href="#compliance-settings">
                                    <i class="bi bi-file-text me-2"></i>إعدادات الامتثال
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Content -->
            <div class="col-lg-9">
                <div class="tab-content">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general-settings">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear me-2"></i>
                                    الإعدادات العامة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">اسم النظام</label>
                                            <input type="text" class="form-control" value="Elite Transfer System" id="appName">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">إصدار النظام</label>
                                            <input type="text" class="form-control" value="7.0" id="appVersion" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">الحد الأدنى للتحويل</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" value="1" id="minTransferAmount">
                                                <span class="input-group-text">USD</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">الحد الأقصى للتحويل</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" value="50000" id="maxTransferAmount">
                                                <span class="input-group-text">USD</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">العملة الافتراضية</label>
                                            <select class="form-control" id="defaultCurrency">
                                                <option value="USD">الدولار الأمريكي (USD)</option>
                                                <option value="SAR" selected>الريال السعودي (SAR)</option>
                                                <option value="AED">الدرهم الإماراتي (AED)</option>
                                                <option value="EUR">اليورو (EUR)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">المنطقة الزمنية</label>
                                            <select class="form-control" id="timezone">
                                                <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                                <option value="Asia/Dubai">دبي (GMT+4)</option>
                                                <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                                <option value="UTC">UTC (GMT+0)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button class="btn btn-primary" onclick="saveGeneralSettings()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Exchange Rates -->
                    <div class="tab-pane fade" id="exchange-rates">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-currency-exchange me-2"></i>
                                    أسعار الصرف
                                </h5>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshRates()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث تلقائي
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="addNewRate()">
                                        <i class="bi bi-plus me-1"></i>إضافة سعر
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row" id="exchangeRatesContainer">
                                    <div class="col-md-6 mb-3">
                                        <div class="currency-rate">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <strong>USD → SAR</strong>
                                                <span class="status-indicator status-active" title="نشط"></span>
                                            </div>
                                            <div class="row">
                                                <div class="col-8">
                                                    <input type="number" class="form-control" value="3.7500" step="0.0001">
                                                </div>
                                                <div class="col-4">
                                                    <button class="btn btn-outline-primary btn-sm w-100" onclick="updateRate('USD', 'SAR')">
                                                        تحديث
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="text-muted">آخر تحديث: 2025-01-15 10:30</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="currency-rate">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <strong>USD → AED</strong>
                                                <span class="status-indicator status-active" title="نشط"></span>
                                            </div>
                                            <div class="row">
                                                <div class="col-8">
                                                    <input type="number" class="form-control" value="3.6725" step="0.0001">
                                                </div>
                                                <div class="col-4">
                                                    <button class="btn btn-outline-primary btn-sm w-100" onclick="updateRate('USD', 'AED')">
                                                        تحديث
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="text-muted">آخر تحديث: 2025-01-15 10:30</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="currency-rate">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <strong>USD → EGP</strong>
                                                <span class="status-indicator status-warning" title="يحتاج تحديث"></span>
                                            </div>
                                            <div class="row">
                                                <div class="col-8">
                                                    <input type="number" class="form-control" value="30.9000" step="0.0001">
                                                </div>
                                                <div class="col-4">
                                                    <button class="btn btn-outline-primary btn-sm w-100" onclick="updateRate('USD', 'EGP')">
                                                        تحديث
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="text-muted">آخر تحديث: 2025-01-14 15:20</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="currency-rate">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <strong>USD → JOD</strong>
                                                <span class="status-indicator status-active" title="نشط"></span>
                                            </div>
                                            <div class="row">
                                                <div class="col-8">
                                                    <input type="number" class="form-control" value="0.7090" step="0.0001">
                                                </div>
                                                <div class="col-4">
                                                    <button class="btn btn-outline-primary btn-sm w-100" onclick="updateRate('USD', 'JOD')">
                                                        تحديث
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="text-muted">آخر تحديث: 2025-01-15 09:45</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end mt-3">
                                    <button class="btn btn-success" onclick="saveAllRates()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ جميع الأسعار
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fees Settings -->
                    <div class="tab-pane fade" id="fees-settings">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-calculator me-2"></i>
                                    إعدادات الرسوم
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <h6>الرسوم الثابتة</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">رسوم التحويل الثابتة</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" value="5.00" step="0.01" id="fixedFee">
                                                <span class="input-group-text">USD</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">رسوم المعالجة السريعة</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" value="10.00" step="0.01" id="expressFee">
                                                <span class="input-group-text">USD</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <h6>الرسوم النسبية</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">نسبة الرسوم الافتراضية</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" value="2.5" step="0.1" id="defaultFeePercentage">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">الحد الأدنى للرسوم</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" value="1.00" step="0.01" id="minFee">
                                                <span class="input-group-text">USD</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button class="btn btn-primary" onclick="saveFeesSettings()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ إعدادات الرسوم
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="tab-pane fade" id="security-settings">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-shield-check me-2"></i>
                                    إعدادات الأمان
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">مدة انتهاء الجلسة (دقيقة)</label>
                                            <input type="number" class="form-control" value="30" id="sessionTimeout">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">عدد محاولات تسجيل الدخول</label>
                                            <input type="number" class="form-control" value="5" id="maxLoginAttempts">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireTwoFactor" checked>
                                        <label class="form-check-label" for="requireTwoFactor">
                                            تفعيل المصادقة الثنائية للمديرين
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
                                        <label class="form-check-label" for="enableAuditLog">
                                            تفعيل سجل المراجعة
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button class="btn btn-primary" onclick="saveSecuritySettings()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ إعدادات الأمان
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="tab-pane fade" id="notification-settings">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-bell me-2"></i>
                                    إعدادات الإشعارات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <h6>إشعارات البريد الإلكتروني</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="emailNewTransfer" checked>
                                        <label class="form-check-label" for="emailNewTransfer">
                                            إشعار عند إنشاء تحويل جديد
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="emailTransferComplete" checked>
                                        <label class="form-check-label" for="emailTransferComplete">
                                            إشعار عند اكتمال التحويل
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <h6>إشعارات SMS</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="smsNewTransfer" checked>
                                        <label class="form-check-label" for="smsNewTransfer">
                                            رسالة نصية عند إنشاء تحويل جديد
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="smsPickupReady" checked>
                                        <label class="form-check-label" for="smsPickupReady">
                                            رسالة نصية عند جاهزية الاستلام
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button class="btn btn-primary" onclick="saveNotificationSettings()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ إعدادات الإشعارات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Settings -->
                    <div class="tab-pane fade" id="api-settings">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-cloud me-2"></i>
                                    إعدادات API
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">مزود أسعار الصرف</label>
                                            <select class="form-control" id="exchangeRateProvider">
                                                <option value="fixer">Fixer.io</option>
                                                <option value="currencylayer">CurrencyLayer</option>
                                                <option value="openexchange">Open Exchange Rates</option>
                                                <option value="fallback" selected>النظام الداخلي</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">مزود الرسائل النصية</label>
                                            <select class="form-control" id="smsProvider">
                                                <option value="twilio">Twilio</option>
                                                <option value="nexmo">Nexmo</option>
                                                <option value="local" selected>النظام المحلي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">مزود الدفع الإلكتروني</label>
                                            <select class="form-control" id="paymentProvider">
                                                <option value="stripe">Stripe</option>
                                                <option value="paypal">PayPal</option>
                                                <option value="local" selected>النظام المحلي</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">حد معدل API (طلب/دقيقة)</label>
                                            <input type="number" class="form-control" value="100" id="apiRateLimit">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button class="btn btn-primary" onclick="saveApiSettings()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ إعدادات API
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Compliance Settings -->
                    <div class="tab-pane fade" id="compliance-settings">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-file-text me-2"></i>
                                    إعدادات الامتثال
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="setting-item">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">حد AML للتحقق (USD)</label>
                                            <input type="number" class="form-control" value="10000" id="amlThreshold">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">حد KYC المطلوب (USD)</label>
                                            <input type="number" class="form-control" value="3000" id="kycThreshold">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableAmlChecks" checked>
                                        <label class="form-check-label" for="enableAmlChecks">
                                            تفعيل فحوصات AML التلقائية
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireKycVerification" checked>
                                        <label class="form-check-label" for="requireKycVerification">
                                            طلب التحقق من الهوية للمبالغ الكبيرة
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button class="btn btn-primary" onclick="saveComplianceSettings()">
                                        <i class="bi bi-check-lg me-2"></i>حفظ إعدادات الامتثال
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function saveGeneralSettings() {
            const settings = {
                appName: document.getElementById('appName').value,
                minTransferAmount: document.getElementById('minTransferAmount').value,
                maxTransferAmount: document.getElementById('maxTransferAmount').value,
                defaultCurrency: document.getElementById('defaultCurrency').value,
                timezone: document.getElementById('timezone').value
            };
            
            alert('تم حفظ الإعدادات العامة بنجاح');
            console.log('General Settings:', settings);
        }
        
        function updateRate(from, to) {
            alert(`تحديث سعر الصرف من ${from} إلى ${to}`);
        }
        
        function refreshRates() {
            alert('تحديث جميع أسعار الصرف تلقائياً...');
        }
        
        function addNewRate() {
            alert('إضافة سعر صرف جديد');
        }
        
        function saveAllRates() {
            alert('تم حفظ جميع أسعار الصرف بنجاح');
        }
        
        function saveFeesSettings() {
            const settings = {
                fixedFee: document.getElementById('fixedFee').value,
                expressFee: document.getElementById('expressFee').value,
                defaultFeePercentage: document.getElementById('defaultFeePercentage').value,
                minFee: document.getElementById('minFee').value
            };
            
            alert('تم حفظ إعدادات الرسوم بنجاح');
            console.log('Fees Settings:', settings);
        }
        
        function saveSecuritySettings() {
            const settings = {
                sessionTimeout: document.getElementById('sessionTimeout').value,
                maxLoginAttempts: document.getElementById('maxLoginAttempts').value,
                requireTwoFactor: document.getElementById('requireTwoFactor').checked,
                enableAuditLog: document.getElementById('enableAuditLog').checked
            };
            
            alert('تم حفظ إعدادات الأمان بنجاح');
            console.log('Security Settings:', settings);
        }
        
        function saveNotificationSettings() {
            const settings = {
                emailNewTransfer: document.getElementById('emailNewTransfer').checked,
                emailTransferComplete: document.getElementById('emailTransferComplete').checked,
                smsNewTransfer: document.getElementById('smsNewTransfer').checked,
                smsPickupReady: document.getElementById('smsPickupReady').checked
            };
            
            alert('تم حفظ إعدادات الإشعارات بنجاح');
            console.log('Notification Settings:', settings);
        }
        
        function saveApiSettings() {
            const settings = {
                exchangeRateProvider: document.getElementById('exchangeRateProvider').value,
                smsProvider: document.getElementById('smsProvider').value,
                paymentProvider: document.getElementById('paymentProvider').value,
                apiRateLimit: document.getElementById('apiRateLimit').value
            };
            
            alert('تم حفظ إعدادات API بنجاح');
            console.log('API Settings:', settings);
        }
        
        function saveComplianceSettings() {
            const settings = {
                amlThreshold: document.getElementById('amlThreshold').value,
                kycThreshold: document.getElementById('kycThreshold').value,
                enableAmlChecks: document.getElementById('enableAmlChecks').checked,
                requireKycVerification: document.getElementById('requireKycVerification').checked
            };
            
            alert('تم حفظ إعدادات الامتثال بنجاح');
            console.log('Compliance Settings:', settings);
        }
        
        function backupSettings() {
            alert('إنشاء نسخة احتياطية من الإعدادات...');
        }
        
        function restoreSettings() {
            if (confirm('هل أنت متأكد من استعادة الإعدادات؟ سيتم استبدال الإعدادات الحالية.')) {
                alert('استعادة الإعدادات...');
            }
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
