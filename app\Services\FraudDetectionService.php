<?php

class FraudDetectionService {
    private $db;
    private $riskThresholds = [
        'high_amount' => 10000,
        'daily_limit' => 50000,
        'velocity_limit' => 5, // max transfers per hour
        'suspicious_countries' => ['XX', 'YY'], // placeholder
        'max_failed_attempts' => 3
    ];
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Analyze transfer for fraud risk
     */
    public function analyzeTransfer($transferData) {
        $riskScore = 0;
        $riskFactors = [];
        $recommendations = [];
        
        // Check amount-based risks
        $amountRisk = $this->checkAmountRisk($transferData);
        $riskScore += $amountRisk['score'];
        if ($amountRisk['risk']) {
            $riskFactors[] = $amountRisk['factor'];
            $recommendations[] = $amountRisk['recommendation'];
        }
        
        // Check velocity risks
        $velocityRisk = $this->checkVelocityRisk($transferData);
        $riskScore += $velocityRisk['score'];
        if ($velocityRisk['risk']) {
            $riskFactors[] = $velocityRisk['factor'];
            $recommendations[] = $velocityRisk['recommendation'];
        }
        
        // Check geographic risks
        $geoRisk = $this->checkGeographicRisk($transferData);
        $riskScore += $geoRisk['score'];
        if ($geoRisk['risk']) {
            $riskFactors[] = $geoRisk['factor'];
            $recommendations[] = $geoRisk['recommendation'];
        }
        
        // Check user behavior patterns
        $behaviorRisk = $this->checkBehaviorPattern($transferData);
        $riskScore += $behaviorRisk['score'];
        if ($behaviorRisk['risk']) {
            $riskFactors[] = $behaviorRisk['factor'];
            $recommendations[] = $behaviorRisk['recommendation'];
        }
        
        // Check blacklist
        $blacklistRisk = $this->checkBlacklist($transferData);
        $riskScore += $blacklistRisk['score'];
        if ($blacklistRisk['risk']) {
            $riskFactors[] = $blacklistRisk['factor'];
            $recommendations[] = $blacklistRisk['recommendation'];
        }
        
        // Determine risk level
        $riskLevel = $this->calculateRiskLevel($riskScore);
        
        // Log fraud analysis
        $this->logFraudAnalysis($transferData, $riskScore, $riskLevel, $riskFactors);
        
        return [
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
            'recommendations' => $recommendations,
            'action_required' => $this->getRequiredAction($riskLevel),
            'auto_approve' => $riskLevel === 'low',
            'requires_review' => in_array($riskLevel, ['medium', 'high']),
            'should_block' => $riskLevel === 'critical'
        ];
    }
    
    private function checkAmountRisk($transferData) {
        $amount = $transferData['amount'];
        $userId = $transferData['sender_id'];
        
        // Check if amount is unusually high
        if ($amount > $this->riskThresholds['high_amount']) {
            return [
                'risk' => true,
                'score' => 30,
                'factor' => 'مبلغ عالي غير اعتيادي',
                'recommendation' => 'مراجعة يدوية للمبلغ العالي'
            ];
        }
        
        // Check user's average transfer amount
        $stmt = $this->db->prepare("
            SELECT AVG(amount) as avg_amount, COUNT(*) as transfer_count 
            FROM transfers 
            WHERE sender_id = ? AND created_at > DATE('now', '-30 days')
        ");
        $stmt->execute([$userId]);
        $userStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($userStats['transfer_count'] > 0 && $amount > ($userStats['avg_amount'] * 5)) {
            return [
                'risk' => true,
                'score' => 20,
                'factor' => 'مبلغ أعلى من المعتاد بـ 5 مرات',
                'recommendation' => 'التحقق من هوية المرسل'
            ];
        }
        
        return ['risk' => false, 'score' => 0];
    }
    
    private function checkVelocityRisk($transferData) {
        $userId = $transferData['sender_id'];
        
        // Check transfers in last hour
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as recent_transfers 
            FROM transfers 
            WHERE sender_id = ? AND created_at > DATETIME('now', '-1 hour')
        ");
        $stmt->execute([$userId]);
        $recentCount = $stmt->fetchColumn();
        
        if ($recentCount >= $this->riskThresholds['velocity_limit']) {
            return [
                'risk' => true,
                'score' => 40,
                'factor' => 'عدد تحويلات عالي في فترة قصيرة',
                'recommendation' => 'إيقاف مؤقت وتحقق من النشاط'
            ];
        }
        
        // Check daily amount limit
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as daily_total 
            FROM transfers 
            WHERE sender_id = ? AND DATE(created_at) = DATE('now')
        ");
        $stmt->execute([$userId]);
        $dailyTotal = $stmt->fetchColumn() ?: 0;
        
        if (($dailyTotal + $transferData['amount']) > $this->riskThresholds['daily_limit']) {
            return [
                'risk' => true,
                'score' => 25,
                'factor' => 'تجاوز الحد اليومي للتحويلات',
                'recommendation' => 'مراجعة الحد اليومي للمستخدم'
            ];
        }
        
        return ['risk' => false, 'score' => 0];
    }
    
    private function checkGeographicRisk($transferData) {
        $senderCountry = $transferData['sender_country_id'];
        $receiverCountry = $transferData['receiver_country_id'];
        
        // Check if countries are in suspicious list
        $stmt = $this->db->prepare("
            SELECT code FROM countries WHERE id IN (?, ?)
        ");
        $stmt->execute([$senderCountry, $receiverCountry]);
        $countries = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($countries as $countryCode) {
            if (in_array($countryCode, $this->riskThresholds['suspicious_countries'])) {
                return [
                    'risk' => true,
                    'score' => 35,
                    'factor' => 'تحويل من/إلى دولة عالية المخاطر',
                    'recommendation' => 'تحقق إضافي من الهوية والغرض'
                ];
            }
        }
        
        // Check unusual country combination
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as combo_count 
            FROM transfers 
            WHERE sender_country_id = ? AND receiver_country_id = ?
        ");
        $stmt->execute([$senderCountry, $receiverCountry]);
        $comboCount = $stmt->fetchColumn();
        
        if ($comboCount < 5) { // Less than 5 transfers between these countries
            return [
                'risk' => true,
                'score' => 15,
                'factor' => 'مسار تحويل غير شائع',
                'recommendation' => 'مراجعة الغرض من التحويل'
            ];
        }
        
        return ['risk' => false, 'score' => 0];
    }
    
    private function checkBehaviorPattern($transferData) {
        $userId = $transferData['sender_id'];
        
        // Check if user typically sends to same recipients
        $stmt = $this->db->prepare("
            SELECT COUNT(DISTINCT receiver_phone) as unique_recipients,
                   COUNT(*) as total_transfers
            FROM transfers 
            WHERE sender_id = ?
        ");
        $stmt->execute([$userId]);
        $pattern = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($pattern['total_transfers'] > 10) {
            $recipientRatio = $pattern['unique_recipients'] / $pattern['total_transfers'];
            
            // If user suddenly sends to many new recipients
            if ($recipientRatio > 0.8) {
                return [
                    'risk' => true,
                    'score' => 20,
                    'factor' => 'تغيير مفاجئ في نمط المستلمين',
                    'recommendation' => 'التحقق من سبب التغيير في النمط'
                ];
            }
        }
        
        // Check time pattern
        $currentHour = date('H');
        $stmt = $this->db->prepare("
            SELECT AVG(CAST(strftime('%H', created_at) AS INTEGER)) as avg_hour
            FROM transfers 
            WHERE sender_id = ? AND created_at > DATE('now', '-30 days')
        ");
        $stmt->execute([$userId]);
        $avgHour = $stmt->fetchColumn();
        
        if ($avgHour && abs($currentHour - $avgHour) > 6) {
            return [
                'risk' => true,
                'score' => 10,
                'factor' => 'تحويل في وقت غير اعتيادي',
                'recommendation' => 'تأكيد هوية المرسل'
            ];
        }
        
        return ['risk' => false, 'score' => 0];
    }
    
    private function checkBlacklist($transferData) {
        $phone = $transferData['receiver_phone'];
        $senderId = $transferData['sender_id'];
        
        // Check if receiver phone is blacklisted
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM blacklist 
            WHERE (phone = ? OR user_id = ?) AND is_active = 1
        ");
        $stmt->execute([$phone, $senderId]);
        $blacklisted = $stmt->fetchColumn();
        
        if ($blacklisted > 0) {
            return [
                'risk' => true,
                'score' => 100, // Critical risk
                'factor' => 'مرسل أو مستلم في القائمة السوداء',
                'recommendation' => 'حظر التحويل فوراً'
            ];
        }
        
        return ['risk' => false, 'score' => 0];
    }
    
    private function calculateRiskLevel($score) {
        if ($score >= 80) return 'critical';
        if ($score >= 50) return 'high';
        if ($score >= 25) return 'medium';
        return 'low';
    }
    
    private function getRequiredAction($riskLevel) {
        switch ($riskLevel) {
            case 'critical':
                return 'block_transfer';
            case 'high':
                return 'manual_review';
            case 'medium':
                return 'additional_verification';
            case 'low':
            default:
                return 'auto_approve';
        }
    }
    
    private function logFraudAnalysis($transferData, $score, $level, $factors) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO fraud_analysis (
                    transfer_id, user_id, risk_score, risk_level, 
                    risk_factors, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $transferData['transfer_id'] ?? null,
                $transferData['sender_id'],
                $score,
                $level,
                json_encode($factors),
                date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log fraud analysis: " . $e->getMessage());
        }
    }
    
    /**
     * Get AI-powered recommendations for user
     */
    public function getSmartRecommendations($userId) {
        $recommendations = [];
        
        // Analyze user's transfer patterns
        $stmt = $this->db->prepare("
            SELECT 
                AVG(amount) as avg_amount,
                COUNT(*) as transfer_count,
                COUNT(DISTINCT receiver_country_id) as countries_count,
                COUNT(DISTINCT receiver_phone) as recipients_count,
                MAX(created_at) as last_transfer
            FROM transfers 
            WHERE sender_id = ? AND created_at > DATE('now', '-90 days')
        ");
        $stmt->execute([$userId]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($stats['transfer_count'] > 0) {
            // Recommend based on frequency
            if ($stats['transfer_count'] > 20) {
                $recommendations[] = [
                    'type' => 'loyalty_program',
                    'title' => 'برنامج العملاء المميزين',
                    'description' => 'انضم لبرنامج العملاء المميزين واحصل على رسوم مخفضة',
                    'priority' => 'high'
                ];
            }
            
            // Recommend based on amount
            if ($stats['avg_amount'] > 5000) {
                $recommendations[] = [
                    'type' => 'premium_service',
                    'title' => 'خدمة التحويل السريع',
                    'description' => 'احصل على تحويلات فورية للمبالغ الكبيرة',
                    'priority' => 'medium'
                ];
            }
            
            // Recommend based on countries
            if ($stats['countries_count'] > 3) {
                $recommendations[] = [
                    'type' => 'multi_currency',
                    'title' => 'محفظة متعددة العملات',
                    'description' => 'وفر على رسوم الصرف مع محفظة متعددة العملات',
                    'priority' => 'medium'
                ];
            }
        }
        
        return $recommendations;
    }
    
    /**
     * Update user risk profile
     */
    public function updateUserRiskProfile($userId) {
        $stmt = $this->db->prepare("
            SELECT 
                AVG(risk_score) as avg_risk_score,
                COUNT(*) as analysis_count,
                SUM(CASE WHEN risk_level = 'high' OR risk_level = 'critical' THEN 1 ELSE 0 END) as high_risk_count
            FROM fraud_analysis 
            WHERE user_id = ? AND created_at > DATE('now', '-30 days')
        ");
        $stmt->execute([$userId]);
        $riskProfile = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $profileLevel = 'low';
        if ($riskProfile['analysis_count'] > 0) {
            $highRiskRatio = $riskProfile['high_risk_count'] / $riskProfile['analysis_count'];
            
            if ($highRiskRatio > 0.3 || $riskProfile['avg_risk_score'] > 40) {
                $profileLevel = 'high';
            } elseif ($highRiskRatio > 0.1 || $riskProfile['avg_risk_score'] > 20) {
                $profileLevel = 'medium';
            }
        }
        
        // Update user risk profile
        $stmt = $this->db->prepare("
            UPDATE users SET 
                risk_profile = ?, 
                risk_updated_at = ?
            WHERE id = ?
        ");
        $stmt->execute([$profileLevel, date('Y-m-d H:i:s'), $userId]);
        
        return $profileLevel;
    }
}
