<?php

echo "🔄 إضافة تحويلات تجريبية...\n";

try {
    $db = new PDO('sqlite:database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user and country IDs
    $users = $db->query("SELECT id, name FROM users")->fetchAll(PDO::FETCH_ASSOC);
    $countries = $db->query("SELECT id, name, currency FROM countries")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users) || empty($countries)) {
        echo "❌ لا توجد مستخدمين أو دول في قاعدة البيانات\n";
        exit(1);
    }
    
    // Sample transfers data
    $sampleTransfers = [
        [
            'sender_name' => 'أحمد محمد علي',
            'sender_phone' => '+966501234567',
            'receiver_name' => 'فاطمة أحمد',
            'receiver_phone' => '+201234567890',
            'amount' => 1000.00,
            'sender_country' => 'SA',
            'receiver_country' => 'EG',
            'status' => 'pending'
        ],
        [
            'sender_name' => 'محمد عبدالله',
            'sender_phone' => '+971501234567',
            'receiver_name' => 'علي حسن',
            'receiver_phone' => '+966501234568',
            'amount' => 2500.00,
            'sender_country' => 'AE',
            'receiver_country' => 'SA',
            'status' => 'processing'
        ],
        [
            'sender_name' => 'سارة أحمد',
            'sender_phone' => '+966501234569',
            'receiver_name' => 'نور محمد',
            'receiver_phone' => '+962791234567',
            'amount' => 750.00,
            'sender_country' => 'SA',
            'receiver_country' => 'JO',
            'status' => 'completed'
        ],
        [
            'sender_name' => 'خالد العتيبي',
            'sender_phone' => '+966501234570',
            'receiver_name' => 'أمل السيد',
            'receiver_phone' => '+201234567891',
            'amount' => 3200.00,
            'sender_country' => 'SA',
            'receiver_country' => 'EG',
            'status' => 'pending'
        ],
        [
            'sender_name' => 'عبدالرحمن الشمري',
            'sender_phone' => '+966501234571',
            'receiver_name' => 'ليلى أحمد',
            'receiver_phone' => '+971501234568',
            'amount' => 1800.00,
            'sender_country' => 'SA',
            'receiver_country' => 'AE',
            'status' => 'ready_for_pickup'
        ],
        [
            'sender_name' => 'نادية محمد',
            'sender_phone' => '+201234567892',
            'receiver_name' => 'حسام علي',
            'receiver_phone' => '+966501234572',
            'amount' => 500.00,
            'sender_country' => 'EG',
            'receiver_country' => 'SA',
            'status' => 'cancelled'
        ]
    ];
    
    // Get country mapping
    $countryMap = [];
    foreach ($countries as $country) {
        $countryMap[$country['id']] = $country;
    }
    
    $countryCodeMap = [];
    foreach ($countries as $country) {
        $countryCodeMap[$country['name']] = $country['id'];
    }
    
    // Map country codes to names for lookup
    $codeToName = [
        'SA' => 'Saudi Arabia',
        'AE' => 'United Arab Emirates', 
        'EG' => 'Egypt',
        'JO' => 'Jordan',
        'KW' => 'Kuwait',
        'US' => 'United States'
    ];
    
    $insertedCount = 0;
    
    foreach ($sampleTransfers as $transfer) {
        // Get country IDs
        $senderCountryName = $codeToName[$transfer['sender_country']] ?? null;
        $receiverCountryName = $codeToName[$transfer['receiver_country']] ?? null;
        
        $senderCountryId = null;
        $receiverCountryId = null;
        $senderCurrency = 'USD';
        $receiverCurrency = 'USD';
        
        foreach ($countries as $country) {
            if ($country['name'] === $senderCountryName) {
                $senderCountryId = $country['id'];
                $senderCurrency = $country['currency'];
            }
            if ($country['name'] === $receiverCountryName) {
                $receiverCountryId = $country['id'];
                $receiverCurrency = $country['currency'];
            }
        }
        
        if (!$senderCountryId || !$receiverCountryId) {
            echo "⚠️  تخطي التحويل: لم يتم العثور على البلدان\n";
            continue;
        }
        
        // Calculate exchange rate and converted amount
        $exchangeRate = 1.0;
        if ($senderCurrency !== $receiverCurrency) {
            // Get exchange rate from database or use default
            $rateStmt = $db->prepare("SELECT rate FROM exchange_rates WHERE from_currency = ? AND to_currency = ? ORDER BY created_at DESC LIMIT 1");
            $rateStmt->execute([$senderCurrency, $receiverCurrency]);
            $rateResult = $rateStmt->fetch();
            
            if ($rateResult) {
                $exchangeRate = $rateResult['rate'];
            } else {
                // Default rates
                $defaultRates = [
                    'USD_SAR' => 3.75,
                    'USD_AED' => 3.67,
                    'USD_EGP' => 30.90,
                    'USD_JOD' => 0.71,
                    'SAR_EGP' => 8.24,
                    'AED_SAR' => 1.02
                ];
                $rateKey = $senderCurrency . '_' . $receiverCurrency;
                $exchangeRate = $defaultRates[$rateKey] ?? 1.0;
            }
        }
        
        $convertedAmount = $transfer['amount'] * $exchangeRate;
        $feeAmount = ($transfer['amount'] * 0.025) + 5.00; // 2.5% + $5 fixed fee
        $totalAmount = $transfer['amount'] + $feeAmount;
        
        // Generate codes
        $transferCode = 'ET' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $pickupCode = str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        
        // Insert transfer
        $stmt = $db->prepare("
            INSERT INTO transfers (
                transfer_code, pickup_code, sender_id, sender_name, sender_phone,
                sender_country_id, receiver_name, receiver_phone, receiver_country_id,
                amount, converted_amount, exchange_rate, fee_amount, total_amount,
                sender_currency, receiver_currency, status, payment_method, pickup_method,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $result = $stmt->execute([
            $transferCode,
            $pickupCode,
            $users[0]['id'], // Use first user as sender
            $transfer['sender_name'],
            $transfer['sender_phone'],
            $senderCountryId,
            $transfer['receiver_name'],
            $transfer['receiver_phone'],
            $receiverCountryId,
            $transfer['amount'],
            $convertedAmount,
            $exchangeRate,
            $feeAmount,
            $totalAmount,
            $senderCurrency,
            $receiverCurrency,
            $transfer['status'],
            'cash',
            'cash'
        ]);
        
        if ($result) {
            $insertedCount++;
            echo "✅ تم إضافة التحويل: {$transferCode} - {$transfer['sender_name']} → {$transfer['receiver_name']}\n";
        }
    }
    
    echo "\n🎉 تم إضافة {$insertedCount} تحويل تجريبي بنجاح!\n";
    
    // Show summary
    $totalTransfers = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
    $statusCounts = $db->query("
        SELECT 
            status,
            COUNT(*) as count
        FROM transfers 
        GROUP BY status
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📊 ملخص التحويلات:\n";
    echo "   إجمالي التحويلات: {$totalTransfers}\n";
    
    foreach ($statusCounts as $statusCount) {
        $statusNames = [
            'pending' => 'معلق',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            'ready_for_pickup' => 'جاهز للاستلام'
        ];
        $statusName = $statusNames[$statusCount['status']] ?? $statusCount['status'];
        echo "   {$statusName}: {$statusCount['count']}\n";
    }
    
    echo "\n🌐 يمكنك الآن زيارة: http://localhost:8000/admin/transfers\n";
    echo "👤 تسجيل الدخول: <EMAIL> / password\n\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

?>
