<?php
// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Require admin access
    if (!is_logged_in()) {
        throw new Exception('Authentication required', 401);
    }
    
    $userData = get_user_data();
    if ($userData['role'] !== 'admin') {
        throw new Exception('Admin access required', 403);
    }
    
    // Connect to database
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get users with their transfer statistics
    $users = $db->query("
        SELECT 
            u.*,
            COALESCE(transfer_stats.transfer_count, 0) as total_transfers,
            COALESCE(transfer_stats.completed_transfers, 0) as completed_transfers,
            COALESCE(transfer_stats.total_amount, 0) as total_transfer_amount,
            COALESCE(transfer_stats.last_transfer_date, NULL) as last_transfer_date
        FROM users u
        LEFT JOIN (
            SELECT 
                sender_id,
                COUNT(*) as transfer_count,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
                MAX(created_at) as last_transfer_date
            FROM transfers 
            GROUP BY sender_id
        ) transfer_stats ON u.id = transfer_stats.sender_id
        ORDER BY u.created_at DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Format user data (remove sensitive information)
    $formattedUsers = [];
    foreach ($users as $user) {
        $formattedUsers[] = [
            'id' => (int)$user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'phone' => $user['phone'],
            'role' => $user['role'],
            'kyc_status' => $user['kyc_status'],
            'kyc_level' => $user['kyc_level'],
            'is_active' => (bool)$user['is_active'],
            'created_at' => $user['created_at'],
            'updated_at' => $user['updated_at'],
            'statistics' => [
                'total_transfers' => (int)$user['total_transfers'],
                'completed_transfers' => (int)$user['completed_transfers'],
                'pending_transfers' => (int)$user['total_transfers'] - (int)$user['completed_transfers'],
                'total_transfer_amount' => (float)$user['total_transfer_amount'],
                'last_transfer_date' => $user['last_transfer_date'],
                'success_rate' => $user['total_transfers'] > 0 
                    ? round(($user['completed_transfers'] / $user['total_transfers']) * 100, 2) 
                    : 0
            ]
        ];
    }
    
    // Get user statistics by role
    $roleStats = $db->query("
        SELECT 
            role,
            COUNT(*) as count,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count,
            COUNT(CASE WHEN kyc_status = 'verified' THEN 1 END) as verified_count
        FROM users 
        GROUP BY role
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Get KYC statistics
    $kycStats = $db->query("
        SELECT 
            kyc_status,
            COUNT(*) as count
        FROM users 
        GROUP BY kyc_status
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Get recent registrations
    $recentUsers = $db->query("
        SELECT name, email, role, created_at
        FROM users 
        ORDER BY created_at DESC 
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate overall statistics
    $totalUsers = count($users);
    $activeUsers = count(array_filter($users, function($u) { return $u['is_active']; }));
    $verifiedUsers = count(array_filter($users, function($u) { return $u['kyc_status'] === 'verified'; }));
    $usersWithTransfers = count(array_filter($users, function($u) { return $u['total_transfers'] > 0; }));
    
    $stats = [
        'total_users' => $totalUsers,
        'active_users' => $activeUsers,
        'inactive_users' => $totalUsers - $activeUsers,
        'verified_users' => $verifiedUsers,
        'pending_verification' => count(array_filter($users, function($u) { return $u['kyc_status'] === 'pending'; })),
        'users_with_transfers' => $usersWithTransfers,
        'users_without_transfers' => $totalUsers - $usersWithTransfers,
        'by_role' => [],
        'by_kyc_status' => [],
        'registration_trend' => [
            'today' => (int)$db->query("SELECT COUNT(*) FROM users WHERE DATE(created_at) = DATE('now')")->fetchColumn(),
            'this_week' => (int)$db->query("SELECT COUNT(*) FROM users WHERE DATE(created_at) >= DATE('now', '-7 days')")->fetchColumn(),
            'this_month' => (int)$db->query("SELECT COUNT(*) FROM users WHERE DATE(created_at) >= DATE('now', 'start of month')")->fetchColumn()
        ]
    ];
    
    // Format role statistics
    foreach ($roleStats as $roleStat) {
        $stats['by_role'][$roleStat['role']] = [
            'total' => (int)$roleStat['count'],
            'active' => (int)$roleStat['active_count'],
            'verified' => (int)$roleStat['verified_count']
        ];
    }
    
    // Format KYC statistics
    foreach ($kycStats as $kycStat) {
        $stats['by_kyc_status'][$kycStat['kyc_status']] = (int)$kycStat['count'];
    }
    
    $response = [
        'success' => true,
        'data' => [
            'users' => $formattedUsers,
            'statistics' => $stats,
            'recent_registrations' => $recentUsers
        ],
        'count' => count($formattedUsers),
        'timestamp' => date('Y-m-d H:i:s'),
        'generated_by' => $userData['name'],
        'access_level' => 'admin'
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $statusCode = $e->getCode() ?: 500;
    $response = [
        'success' => false,
        'error' => [
            'message' => $e->getMessage(),
            'code' => $statusCode,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    http_response_code($statusCode);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
