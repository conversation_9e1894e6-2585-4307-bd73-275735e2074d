<?php

class ConfigurationService {
    private $db;
    private $cacheService;
    private $config = [];
    
    public function __construct($database, $cacheService = null) {
        $this->db = $database;
        $this->cacheService = $cacheService;
        $this->loadConfiguration();
    }
    
    /**
     * Load configuration from database and environment
     */
    private function loadConfiguration() {
        // Load from cache first
        if ($this->cacheService) {
            $cachedConfig = $this->cacheService->get('system_configuration');
            if ($cachedConfig !== null) {
                $this->config = $cachedConfig;
                return;
            }
        }
        
        // Load from database
        try {
            $stmt = $this->db->query("SELECT key, value FROM system_settings");
            $dbConfig = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Merge with environment variables
            $this->config = array_merge($this->getDefaultConfig(), $dbConfig);
            
            // Cache the configuration
            if ($this->cacheService) {
                $this->cacheService->put('system_configuration', $this->config, 3600);
            }
            
        } catch (Exception $e) {
            // Fallback to default configuration
            $this->config = $this->getDefaultConfig();
            error_log("Failed to load configuration from database: " . $e->getMessage());
        }
    }
    
    /**
     * Get default configuration from environment variables
     */
    private function getDefaultConfig() {
        return [
            // Application Settings
            'app_name' => $_ENV['APP_NAME'] ?? 'Elite Transfer System',
            'app_env' => $_ENV['APP_ENV'] ?? 'production',
            'app_debug' => $_ENV['APP_DEBUG'] ?? 'false',
            'app_url' => $_ENV['APP_URL'] ?? 'https://elitetransfer.com',
            
            // SMS Configuration
            'sms_provider' => $_ENV['SMS_PROVIDER'] ?? 'local',
            'twilio_account_sid' => $_ENV['TWILIO_ACCOUNT_SID'] ?? '',
            'twilio_auth_token' => $_ENV['TWILIO_AUTH_TOKEN'] ?? '',
            'twilio_from_number' => $_ENV['TWILIO_FROM_NUMBER'] ?? '',
            
            // Email Configuration
            'email_provider' => $_ENV['EMAIL_PROVIDER'] ?? 'local',
            'sendgrid_api_key' => $_ENV['SENDGRID_API_KEY'] ?? '',
            'sendgrid_from_email' => $_ENV['SENDGRID_FROM_EMAIL'] ?? '<EMAIL>',
            'sendgrid_from_name' => $_ENV['SENDGRID_FROM_NAME'] ?? 'Elite Transfer System',
            
            // Exchange Rate Configuration
            'exchange_rate_provider' => $_ENV['EXCHANGE_RATE_PROVIDER'] ?? 'fallback',
            'fixer_api_key' => $_ENV['FIXER_API_KEY'] ?? '',
            'exchangerate_api_key' => $_ENV['EXCHANGERATE_API_KEY'] ?? '',
            
            // Business Configuration
            'default_transfer_fee_percentage' => $_ENV['DEFAULT_TRANSFER_FEE_PERCENTAGE'] ?? '2.5',
            'default_transfer_fee_fixed' => $_ENV['DEFAULT_TRANSFER_FEE_FIXED'] ?? '5.00',
            'max_transfer_amount' => $_ENV['MAX_TRANSFER_AMOUNT'] ?? '50000',
            'min_transfer_amount' => $_ENV['MIN_TRANSFER_AMOUNT'] ?? '1',
            'daily_transfer_limit' => $_ENV['DAILY_TRANSFER_LIMIT'] ?? '100000',
            
            // Security Configuration
            'feature_2fa_enabled' => $_ENV['FEATURE_2FA_ENABLED'] ?? 'true',
            'feature_fraud_detection_enabled' => $_ENV['FEATURE_FRAUD_DETECTION_ENABLED'] ?? 'true',
            'aml_threshold_amount' => $_ENV['AML_THRESHOLD_AMOUNT'] ?? '10000',
            'kyc_required_amount' => $_ENV['KYC_REQUIRED_AMOUNT'] ?? '3000',
            
            // Rate Limiting
            'rate_limit_enabled' => $_ENV['RATE_LIMIT_ENABLED'] ?? 'true',
            'rate_limit_max_attempts' => $_ENV['RATE_LIMIT_MAX_ATTEMPTS'] ?? '60',
            'rate_limit_decay_minutes' => $_ENV['RATE_LIMIT_DECAY_MINUTES'] ?? '1',
            
            // Notification Settings
            'sms_notifications_enabled' => $_ENV['FEATURE_SMS_NOTIFICATIONS_ENABLED'] ?? 'true',
            'email_notifications_enabled' => $_ENV['FEATURE_EMAIL_NOTIFICATIONS_ENABLED'] ?? 'true',
            
            // Cache Settings
            'cache_driver' => $_ENV['CACHE_DRIVER'] ?? 'file',
            'cache_default_ttl' => '3600',
            
            // Logging Settings
            'log_level' => $_ENV['LOG_LEVEL'] ?? 'info',
            'log_channel' => $_ENV['LOG_CHANNEL'] ?? 'file'
        ];
    }
    
    /**
     * Get configuration value
     */
    public function get($key, $default = null) {
        return $this->config[$key] ?? $default;
    }
    
    /**
     * Set configuration value
     */
    public function set($key, $value) {
        $this->config[$key] = $value;
        
        // Update in database
        try {
            $stmt = $this->db->prepare("
                INSERT OR REPLACE INTO system_settings (key, value, updated_at) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$key, $value, date('Y-m-d H:i:s')]);
            
            // Clear cache
            if ($this->cacheService) {
                $this->cacheService->forget('system_configuration');
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Failed to set configuration: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all configuration
     */
    public function all() {
        return $this->config;
    }
    
    /**
     * Check if feature is enabled
     */
    public function isFeatureEnabled($feature) {
        $key = 'feature_' . $feature . '_enabled';
        return $this->get($key, 'false') === 'true';
    }
    
    /**
     * Get business rules
     */
    public function getBusinessRules() {
        return [
            'transfer_fees' => [
                'percentage' => floatval($this->get('default_transfer_fee_percentage', 2.5)),
                'fixed' => floatval($this->get('default_transfer_fee_fixed', 5.00))
            ],
            'limits' => [
                'max_transfer_amount' => floatval($this->get('max_transfer_amount', 50000)),
                'min_transfer_amount' => floatval($this->get('min_transfer_amount', 1)),
                'daily_transfer_limit' => floatval($this->get('daily_transfer_limit', 100000))
            ],
            'compliance' => [
                'aml_threshold' => floatval($this->get('aml_threshold_amount', 10000)),
                'kyc_required' => floatval($this->get('kyc_required_amount', 3000))
            ]
        ];
    }
    
    /**
     * Get external service configuration
     */
    public function getExternalServiceConfig($service) {
        switch ($service) {
            case 'sms':
                return [
                    'provider' => $this->get('sms_provider', 'local'),
                    'twilio' => [
                        'account_sid' => $this->get('twilio_account_sid'),
                        'auth_token' => $this->get('twilio_auth_token'),
                        'from_number' => $this->get('twilio_from_number')
                    ]
                ];
                
            case 'email':
                return [
                    'provider' => $this->get('email_provider', 'local'),
                    'sendgrid' => [
                        'api_key' => $this->get('sendgrid_api_key'),
                        'from_email' => $this->get('sendgrid_from_email'),
                        'from_name' => $this->get('sendgrid_from_name')
                    ]
                ];
                
            case 'exchange_rate':
                return [
                    'provider' => $this->get('exchange_rate_provider', 'fallback'),
                    'fixer' => [
                        'api_key' => $this->get('fixer_api_key')
                    ],
                    'exchangerate_api' => [
                        'api_key' => $this->get('exchangerate_api_key')
                    ]
                ];
                
            default:
                return [];
        }
    }
    
    /**
     * Validate configuration
     */
    public function validateConfiguration() {
        $errors = [];
        
        // Check required SMS configuration if SMS is enabled
        if ($this->isFeatureEnabled('sms_notifications')) {
            $smsProvider = $this->get('sms_provider');
            if ($smsProvider === 'twilio' && empty($this->get('twilio_account_sid'))) {
                $errors[] = 'Twilio Account SID is required when using Twilio SMS provider';
            }
        }
        
        // Check required email configuration if email is enabled
        if ($this->isFeatureEnabled('email_notifications')) {
            $emailProvider = $this->get('email_provider');
            if ($emailProvider === 'sendgrid' && empty($this->get('sendgrid_api_key'))) {
                $errors[] = 'SendGrid API key is required when using SendGrid email provider';
            }
        }
        
        // Check exchange rate configuration
        $exchangeProvider = $this->get('exchange_rate_provider');
        if ($exchangeProvider === 'fixer' && empty($this->get('fixer_api_key'))) {
            $errors[] = 'Fixer.io API key is required when using Fixer exchange rate provider';
        }
        
        // Validate business rules
        $maxAmount = floatval($this->get('max_transfer_amount', 50000));
        $minAmount = floatval($this->get('min_transfer_amount', 1));
        
        if ($maxAmount <= $minAmount) {
            $errors[] = 'Maximum transfer amount must be greater than minimum transfer amount';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Export configuration for backup
     */
    public function exportConfiguration() {
        return [
            'exported_at' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'configuration' => $this->config
        ];
    }
    
    /**
     * Import configuration from backup
     */
    public function importConfiguration($configData) {
        if (!isset($configData['configuration']) || !is_array($configData['configuration'])) {
            throw new Exception('Invalid configuration data format');
        }
        
        $imported = 0;
        $errors = [];
        
        foreach ($configData['configuration'] as $key => $value) {
            try {
                if ($this->set($key, $value)) {
                    $imported++;
                } else {
                    $errors[] = "Failed to import: {$key}";
                }
            } catch (Exception $e) {
                $errors[] = "Error importing {$key}: " . $e->getMessage();
            }
        }
        
        // Reload configuration
        $this->loadConfiguration();
        
        return [
            'imported' => $imported,
            'errors' => $errors,
            'total' => count($configData['configuration'])
        ];
    }
    
    /**
     * Get configuration health status
     */
    public function getHealthStatus() {
        $validation = $this->validateConfiguration();
        $externalServices = $this->checkExternalServices();
        
        return [
            'overall_status' => $validation['valid'] && $externalServices['all_healthy'] ? 'healthy' : 'warning',
            'configuration_valid' => $validation['valid'],
            'configuration_errors' => $validation['errors'],
            'external_services' => $externalServices['services'],
            'last_checked' => date('Y-m-d H:i:s')
        ];
    }
    
    private function checkExternalServices() {
        $services = [];
        $allHealthy = true;
        
        // Check SMS service
        if ($this->isFeatureEnabled('sms_notifications')) {
            $smsProvider = $this->get('sms_provider');
            $services['sms'] = [
                'provider' => $smsProvider,
                'status' => $smsProvider !== 'local' && !empty($this->get('twilio_account_sid')) ? 'configured' : 'not_configured'
            ];
            
            if ($services['sms']['status'] === 'not_configured') {
                $allHealthy = false;
            }
        }
        
        // Check email service
        if ($this->isFeatureEnabled('email_notifications')) {
            $emailProvider = $this->get('email_provider');
            $services['email'] = [
                'provider' => $emailProvider,
                'status' => $emailProvider !== 'local' && !empty($this->get('sendgrid_api_key')) ? 'configured' : 'not_configured'
            ];
            
            if ($services['email']['status'] === 'not_configured') {
                $allHealthy = false;
            }
        }
        
        // Check exchange rate service
        $exchangeProvider = $this->get('exchange_rate_provider');
        $services['exchange_rate'] = [
            'provider' => $exchangeProvider,
            'status' => $exchangeProvider !== 'fallback' && !empty($this->get('fixer_api_key')) ? 'configured' : 'fallback'
        ];
        
        return [
            'services' => $services,
            'all_healthy' => $allHealthy
        ];
    }
}
