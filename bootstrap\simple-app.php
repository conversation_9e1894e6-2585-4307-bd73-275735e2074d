<?php

// Simple Laravel-like application bootstrap
class SimpleApp {
    private $routes = [];
    private $middleware = [];
    private $db = null;
    private $notificationService = null;
    private $reportService = null;
    private $otpService = null;
    private $fraudDetectionService = null;
    private $cacheService = null;
    private $performanceMonitor = null;
    private $exchangeRateService = null;
    private $paymentService = null;
    private $complianceService = null;

    public function __construct() {
        session_start();
        $this->loadConfig();
        $this->initDatabase();
        $this->initServices();
    }
    
    private function loadConfig() {
        // Load environment variables
        if (file_exists(__DIR__ . '/../.env')) {
            $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
    }
    
    private function initDatabase() {
        try {
            $dbPath = __DIR__ . '/../database/database.sqlite';
            
            // Create database file if it doesn't exist
            if (!file_exists($dbPath)) {
                touch($dbPath);
            }
            
            $this->db = new PDO('sqlite:' . $dbPath);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create tables if they don't exist
            $this->createTables();
            
        } catch (Exception $e) {
            error_log("Database error: " . $e->getMessage());
        }
    }
    
    private function createTables() {
        $sql = "
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            phone TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'customer',
            branch_id INTEGER,
            country_id INTEGER,
            national_id TEXT,
            address TEXT,
            date_of_birth DATE,
            gender TEXT,
            is_active INTEGER DEFAULT 1,
            two_factor_enabled INTEGER DEFAULT 0,
            two_factor_phone TEXT,
            kyc_status TEXT DEFAULT 'pending',
            risk_profile TEXT DEFAULT 'low',
            risk_updated_at DATETIME,
            daily_limit DECIMAL(15,2) DEFAULT 5000.00,
            monthly_limit DECIMAL(15,2) DEFAULT 50000.00,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS countries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            currency_code TEXT DEFAULT 'USD',
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_code TEXT UNIQUE NOT NULL,
            sender_id INTEGER,
            receiver_id INTEGER,
            sender_country_id INTEGER,
            receiver_country_id INTEGER,
            amount DECIMAL(15,2) NOT NULL,
            exchange_rate DECIMAL(15,6) DEFAULT 1.0,
            converted_amount DECIMAL(15,2) NOT NULL,
            fee_amount DECIMAL(15,2) DEFAULT 0.00,
            total_amount DECIMAL(15,2) NOT NULL,
            status TEXT DEFAULT 'pending',
            pickup_code TEXT,
            receiver_name TEXT NOT NULL,
            receiver_phone TEXT NOT NULL,
            receiver_address TEXT,
            sender_name TEXT NOT NULL,
            sender_phone TEXT NOT NULL,
            purpose TEXT,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sender_id) REFERENCES users(id),
            FOREIGN KEY (receiver_id) REFERENCES users(id),
            FOREIGN KEY (sender_country_id) REFERENCES countries(id),
            FOREIGN KEY (receiver_country_id) REFERENCES countries(id)
        );
        
        CREATE TABLE IF NOT EXISTS branches (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            country_id INTEGER,
            city TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (country_id) REFERENCES countries(id)
        );

        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_id INTEGER,
            type TEXT NOT NULL,
            status TEXT DEFAULT 'sent',
            message TEXT,
            read_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id)
        );

        CREATE TABLE IF NOT EXISTS audit_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            table_name TEXT,
            record_id INTEGER,
            old_values TEXT,
            new_values TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS otps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            phone TEXT NOT NULL,
            otp_code TEXT NOT NULL,
            purpose TEXT DEFAULT 'verification',
            user_id INTEGER,
            expires_at DATETIME NOT NULL,
            verified_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS fraud_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_id INTEGER,
            user_id INTEGER,
            risk_score INTEGER DEFAULT 0,
            risk_level TEXT DEFAULT 'low',
            risk_factors TEXT,
            action_taken TEXT,
            reviewed_by INTEGER,
            reviewed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (reviewed_by) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS blacklist (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            phone TEXT,
            email TEXT,
            reason TEXT,
            added_by INTEGER,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (added_by) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS performance_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            metric_name TEXT NOT NULL,
            metric_value DECIMAL(15,4),
            metric_date DATE,
            category TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS exchange_rates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            from_currency TEXT NOT NULL,
            to_currency TEXT NOT NULL,
            rate DECIMAL(15,6) NOT NULL,
            provider TEXT DEFAULT 'fallback',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS external_api_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            service_name TEXT NOT NULL,
            provider TEXT NOT NULL,
            endpoint TEXT,
            request_data TEXT,
            response_data TEXT,
            status_code INTEGER,
            response_time DECIMAL(10,3),
            success INTEGER DEFAULT 1,
            error_message TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            currency TEXT DEFAULT 'USD',
            payment_method TEXT NOT NULL,
            payment_provider TEXT,
            transaction_id TEXT,
            status TEXT DEFAULT 'pending',
            response_data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id)
        );

        CREATE TABLE IF NOT EXISTS payment_intents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_id INTEGER NOT NULL,
            client_secret TEXT NOT NULL,
            payment_method TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id)
        );

        CREATE TABLE IF NOT EXISTS chargebacks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            charge_id TEXT NOT NULL,
            provider TEXT NOT NULL,
            amount DECIMAL(15,2),
            reason TEXT,
            status TEXT DEFAULT 'open',
            data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS aml_checks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_id INTEGER,
            user_id INTEGER NOT NULL,
            risk_score INTEGER DEFAULT 0,
            action TEXT NOT NULL,
            checks_data TEXT,
            reviewed_by INTEGER,
            reviewed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (reviewed_by) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS kyc_documents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            document_type TEXT NOT NULL,
            file_path TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            verified_by INTEGER,
            verified_at DATETIME,
            rejection_reason TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (verified_by) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS suspicious_activity_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sar_id TEXT UNIQUE NOT NULL,
            transfer_id INTEGER,
            user_id INTEGER NOT NULL,
            reason TEXT NOT NULL,
            description TEXT,
            amount DECIMAL(15,2),
            status TEXT DEFAULT 'pending',
            filed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        );

        CREATE TABLE IF NOT EXISTS currency_transaction_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ctr_id TEXT UNIQUE NOT NULL,
            transfer_id INTEGER,
            user_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            reason TEXT NOT NULL,
            transaction_date DATE NOT NULL,
            status TEXT DEFAULT 'pending',
            filed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transfer_id) REFERENCES transfers(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        );
        ";
        
        $this->db->exec($sql);
        
        // Insert sample data if tables are empty
        $this->insertSampleData();
    }

    private function initServices() {
        // Load external integration services
        require_once __DIR__ . '/../app/Services/ExternalIntegrations/SMSService.php';
        require_once __DIR__ . '/../app/Services/ExternalIntegrations/EmailService.php';
        require_once __DIR__ . '/../app/Services/ExternalIntegrations/ExchangeRateService.php';

        // Load payment and compliance services
        require_once __DIR__ . '/../app/Services/PaymentGateway/PaymentService.php';
        require_once __DIR__ . '/../app/Services/PaymentGateway/StripePaymentProvider.php';
        require_once __DIR__ . '/../app/Services/Compliance/ComplianceService.php';

        // Initialize external services with configuration
        $externalConfig = $this->getExternalServicesConfig();
        $smsService = new SMSService($externalConfig);
        $emailService = new EmailService($externalConfig);
        $exchangeRateService = new ExchangeRateService($externalConfig, null, $this->db);

        // Initialize payment and compliance services
        $this->paymentService = new PaymentService($externalConfig, $this->db);
        $this->complianceService = new ComplianceService($this->db, $externalConfig);

        // Load notification service with external services
        require_once __DIR__ . '/../app/Services/NotificationService.php';
        $this->notificationService = new NotificationService($this->db, $smsService, $emailService);

        // Load report service
        require_once __DIR__ . '/../app/Services/ReportService.php';
        $this->reportService = new ReportService($this->db);

        // Load OTP service
        require_once __DIR__ . '/../app/Services/OTPService.php';
        $this->otpService = new OTPService($this->db, $this->notificationService);

        // Load cache service
        require_once __DIR__ . '/../app/Services/CacheService.php';
        $this->cacheService = new CacheService();

        // Load performance monitor
        require_once __DIR__ . '/../app/Services/PerformanceMonitorService.php';
        $this->performanceMonitor = new PerformanceMonitorService($this->db);

        // Load fraud detection service
        require_once __DIR__ . '/../app/Services/FraudDetectionService.php';
        $this->fraudDetectionService = new FraudDetectionService($this->db);

        // Store exchange rate service
        $this->exchangeRateService = $exchangeRateService;

        // Warm up cache
        $this->cacheService->warmUp($this->db);
    }

    private function getExternalServicesConfig() {
        return [
            // SMS Services Configuration
            'twilio' => [
                'account_sid' => $_ENV['TWILIO_ACCOUNT_SID'] ?? '',
                'auth_token' => $_ENV['TWILIO_AUTH_TOKEN'] ?? '',
                'from_number' => $_ENV['TWILIO_FROM_NUMBER'] ?? ''
            ],
            'aws' => [
                'access_key' => $_ENV['AWS_ACCESS_KEY'] ?? '',
                'secret_key' => $_ENV['AWS_SECRET_KEY'] ?? '',
                'region' => $_ENV['AWS_REGION'] ?? 'us-east-1'
            ],
            'nexmo' => [
                'api_key' => $_ENV['NEXMO_API_KEY'] ?? '',
                'api_secret' => $_ENV['NEXMO_API_SECRET'] ?? '',
                'from' => $_ENV['NEXMO_FROM'] ?? 'EliteTransfer'
            ],

            // Email Services Configuration
            'sendgrid' => [
                'api_key' => $_ENV['SENDGRID_API_KEY'] ?? '',
                'from_email' => $_ENV['SENDGRID_FROM_EMAIL'] ?? '<EMAIL>',
                'from_name' => $_ENV['SENDGRID_FROM_NAME'] ?? 'Elite Transfer System'
            ],
            'smtp' => [
                'host' => $_ENV['SMTP_HOST'] ?? '',
                'port' => $_ENV['SMTP_PORT'] ?? 587,
                'username' => $_ENV['SMTP_USERNAME'] ?? '',
                'password' => $_ENV['SMTP_PASSWORD'] ?? '',
                'encryption' => $_ENV['SMTP_ENCRYPTION'] ?? 'tls',
                'from_email' => $_ENV['SMTP_FROM_EMAIL'] ?? '<EMAIL>',
                'from_name' => $_ENV['SMTP_FROM_NAME'] ?? 'Elite Transfer System'
            ],

            // Exchange Rate Services Configuration
            'fixer' => [
                'api_key' => $_ENV['FIXER_API_KEY'] ?? ''
            ],
            'exchangerate_api' => [
                'api_key' => $_ENV['EXCHANGERATE_API_KEY'] ?? ''
            ],
            'currencylayer' => [
                'api_key' => $_ENV['CURRENCYLAYER_API_KEY'] ?? ''
            ]
        ];
    }
    
    private function insertSampleData() {
        // Check if data already exists
        $stmt = $this->db->query("SELECT COUNT(*) FROM users");
        if ($stmt->fetchColumn() > 0) {
            return; // Data already exists
        }
        
        // Insert countries
        $countries = [
            ['السعودية', 'Saudi Arabia', 'SAU', 'SAR'],
            ['الإمارات', 'UAE', 'UAE', 'AED'],
            ['مصر', 'Egypt', 'EGY', 'EGP'],
            ['الكويت', 'Kuwait', 'KWT', 'KWD'],
            ['الأردن', 'Jordan', 'JOR', 'JOD'],
            ['الولايات المتحدة', 'United States', 'USA', 'USD']
        ];
        
        $stmt = $this->db->prepare("INSERT INTO countries (name_ar, name, code, currency_code) VALUES (?, ?, ?, ?)");
        foreach ($countries as $country) {
            $stmt->execute($country);
        }
        
        // Insert branches
        $branches = [
            ['Elite Transfer - الرياض', 'ET-RY-001', 1, 'الرياض', 'طريق الملك فهد، الرياض', '+966112345678', '<EMAIL>'],
            ['Elite Transfer - دبي', 'ET-DB-001', 2, 'دبي', 'شارع الشيخ زايد، دبي', '+97143456789', '<EMAIL>'],
            ['Elite Transfer - القاهرة', 'ET-CA-001', 3, 'القاهرة', 'شارع التحرير، القاهرة', '+20223456789', '<EMAIL>']
        ];
        
        $stmt = $this->db->prepare("INSERT INTO branches (name, code, country_id, city, address, phone, email) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($branches as $branch) {
            $stmt->execute($branch);
        }
        
        // Insert users
        $users = [
            ['مدير النظام', '<EMAIL>', '+966501234567', password_hash('password123', PASSWORD_DEFAULT), 'super_admin', 1, 1],
            ['فاطمة الزهراء', '<EMAIL>', '+966501234568', password_hash('password123', PASSWORD_DEFAULT), 'agent', 1, 1],
            ['عمر عبدالله', '<EMAIL>', '+966501234569', password_hash('password123', PASSWORD_DEFAULT), 'customer', null, 1]
        ];
        
        $stmt = $this->db->prepare("INSERT INTO users (name, email, phone, password, role, branch_id, country_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($users as $user) {
            $stmt->execute($user);
        }
        
        // Insert sample transfers
        $transfers = [
            ['TRF20250125001', 3, null, 1, 2, 1000.00, 3.75, 3750.00, 25.00, 1025.00, 'completed', 'PK123456', 'أحمد محمد', '+971501234567', 'دبي، الإمارات', 'عمر عبدالله', '+966501234569', 'family_support', 'تحويل للعائلة'],
            ['TRF20250125002', 3, null, 2, 3, 750.00, 30.90, 23175.00, 18.75, 768.75, 'processing', 'PK789012', 'فاطمة علي', '+20101234567', 'القاهرة، مصر', 'عمر عبدالله', '+966501234569', 'education', 'رسوم دراسية']
        ];
        
        $stmt = $this->db->prepare("INSERT INTO transfers (transfer_code, sender_id, receiver_id, sender_country_id, receiver_country_id, amount, exchange_rate, converted_amount, fee_amount, total_amount, status, pickup_code, receiver_name, receiver_phone, receiver_address, sender_name, sender_phone, purpose, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($transfers as $transfer) {
            $stmt->execute($transfer);
        }
    }
    
    public function getDatabase() {
        return $this->db;
    }
    
    public function route($method, $path, $handler) {
        $this->routes[$method][$path] = $handler;
    }
    
    public function get($path, $handler) {
        $this->route('GET', $path, $handler);
    }
    
    public function post($path, $handler) {
        $this->route('POST', $path, $handler);
    }
    
    public function run() {
        // Start performance monitoring
        $this->performanceMonitor->startTimer('total_request');

        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = rtrim($path, '/') ?: '/';

        try {
            if (isset($this->routes[$method][$path])) {
                $handler = $this->routes[$method][$path];
                if (is_callable($handler)) {
                    return $handler();
                } elseif (is_string($handler)) {
                    return $this->loadView($handler);
                }
            }

            // Default routing
            return $this->handleDefaultRouting($path);

        } finally {
            // End performance monitoring and log metrics
            $this->performanceMonitor->endTimer('total_request');
            $this->performanceMonitor->logMetrics();
        }
    }
    
    private function handleDefaultRouting($path) {
        switch ($path) {
            case '/':
                return $this->loadView('welcome');
            case '/login':
                return $this->handleLogin();
            case '/register':
                return $this->loadView('register');
            case '/dashboard':
                return $this->handleDashboard();
            case '/track':
                return $this->handleTrack();
            case '/transfers/create':
                return $this->handleTransferCreate();
            case '/api/login':
                return $this->handleApiLogin();
            case '/api/track':
                return $this->handleApiTrack();
            case '/logout':
                return $this->handleLogout();
            case '/api/dashboard-stats':
                return $this->handleDashboardStats();
            case '/api/chart-data':
                return $this->handleChartData();
            case '/reports':
                return $this->handleReports();
            case '/api/system-health':
                return $this->handleSystemHealth();
            case '/api/performance-stats':
                return $this->handlePerformanceStats();
            case '/admin/monitoring':
                return $this->handleMonitoring();
            case '/api/create-payment-intent':
                return $this->handleCreatePaymentIntent();
            case '/api/confirm-payment':
                return $this->handleConfirmPayment();
            case '/api/submit-kyc':
                return $this->handleSubmitKYC();
            case '/api/payment-webhook':
                return $this->handlePaymentWebhook();
            case '/compliance/dashboard':
                return $this->handleComplianceDashboard();
            default:
                http_response_code(404);
                return $this->loadView('404');
        }
    }
    
    private function handleLogin() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->processLogin();
        }
        return $this->loadView('login');
    }
    
    private function processLogin() {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        if (empty($email) || empty($password)) {
            return $this->jsonResponse(['success' => false, 'message' => 'البريد الإلكتروني وكلمة المرور مطلوبان']);
        }
        
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];

            return $this->jsonResponse([
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'redirect' => '/dashboard?role=' . $user['role']
            ]);
        }
        
        return $this->jsonResponse(['success' => false, 'message' => 'بيانات الدخول غير صحيحة']);
    }
    
    private function handleDashboard() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: /login');
            exit;
        }
        return $this->loadView('dashboard');
    }
    
    private function handleTrack() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->processTrack();
        }
        return $this->loadView('track');
    }
    
    private function processTrack() {
        $transferCode = $_POST['transfer_code'] ?? '';
        
        if (empty($transferCode)) {
            return $this->jsonResponse(['success' => false, 'message' => 'رمز التحويل مطلوب']);
        }
        
        $stmt = $this->db->prepare("
            SELECT t.*, 
                   sc.name_ar as sender_country, 
                   rc.name_ar as receiver_country,
                   sc.currency_code as sender_currency,
                   rc.currency_code as receiver_currency
            FROM transfers t 
            LEFT JOIN countries sc ON t.sender_country_id = sc.id 
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id 
            WHERE t.transfer_code = ?
        ");
        $stmt->execute([$transferCode]);
        $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($transfer) {
            return $this->jsonResponse(['success' => true, 'data' => $transfer]);
        }
        
        return $this->jsonResponse(['success' => false, 'message' => 'التحويل غير موجود']);
    }
    
    private function handleApiLogin() {
        header('Content-Type: application/json');
        return $this->processLogin();
    }
    
    private function handleApiTrack() {
        header('Content-Type: application/json');
        return $this->processTrack();
    }

    private function handleLogout() {
        session_destroy();
        session_start();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->jsonResponse(['success' => true, 'message' => 'تم تسجيل الخروج بنجاح']);
        } else {
            header('Location: /');
            exit;
        }
    }

    private function handleTransferCreate() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: /login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->processTransferCreate();
        }

        return $this->loadView('transfer-create');
    }

    private function processTransferCreate() {
        $userId = $_SESSION['user_id'];
        $transferCode = 'TRF' . date('Ymd') . strtoupper(substr(uniqid(), -6));
        $pickupCode = 'PK' . rand(100000, 999999);

        // Get form data
        $senderCountryId = $_POST['sender_country'] ?? '';
        $receiverCountryId = $_POST['receiver_country'] ?? '';
        $amount = floatval($_POST['amount'] ?? 0);
        $receiverName = $_POST['receiver_name'] ?? '';
        $receiverPhone = $_POST['receiver_phone'] ?? '';
        $receiverAddress = $_POST['receiver_address'] ?? '';
        $purpose = $_POST['purpose'] ?? '';
        $notes = $_POST['notes'] ?? '';

        // Validate required fields
        if (empty($senderCountryId) || empty($receiverCountryId) || $amount <= 0 || empty($receiverName) || empty($receiverPhone)) {
            return $this->jsonResponse(['success' => false, 'message' => 'جميع الحقول المطلوبة يجب ملؤها']);
        }

        // Get currency codes for countries
        $senderCurrency = $this->getCurrencyForCountry($senderCountryId);
        $receiverCurrency = $this->getCurrencyForCountry($receiverCountryId);

        // Get real-time exchange rate
        try {
            $exchangeRateResult = $this->exchangeRateService->getExchangeRate($senderCurrency, $receiverCurrency, $amount);
            $exchangeRate = $exchangeRateResult['rate'];
            $convertedAmount = $exchangeRateResult['converted_amount'];
        } catch (Exception $e) {
            // Fallback to static rate
            $exchangeRate = $this->getExchangeRate($senderCountryId, $receiverCountryId);
            $convertedAmount = $amount * $exchangeRate;
        }

        // Calculate fees using real exchange rate service
        try {
            $feeCalculation = $this->exchangeRateService->calculateTransferFees(
                $amount,
                $this->getCountryCode($senderCountryId),
                $this->getCountryCode($receiverCountryId),
                $senderCurrency,
                $receiverCurrency
            );
            $feeAmount = $feeCalculation['total_fee'];
        } catch (Exception $e) {
            // Fallback to simple calculation
            $feeAmount = ($amount * 0.025) + 5.00;
        }

        $totalAmount = $amount + $feeAmount;

        // Get user info
        $stmt = $this->db->prepare("SELECT name, phone FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Prepare transfer data for fraud analysis
        $transferDataForAnalysis = [
            'sender_id' => $userId,
            'sender_country_id' => $senderCountryId,
            'receiver_country_id' => $receiverCountryId,
            'amount' => $amount,
            'receiver_phone' => $receiverPhone,
            'purpose' => $purpose
        ];

        // Perform AML/KYC checks
        $amlCheck = $this->complianceService->performAMLCheck($transferDataForAnalysis);
        $kycCheck = $this->complianceService->performKYCCheck($userId, $amount);

        // Check if transfer should be blocked due to compliance
        if ($amlCheck['should_block'] || !$kycCheck['is_sufficient']) {
            $message = 'تم رفض التحويل لأسباب تنظيمية.';
            if (!$kycCheck['is_sufficient']) {
                $message .= ' يرجى إكمال التحقق من الهوية.';
            }

            return $this->jsonResponse([
                'success' => false,
                'message' => $message,
                'requires_kyc' => !$kycCheck['is_sufficient'],
                'kyc_requirements' => $kycCheck['missing_documents'] ?? [],
                'requires_review' => $amlCheck['requires_review']
            ]);
        }

        // Analyze for fraud
        $fraudAnalysis = $this->fraudDetectionService->analyzeTransfer($transferDataForAnalysis);

        // Check if transfer should be blocked
        if ($fraudAnalysis['should_block']) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'تم رفض التحويل لأسباب أمنية. يرجى التواصل مع الدعم.',
                'requires_review' => true
            ]);
        }

        // Determine initial status based on risk levels
        $requiresReview = $amlCheck['requires_review'] || $fraudAnalysis['requires_review'];
        $initialStatus = $requiresReview ? 'pending_review' : 'pending_payment';

        try {
            // Insert transfer
            $stmt = $this->db->prepare("
                INSERT INTO transfers (
                    transfer_code, sender_id, sender_country_id, receiver_country_id,
                    amount, exchange_rate, converted_amount, fee_amount, total_amount,
                    status, pickup_code, receiver_name, receiver_phone, receiver_address,
                    sender_name, sender_phone, purpose, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $transferCode, $userId, $senderCountryId, $receiverCountryId,
                $amount, $exchangeRate, $convertedAmount, $feeAmount, $totalAmount,
                $initialStatus, $pickupCode, $receiverName, $receiverPhone, $receiverAddress,
                $user['name'], $user['phone'], $purpose, $notes
            ]);

            $transferId = $this->db->lastInsertId();

            // Send notifications
            $transferData = [
                'transfer_id' => $transferId,
                'transfer_code' => $transferCode,
                'pickup_code' => $pickupCode,
                'sender_name' => $user['name'],
                'sender_phone' => $user['phone'],
                'sender_email' => $_SESSION['user_email'] ?? '',
                'receiver_name' => $receiverName,
                'receiver_phone' => $receiverPhone,
                'amount' => $amount
            ];

            $this->notificationService->sendTransferCreatedNotification($transferData);

            // Log audit
            $this->logAudit($userId, 'create_transfer', 'transfers', $transferId, null, $transferData);

            // Update user risk profile
            $this->fraudDetectionService->updateUserRiskProfile($userId);

            // Invalidate user cache
            $this->cacheService->invalidateUserCache($userId);

            $message = 'تم إنشاء التحويل بنجاح وإرسال الإشعارات';
            if ($fraudAnalysis['requires_review']) {
                $message .= ' - التحويل قيد المراجعة الأمنية';
            }

            return $this->jsonResponse([
                'success' => true,
                'message' => $message,
                'data' => [
                    'transfer_code' => $transferCode,
                    'pickup_code' => $pickupCode,
                    'amount' => $amount,
                    'fee_amount' => $feeAmount,
                    'total_amount' => $totalAmount,
                    'converted_amount' => $convertedAmount,
                    'status' => $initialStatus,
                    'risk_level' => $fraudAnalysis['risk_level'],
                    'requires_review' => $fraudAnalysis['requires_review']
                ]
            ]);

        } catch (Exception $e) {
            return $this->jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء إنشاء التحويل']);
        }
    }

    private function getExchangeRate($fromCountryId, $toCountryId) {
        // Simplified exchange rates
        $rates = [
            '1-2' => 1.02, // SAR to AED
            '1-3' => 8.25, // SAR to EGP
            '2-3' => 8.08, // AED to EGP
            '6-1' => 3.75, // USD to SAR
            '6-2' => 3.67, // USD to AED
            '6-3' => 30.90, // USD to EGP
        ];

        return $rates["{$fromCountryId}-{$toCountryId}"] ?? 1.0;
    }

    private function getCurrencyForCountry($countryId) {
        $currencies = [
            '1' => 'SAR', // Saudi Arabia
            '2' => 'AED', // UAE
            '3' => 'EGP', // Egypt
            '4' => 'KWD', // Kuwait
            '5' => 'JOD', // Jordan
            '6' => 'USD'  // United States
        ];

        return $currencies[$countryId] ?? 'USD';
    }

    private function getCountryCode($countryId) {
        $codes = [
            '1' => 'SA', // Saudi Arabia
            '2' => 'AE', // UAE
            '3' => 'EG', // Egypt
            '4' => 'KW', // Kuwait
            '5' => 'JO', // Jordan
            '6' => 'US'  // United States
        ];

        return $codes[$countryId] ?? 'US';
    }

    private function logAudit($userId, $action, $tableName, $recordId, $oldValues, $newValues) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                $action,
                $tableName,
                $recordId,
                json_encode($oldValues),
                json_encode($newValues),
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            error_log("Failed to log audit: " . $e->getMessage());
        }
    }

    private function handleDashboardStats() {
        if (!isset($_SESSION['user_id'])) {
            return $this->jsonResponse(['success' => false, 'message' => 'غير مصرح']);
        }

        $userId = $_SESSION['user_id'];
        $role = $_SESSION['user_role'];

        $stats = $this->reportService->getTransferStats($userId, $role);

        return $this->jsonResponse(['success' => true, 'data' => $stats]);
    }

    private function handleChartData() {
        if (!isset($_SESSION['user_id'])) {
            return $this->jsonResponse(['success' => false, 'message' => 'غير مصرح']);
        }

        $userId = $_SESSION['user_id'];
        $role = $_SESSION['user_role'];
        $period = $_GET['period'] ?? '7days';

        $chartData = $this->reportService->getChartData($userId, $role, $period);

        return $this->jsonResponse(['success' => true, 'data' => $chartData]);
    }

    private function handleReports() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: /login');
            exit;
        }

        if ($_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'agent') {
            header('Location: /dashboard');
            exit;
        }

        return $this->loadView('reports');
    }

    private function handleSystemHealth() {
        header('Content-Type: application/json');

        $health = $this->performanceMonitor->checkSystemHealth();

        return $this->jsonResponse([
            'success' => true,
            'data' => $health
        ]);
    }

    private function handlePerformanceStats() {
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
            return $this->jsonResponse(['success' => false, 'message' => 'غير مصرح']);
        }

        header('Content-Type: application/json');

        $stats = $this->performanceMonitor->getSystemStats();

        return $this->jsonResponse([
            'success' => true,
            'data' => $stats
        ]);
    }

    private function handleMonitoring() {
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
            header('Location: /dashboard');
            exit;
        }

        return $this->loadView('monitoring');
    }

    private function handleCreatePaymentIntent() {
        if (!isset($_SESSION['user_id'])) {
            return $this->jsonResponse(['success' => false, 'message' => 'غير مصرح']);
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $transferId = $input['transfer_id'] ?? null;
        $amount = $input['amount'] ?? null;
        $currency = $input['currency'] ?? 'USD';
        $paymentMethod = $input['payment_method'] ?? 'stripe';

        if (!$transferId || !$amount) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'معاملات مطلوبة مفقودة'
            ]);
        }

        $result = $this->paymentService->createPaymentIntent($transferId, $amount, $currency, $paymentMethod);

        return $this->jsonResponse($result);
    }

    private function handleConfirmPayment() {
        if (!isset($_SESSION['user_id'])) {
            return $this->jsonResponse(['success' => false, 'message' => 'غير مصرح']);
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $paymentIntentId = $input['payment_intent_id'] ?? null;
        $paymentMethod = $input['payment_method'] ?? 'stripe';

        if (!$paymentIntentId) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'معرف الدفع مطلوب'
            ]);
        }

        $result = $this->paymentService->confirmPayment($paymentIntentId, $paymentMethod);

        return $this->jsonResponse($result);
    }

    private function handleSubmitKYC() {
        if (!isset($_SESSION['user_id'])) {
            return $this->jsonResponse(['success' => false, 'message' => 'غير مصرح']);
        }

        $userId = $_SESSION['user_id'];
        $documents = $_POST['documents'] ?? [];
        $personalInfo = $_POST['personal_info'] ?? [];

        $result = $this->complianceService->submitKYCDocuments($userId, $documents, $personalInfo);

        return $this->jsonResponse($result);
    }

    private function handlePaymentWebhook() {
        $provider = $_GET['provider'] ?? 'stripe';
        $payload = file_get_contents('php://input');
        $signature = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? null;

        $result = $this->paymentService->handleWebhook($provider, $payload, $signature);

        return $this->jsonResponse($result);
    }

    private function handleComplianceDashboard() {
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
            header('Location: /dashboard');
            exit;
        }

        return $this->loadView('compliance_dashboard');
    }
    
    private function loadView($view) {
        $viewFile = __DIR__ . "/../public/{$view}.php";
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            echo "View not found: {$view}";
        }
    }
    
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
    }
}

return new SimpleApp();
