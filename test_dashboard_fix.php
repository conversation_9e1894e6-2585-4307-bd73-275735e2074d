<?php

echo "🔧 Testing Dashboard Fixes\n\n";

try {
    // Test database connection
    $db = new PDO('sqlite:database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "✅ Database connection successful\n";
    
    // Test user data retrieval
    $users = $db->query("SELECT * FROM users WHERE deleted_at IS NULL LIMIT 5")->fetchAll();
    echo "✅ Found " . count($users) . " users\n";
    
    foreach ($users as $user) {
        echo "  - {$user['name']} ({$user['email']}) - Role: {$user['role']}\n";
        echo "    Last login: " . ($user['last_login_at'] ?? 'Never') . "\n";
    }
    
    // Test session helper functions
    echo "\n🧪 Testing session helper functions:\n";
    
    // Simulate session data
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['name'] = 'Test User';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'admin';
    $_SESSION['last_login_at'] = date('Y-m-d H:i:s');
    
    // Include session helper
    require_once 'public/includes/session_helper.php';
    
    // Test get_user_data function
    $userData = get_user_data();
    echo "✅ get_user_data() works:\n";
    echo "  - ID: " . ($userData['id'] ?? 'null') . "\n";
    echo "  - Name: " . ($userData['name'] ?? 'null') . "\n";
    echo "  - Email: " . ($userData['email'] ?? 'null') . "\n";
    echo "  - Role: " . ($userData['role'] ?? 'null') . "\n";
    echo "  - Last Login: " . ($userData['last_login_at'] ?? 'null') . "\n";
    
    // Test is_logged_in function
    echo "✅ is_logged_in(): " . (is_logged_in() ? 'true' : 'false') . "\n";
    
    // Test statistics function
    echo "\n📊 Testing statistics:\n";
    
    $totalUsers = $db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn();
    $totalTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn();
    $totalCountries = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    
    echo "✅ Statistics:\n";
    echo "  - Total Users: $totalUsers\n";
    echo "  - Total Transfers: $totalTransfers\n";
    echo "  - Total Countries: $totalCountries\n";
    
    // Test role-based data access
    echo "\n🔐 Testing role-based access:\n";
    
    $roles = ['admin', 'manager', 'agent', 'compliance', 'customer'];
    foreach ($roles as $role) {
        $userCount = $db->query("SELECT COUNT(*) FROM users WHERE role = '$role' AND deleted_at IS NULL")->fetchColumn();
        echo "  - $role: $userCount users\n";
    }
    
    echo "\n🎉 All tests passed! Dashboard should work correctly now.\n";
    echo "🔗 Try accessing: http://localhost:8000/login\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
