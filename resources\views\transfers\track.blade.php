@extends('layouts.app')

@section('title', 'Track Transfer - Elite Financial Transfer System')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h2 class="text-white mb-3">
                    <i class="bi bi-search me-2"></i>
                    {{ __('Track Your Transfer') }}
                </h2>
                <p class="text-white-50">{{ __('Enter your transfer code to track the status of your money transfer') }}</p>
            </div>

            <div class="card">
                <div class="card-body p-5">
                    <form id="trackingForm">
                        @csrf
                        <div class="mb-4">
                            <label for="transfer_code" class="form-label">
                                <i class="bi bi-qr-code me-1"></i>
                                {{ __('Transfer Code') }}
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg text-center" 
                                   id="transfer_code" 
                                   name="transfer_code" 
                                   placeholder="TRF20240125ABC123"
                                   style="font-size: 1.2rem; letter-spacing: 2px;"
                                   required>
                            <div class="form-text">
                                {{ __('Enter the transfer code provided when you sent the money') }}
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" data-original-text="{{ __('Track Transfer') }}">
                                <i class="bi bi-search me-2"></i>
                                {{ __('Track Transfer') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tracking Results -->
            <div id="trackingResults" class="d-none mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            {{ __('Transfer Information') }}
                        </h5>
                    </div>
                    <div class="card-body" id="transferInfo">
                        <!-- Transfer info will be populated here -->
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            {{ __('Transfer Timeline') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="transferTimeline">
                            <!-- Timeline will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-question-circle me-2"></i>
                        {{ __('Need Help?') }}
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ __('Where to find your transfer code?') }}</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check text-success me-2"></i>{{ __('Email confirmation') }}</li>
                                <li><i class="bi bi-check text-success me-2"></i>{{ __('SMS notification') }}</li>
                                <li><i class="bi bi-check text-success me-2"></i>{{ __('Receipt from agent') }}</li>
                                <li><i class="bi bi-check text-success me-2"></i>{{ __('Your account dashboard') }}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ __('Transfer Status Meanings') }}</h6>
                            <ul class="list-unstyled">
                                <li><span class="status-badge status-pending me-2">Pending</span>{{ __('Being processed') }}</li>
                                <li><span class="status-badge status-processing me-2">Processing</span>{{ __('In progress') }}</li>
                                <li><span class="status-badge status-ready-for-pickup me-2">Ready</span>{{ __('Ready for pickup') }}</li>
                                <li><span class="status-badge status-completed me-2">Completed</span>{{ __('Successfully delivered') }}</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <p class="mb-2">{{ __('Still need help? Contact our support team') }}</p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="tel:+1234567890" class="btn btn-outline-primary">
                                <i class="bi bi-telephone me-2"></i>
                                {{ __('Call Support') }}
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                                <i class="bi bi-envelope me-2"></i>
                                {{ __('Email Support') }}
                            </a>
                            <button class="btn btn-outline-primary" onclick="openLiveChat()">
                                <i class="bi bi-chat-dots me-2"></i>
                                {{ __('Live Chat') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.getElementById('trackingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    setLoading(submitBtn, true);
    
    axios.post('{{ route("track.transfer") }}', formData)
        .then(response => {
            if (response.data.success) {
                displayTrackingResults(response.data.data);
                document.getElementById('trackingResults').classList.remove('d-none');
                
                // Scroll to results
                document.getElementById('trackingResults').scrollIntoView({ 
                    behavior: 'smooth' 
                });
            } else {
                showNotification(response.data.message, 'error');
            }
        })
        .catch(error => {
            const message = error.response?.data?.message || 'Transfer not found or an error occurred.';
            showNotification(message, 'error');
        })
        .finally(() => {
            setLoading(submitBtn, false);
        });
});

function displayTrackingResults(data) {
    // Display transfer information
    const transferInfo = `
        <div class="row">
            <div class="col-md-6">
                <h6>{{ __('Transfer Details') }}</h6>
                <p><strong>{{ __('Transfer Code:') }}</strong> ${data.transfer_code}</p>
                <p><strong>{{ __('Status:') }}</strong> <span class="status-badge status-${data.status}">${data.status}</span></p>
                <p><strong>{{ __('Amount Sent:') }}</strong> ${data.amount} ${data.sender_currency}</p>
                <p><strong>{{ __('Amount to Receive:') }}</strong> ${data.converted_amount} ${data.receiver_currency}</p>
                <p><strong>{{ __('Created:') }}</strong> ${new Date(data.created_at).toLocaleString()}</p>
                ${data.completed_at ? `<p><strong>{{ __('Completed:') }}</strong> ${new Date(data.completed_at).toLocaleString()}</p>` : ''}
            </div>
            <div class="col-md-6">
                <h6>{{ __('Transfer Route') }}</h6>
                <p><strong>{{ __('From:') }}</strong> ${data.sender_country}</p>
                <p><strong>{{ __('To:') }}</strong> ${data.receiver_country}</p>
                <p><strong>{{ __('Receiver:') }}</strong> ${data.receiver_name}</p>
                
                ${data.status === 'ready_for_pickup' || data.status === 'completed' ? `
                    <div class="alert alert-info mt-3">
                        <h6><i class="bi bi-info-circle me-2"></i>{{ __('Pickup Information') }}</h6>
                        <p class="mb-1"><strong>{{ __('Pickup Code:') }}</strong> <code>${data.pickup_code || 'N/A'}</code></p>
                        <p class="mb-0"><small>{{ __('Present this code and valid ID to collect the money') }}</small></p>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
    
    document.getElementById('transferInfo').innerHTML = transferInfo;
    
    // Display timeline
    const timeline = data.timeline.map((item, index) => {
        const isLast = index === data.timeline.length - 1;
        return `
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <div class="timeline-icon ${isLast ? 'bg-primary' : 'bg-secondary'}">
                        <i class="bi bi-${getStatusIcon(item.status)} text-white"></i>
                    </div>
                    ${!isLast ? '<div class="timeline-line"></div>' : ''}
                </div>
                <div class="flex-grow-1 ms-3">
                    <div class="timeline-content">
                        <h6 class="mb-1">${formatStatus(item.status)}</h6>
                        <p class="text-muted mb-1">${new Date(item.timestamp).toLocaleString()}</p>
                        ${item.notes ? `<p class="mb-0"><small>${item.notes}</small></p>` : ''}
                        ${item.updated_by ? `<p class="mb-0"><small>{{ __('Updated by:') }} ${item.updated_by}</small></p>` : ''}
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    document.getElementById('transferTimeline').innerHTML = timeline;
}

function getStatusIcon(status) {
    const icons = {
        'pending': 'clock',
        'processing': 'gear',
        'ready_for_pickup': 'check-circle',
        'completed': 'check-circle-fill',
        'cancelled': 'x-circle',
        'failed': 'exclamation-triangle'
    };
    return icons[status] || 'circle';
}

function formatStatus(status) {
    const statuses = {
        'pending': '{{ __("Pending") }}',
        'processing': '{{ __("Processing") }}',
        'ready_for_pickup': '{{ __("Ready for Pickup") }}',
        'completed': '{{ __("Completed") }}',
        'cancelled': '{{ __("Cancelled") }}',
        'failed': '{{ __("Failed") }}'
    };
    return statuses[status] || status;
}

function openLiveChat() {
    // Implement live chat functionality
    showNotification('Live chat feature coming soon!', 'info');
}

// Auto-format transfer code input
document.getElementById('transfer_code').addEventListener('input', function(e) {
    let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    e.target.value = value;
});
</script>

<style>
.timeline-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.timeline-line {
    width: 2px;
    height: 60px;
    background-color: #dee2e6;
    margin: 5px auto 0;
}

.timeline-content {
    padding-bottom: 30px;
}

.status-ready-for-pickup {
    background: #0dcaf0;
    color: white;
}
</style>
@endpush
