# دليل التثبيت - Elite Financial Transfer System v6.0

## 🚀 التثبيت السريع (Windows)

### الطريقة الأولى: التثبيت التلقائي
1. تأكد من تثبيت PHP 8.2+ و Composer
2. شغ<PERSON> ملف `setup.bat`
3. شغ<PERSON> ملف `run.bat` لبدء الخادم

### الطريقة الثانية: التثبيت اليدوي
```cmd
# 1. تثبيت المكتبات
composer install

# 2. إعداد البيئة
copy .env.example .env
php artisan key:generate

# 3. إعداد قاعدة البيانات
echo. > database\database.sqlite
php artisan migrate --seed

# 4. تشغيل الخادم
php artisan serve
```

## 🐧 التثبيت السريع (Linux/Mac)

### الطريقة الأولى: التثبيت التلقائي
```bash
# إعطاء صلاحيات التنفيذ
chmod +x setup.sh run.sh

# تشغيل الإعداد
./setup.sh

# تشغيل الخادم
./run.sh
```

### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. تثبيت المكتبات
composer install

# 2. إعداد البيئة
cp .env.example .env
php artisan key:generate

# 3. إعداد قاعدة البيانات
touch database/database.sqlite
php artisan migrate --seed

# 4. تشغيل الخادم
php artisan serve
```

## 📋 المتطلبات الأساسية

### متطلبات الخادم
- **PHP**: 8.2 أو أحدث
- **Extensions**: 
  - OpenSSL
  - PDO
  - Mbstring
  - Tokenizer
  - XML
  - Ctype
  - JSON
  - BCMath
  - SQLite3

### أدوات التطوير
- **Composer**: لإدارة حزم PHP
- **Node.js**: 16.0+ (اختياري للتطوير)
- **NPM**: 8.0+ (اختياري للتطوير)

## 🔧 الإعدادات المتقدمة

### إعداد قاعدة البيانات
```env
# SQLite (الافتراضي)
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite

# MySQL (اختياري)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=elite_transfer
DB_USERNAME=root
DB_PASSWORD=
```

### إعداد البريد الإلكتروني
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Elite Transfer System"
```

### إعداد الرسائل النصية (اختياري)
```env
# Twilio
SMS_PROVIDER=twilio
TWILIO_SID=your-twilio-sid
TWILIO_TOKEN=your-twilio-token
TWILIO_FROM=+**********

# أو Nexmo/Vonage
SMS_PROVIDER=nexmo
NEXMO_KEY=your-nexmo-key
NEXMO_SECRET=your-nexmo-secret
NEXMO_FROM=EliteTransfer
```

## 👥 الحسابات الافتراضية

بعد تشغيل `php artisan db:seed`، ستتوفر الحسابات التالية:

### مدير النظام الرئيسي
- **البريد**: <EMAIL>
- **كلمة المرور**: password123
- **الصلاحيات**: جميع الصلاحيات

### مدير الفرع
- **البريد**: <EMAIL>
- **كلمة المرور**: password123
- **الفرع**: الرياض

### الوكيل
- **البريد**: <EMAIL>
- **كلمة المرور**: password123
- **الفرع**: الرياض
- **رمز الوكيل**: AG000002

### العميل
- **البريد**: <EMAIL>
- **كلمة المرور**: password123
- **الحد اليومي**: $5,000

## 🌐 الوصول للنظام

بعد التثبيت، يمكن الوصول للنظام عبر:
- **الموقع الرئيسي**: http://localhost:8000
- **لوحة الإدارة**: http://localhost:8000/admin/dashboard
- **لوحة الوكيل**: http://localhost:8000/agent/dashboard
- **API**: http://localhost:8000/api/v1/

## 🔍 اختبار النظام

### اختبار التحويل الأساسي
1. سجل دخول كعميل
2. اذهب إلى "Send Money"
3. املأ بيانات التحويل
4. أكمل العملية
5. تتبع التحويل برقم التحويل

### اختبار لوحة الإدارة
1. سجل دخول كمدير
2. اذهب إلى Admin Panel
3. تصفح التحويلات والمستخدمين
4. راجع التقارير

### اختبار واجهة الوكيل
1. سجل دخول كوكيل
2. اذهب إلى Agent Panel
3. عالج التحويلات المعلقة
4. أكمل عمليات الاستلام

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ "Class not found"
```bash
composer dump-autoload
php artisan config:clear
php artisan cache:clear
```

#### خطأ قاعدة البيانات
```bash
# تأكد من وجود ملف قاعدة البيانات
touch database/database.sqlite

# أعد تشغيل الهجرات
php artisan migrate:fresh --seed
```

#### خطأ الصلاحيات (Linux/Mac)
```bash
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

#### خطأ مفتاح التطبيق
```bash
php artisan key:generate --force
```

### سجلات الأخطاء
- **Laravel Logs**: `storage/logs/laravel.log`
- **Web Server Logs**: حسب نوع الخادم
- **Database Logs**: في ملف قاعدة البيانات

## 📊 مراقبة الأداء

### أوامر مفيدة للمراقبة
```bash
# عرض حالة النظام
php artisan about

# تنظيف الكاش
php artisan optimize:clear

# عرض الطرق المتاحة
php artisan route:list

# عرض المهام المجدولة
php artisan schedule:list
```

### مؤشرات الأداء
- **استخدام الذاكرة**: مراقبة عبر `memory_get_usage()`
- **وقت الاستجابة**: مراقبة عبر Laravel Telescope (اختياري)
- **حجم قاعدة البيانات**: مراقبة ملف SQLite

## 🔒 الأمان والحماية

### إعدادات الأمان الموصى بها
```env
# تفعيل HTTPS في الإنتاج
APP_ENV=production
APP_DEBUG=false

# تقوية كلمات المرور
BCRYPT_ROUNDS=12

# حماية من الهجمات
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
```

### نصائح أمنية
1. غير كلمات المرور الافتراضية
2. فعل SSL/TLS في الإنتاج
3. راجع سجلات المراجعة بانتظام
4. حدث النظام بانتظام
5. استخدم جدار حماية قوي

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. راجع ملف `storage/logs/laravel.log`
2. تأكد من تطبيق جميع خطوات التثبيت
3. راجع متطلبات النظام
4. تواصل مع فريق الدعم

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://elitetransfer.com
- **التوثيق**: https://docs.elitetransfer.com

---

**نظام Elite Financial Transfer System v6.0** - نظام التحويلات المالية الأكثر تطوراً وأماناً 🚀
