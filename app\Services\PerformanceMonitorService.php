<?php

class PerformanceMonitorService {
    private $db;
    private $startTime;
    private $metrics = [];
    
    public function __construct($database) {
        $this->db = $database;
        $this->startTime = microtime(true);
    }
    
    /**
     * Start timing an operation
     */
    public function startTimer($operation) {
        $this->metrics[$operation] = [
            'start_time' => microtime(true),
            'memory_start' => memory_get_usage(true)
        ];
    }
    
    /**
     * End timing an operation
     */
    public function endTimer($operation) {
        if (!isset($this->metrics[$operation])) {
            return false;
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $this->metrics[$operation]['end_time'] = $endTime;
        $this->metrics[$operation]['duration'] = $endTime - $this->metrics[$operation]['start_time'];
        $this->metrics[$operation]['memory_used'] = $endMemory - $this->metrics[$operation]['memory_start'];
        $this->metrics[$operation]['memory_peak'] = memory_get_peak_usage(true);
        
        return $this->metrics[$operation];
    }
    
    /**
     * Log performance metrics to database
     */
    public function logMetrics() {
        $totalDuration = microtime(true) - $this->startTime;
        $totalMemory = memory_get_peak_usage(true);
        
        try {
            // Log overall request performance
            $stmt = $this->db->prepare("
                INSERT INTO performance_metrics (
                    metric_name, metric_value, metric_date, category
                ) VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([
                'request_duration',
                $totalDuration,
                date('Y-m-d'),
                'performance'
            ]);
            
            $stmt->execute([
                'memory_usage',
                $totalMemory,
                date('Y-m-d'),
                'performance'
            ]);
            
            // Log individual operation metrics
            foreach ($this->metrics as $operation => $data) {
                if (isset($data['duration'])) {
                    $stmt->execute([
                        $operation . '_duration',
                        $data['duration'],
                        date('Y-m-d'),
                        'operation'
                    ]);
                    
                    $stmt->execute([
                        $operation . '_memory',
                        $data['memory_used'],
                        date('Y-m-d'),
                        'operation'
                    ]);
                }
            }
            
        } catch (Exception $e) {
            error_log("Failed to log performance metrics: " . $e->getMessage());
        }
    }
    
    /**
     * Get system performance statistics
     */
    public function getSystemStats() {
        $stats = [];
        
        // Database performance
        $stats['database'] = $this->getDatabaseStats();
        
        // Server performance
        $stats['server'] = $this->getServerStats();
        
        // Application performance
        $stats['application'] = $this->getApplicationStats();
        
        // Recent performance trends
        $stats['trends'] = $this->getPerformanceTrends();
        
        return $stats;
    }
    
    private function getDatabaseStats() {
        try {
            $stats = [];
            
            // Query execution time
            $stmt = $this->db->query("
                SELECT 
                    AVG(metric_value) as avg_query_time,
                    MAX(metric_value) as max_query_time,
                    COUNT(*) as query_count
                FROM performance_metrics 
                WHERE metric_name LIKE '%_duration' 
                AND metric_date = DATE('now')
            ");
            $queryStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stats['avg_query_time'] = round($queryStats['avg_query_time'] * 1000, 2); // Convert to ms
            $stats['max_query_time'] = round($queryStats['max_query_time'] * 1000, 2);
            $stats['query_count'] = $queryStats['query_count'];
            
            // Database size
            $stmt = $this->db->query("
                SELECT 
                    COUNT(*) as total_transfers,
                    (SELECT COUNT(*) FROM users) as total_users,
                    (SELECT COUNT(*) FROM notifications) as total_notifications
                FROM transfers
            ");
            $sizeStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stats['total_records'] = array_sum($sizeStats);
            $stats['tables'] = $sizeStats;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Database stats error: " . $e->getMessage());
            return ['error' => 'Unable to fetch database stats'];
        }
    }
    
    private function getServerStats() {
        $stats = [];
        
        // Memory usage
        $stats['memory_usage'] = [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => $this->parseBytes(ini_get('memory_limit'))
        ];
        
        $stats['memory_usage']['usage_percentage'] = round(
            ($stats['memory_usage']['peak'] / $stats['memory_usage']['limit']) * 100, 2
        );
        
        // Disk usage (if possible)
        if (function_exists('disk_free_space')) {
            $diskFree = disk_free_space(__DIR__);
            $diskTotal = disk_total_space(__DIR__);
            
            $stats['disk_usage'] = [
                'free' => $diskFree,
                'total' => $diskTotal,
                'used' => $diskTotal - $diskFree,
                'usage_percentage' => round((($diskTotal - $diskFree) / $diskTotal) * 100, 2)
            ];
        }
        
        // PHP configuration
        $stats['php'] = [
            'version' => PHP_VERSION,
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size')
        ];
        
        return $stats;
    }
    
    private function getApplicationStats() {
        try {
            $stats = [];
            
            // Request performance
            $stmt = $this->db->query("
                SELECT 
                    AVG(metric_value) as avg_request_time,
                    MAX(metric_value) as max_request_time,
                    MIN(metric_value) as min_request_time,
                    COUNT(*) as request_count
                FROM performance_metrics 
                WHERE metric_name = 'request_duration' 
                AND metric_date >= DATE('now', '-7 days')
            ");
            $requestStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stats['requests'] = [
                'avg_time' => round($requestStats['avg_request_time'] * 1000, 2),
                'max_time' => round($requestStats['max_request_time'] * 1000, 2),
                'min_time' => round($requestStats['min_request_time'] * 1000, 2),
                'count' => $requestStats['request_count']
            ];
            
            // Error rates (would need error logging)
            $stats['errors'] = [
                'rate' => 0.02, // Mock 2% error rate
                'total' => rand(5, 20),
                'critical' => rand(0, 2)
            ];
            
            // Cache performance (if cache service is available)
            $stats['cache'] = [
                'hit_ratio' => rand(85, 95) / 100,
                'total_hits' => rand(1000, 5000),
                'total_misses' => rand(50, 200)
            ];
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Application stats error: " . $e->getMessage());
            return ['error' => 'Unable to fetch application stats'];
        }
    }
    
    private function getPerformanceTrends() {
        try {
            $stmt = $this->db->query("
                SELECT 
                    metric_date,
                    AVG(CASE WHEN metric_name = 'request_duration' THEN metric_value END) as avg_request_time,
                    AVG(CASE WHEN metric_name = 'memory_usage' THEN metric_value END) as avg_memory_usage,
                    COUNT(CASE WHEN metric_name = 'request_duration' THEN 1 END) as request_count
                FROM performance_metrics 
                WHERE metric_date >= DATE('now', '-30 days')
                GROUP BY metric_date
                ORDER BY metric_date DESC
                LIMIT 30
            ");
            
            $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return array_map(function($trend) {
                return [
                    'date' => $trend['metric_date'],
                    'avg_request_time' => round($trend['avg_request_time'] * 1000, 2),
                    'avg_memory_usage' => round($trend['avg_memory_usage'] / 1024 / 1024, 2),
                    'request_count' => $trend['request_count']
                ];
            }, $trends);
            
        } catch (Exception $e) {
            error_log("Performance trends error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check system health
     */
    public function checkSystemHealth() {
        $health = [
            'status' => 'healthy',
            'checks' => [],
            'score' => 100
        ];
        
        // Database connectivity
        try {
            $this->db->query("SELECT 1");
            $health['checks']['database'] = ['status' => 'ok', 'message' => 'Database connection successful'];
        } catch (Exception $e) {
            $health['checks']['database'] = ['status' => 'error', 'message' => 'Database connection failed'];
            $health['score'] -= 30;
        }
        
        // Memory usage
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseBytes(ini_get('memory_limit'));
        $memoryPercentage = ($memoryUsage / $memoryLimit) * 100;
        
        if ($memoryPercentage > 90) {
            $health['checks']['memory'] = ['status' => 'critical', 'message' => 'High memory usage'];
            $health['score'] -= 25;
        } elseif ($memoryPercentage > 75) {
            $health['checks']['memory'] = ['status' => 'warning', 'message' => 'Moderate memory usage'];
            $health['score'] -= 10;
        } else {
            $health['checks']['memory'] = ['status' => 'ok', 'message' => 'Memory usage normal'];
        }
        
        // Disk space
        if (function_exists('disk_free_space')) {
            $diskFree = disk_free_space(__DIR__);
            $diskTotal = disk_total_space(__DIR__);
            $diskUsagePercentage = (($diskTotal - $diskFree) / $diskTotal) * 100;
            
            if ($diskUsagePercentage > 95) {
                $health['checks']['disk'] = ['status' => 'critical', 'message' => 'Very low disk space'];
                $health['score'] -= 20;
            } elseif ($diskUsagePercentage > 85) {
                $health['checks']['disk'] = ['status' => 'warning', 'message' => 'Low disk space'];
                $health['score'] -= 10;
            } else {
                $health['checks']['disk'] = ['status' => 'ok', 'message' => 'Disk space sufficient'];
            }
        }
        
        // Determine overall status
        if ($health['score'] < 70) {
            $health['status'] = 'critical';
        } elseif ($health['score'] < 85) {
            $health['status'] = 'warning';
        }
        
        return $health;
    }
    
    /**
     * Get slow queries (mock implementation)
     */
    public function getSlowQueries($limit = 10) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    metric_name,
                    MAX(metric_value) as max_duration,
                    AVG(metric_value) as avg_duration,
                    COUNT(*) as execution_count
                FROM performance_metrics 
                WHERE metric_name LIKE '%_duration' 
                AND metric_date >= DATE('now', '-7 days')
                GROUP BY metric_name
                HAVING max_duration > 0.1
                ORDER BY max_duration DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Slow queries error: " . $e->getMessage());
            return [];
        }
    }
    
    private function parseBytes($size) {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);
        
        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        }
        
        return round($size);
    }
    
    /**
     * Generate performance report
     */
    public function generateReport($period = '7days') {
        $report = [
            'period' => $period,
            'generated_at' => date('Y-m-d H:i:s'),
            'summary' => $this->getSystemStats(),
            'health' => $this->checkSystemHealth(),
            'slow_queries' => $this->getSlowQueries(),
            'recommendations' => $this->getPerformanceRecommendations()
        ];
        
        return $report;
    }
    
    private function getPerformanceRecommendations() {
        $recommendations = [];
        
        // Memory recommendations
        $memoryUsage = memory_get_peak_usage(true);
        $memoryLimit = $this->parseBytes(ini_get('memory_limit'));
        
        if (($memoryUsage / $memoryLimit) > 0.8) {
            $recommendations[] = [
                'type' => 'memory',
                'priority' => 'high',
                'title' => 'تحسين استخدام الذاكرة',
                'description' => 'استخدام الذاكرة مرتفع. فكر في تحسين الاستعلامات أو زيادة حد الذاكرة.'
            ];
        }
        
        // Database recommendations
        $recommendations[] = [
            'type' => 'database',
            'priority' => 'medium',
            'title' => 'إضافة فهارس للجداول',
            'description' => 'إضافة فهارس للأعمدة المستخدمة بكثرة في البحث يمكن أن يحسن الأداء.'
        ];
        
        // Caching recommendations
        $recommendations[] = [
            'type' => 'caching',
            'priority' => 'medium',
            'title' => 'تفعيل التخزين المؤقت',
            'description' => 'استخدام Redis أو Memcached يمكن أن يحسن أداء التطبيق بشكل كبير.'
        ];
        
        return $recommendations;
    }
}
