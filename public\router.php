<?php
// Elite Transfer System - Built-in Server Router
// This file handles routing for PHP's built-in server

$uri = urldecode(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));

// Remove leading slash
$path = ltrim($uri, '/');

// Define routes
$routes = [
    '' => 'index.php',
    'home' => 'index.php',
    'login' => 'index.php',
    'logout' => 'index.php',
    'register' => 'index.php',
    'dashboard' => 'index.php',
    'create-transfer' => 'index.php',
    'track-transfer' => 'index.php',
    'transfers/create' => 'index.php',
    'transfers/track' => 'index.php',
    'transfers/view' => 'index.php',
    'admin/users' => 'index.php',
    'admin/transfers' => 'index.php',
    'admin/reports' => 'index.php',
    'admin/settings' => 'index.php',
    'admin/monitoring' => 'index.php',
    'compliance/dashboard' => 'index.php',
    'api' => 'index.php',
    'api-test' => 'index.php',
    'health' => 'index.php',
    'stats' => 'index.php',
    'countries' => 'index.php',
    'users' => 'index.php',
    'transfers' => 'index.php'
];

// Check if it's a static file
if (is_file(__DIR__ . $uri)) {
    return false; // Serve the file directly
}

// Check if route exists
if (array_key_exists($path, $routes)) {
    include __DIR__ . '/' . $routes[$path];
} else {
    // Route not found, let index.php handle it
    include __DIR__ . '/index.php';
}
?>
