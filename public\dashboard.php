<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

// Get user role from session or URL parameter
$role = $_SESSION['user_role'] ?? $_GET['role'] ?? 'customer';
$userName = $_SESSION['user_name'] ?? 'مستخدم';

// Define user data based on role
$userData = [
    'admin' => [
        'name' => $userName,
        'email' => $_SESSION['user_email'] ?? '<EMAIL>',
        'role_ar' => 'مدير النظام',
        'stats' => [
            ['title' => 'إجمالي التحويلات', 'value' => '15,234', 'icon' => 'bi-send', 'color' => 'primary'],
            ['title' => 'التحويلات اليوم', 'value' => '89', 'icon' => 'bi-calendar-day', 'color' => 'success'],
            ['title' => 'إجمالي المبلغ', 'value' => '$2.5M', 'icon' => 'bi-currency-dollar', 'color' => 'warning'],
            ['title' => 'الإيرادات الشهرية', 'value' => '$125K', 'icon' => 'bi-graph-up', 'color' => 'info']
        ]
    ],
    'agent' => [
        'name' => $userName,
        'email' => $_SESSION['user_email'] ?? '<EMAIL>',
        'role_ar' => 'وكيل',
        'stats' => [
            ['title' => 'تحويلاتي', 'value' => '234', 'icon' => 'bi-send', 'color' => 'primary'],
            ['title' => 'معلقة', 'value' => '12', 'icon' => 'bi-clock', 'color' => 'warning'],
            ['title' => 'مكتملة', 'value' => '198', 'icon' => 'bi-check-circle', 'color' => 'success'],
            ['title' => 'العمولة الشهرية', 'value' => '$2,450', 'icon' => 'bi-wallet', 'color' => 'info']
        ]
    ],
    'customer' => [
        'name' => $userName,
        'email' => $_SESSION['user_email'] ?? '<EMAIL>',
        'role_ar' => 'عميل',
        'stats' => [
            ['title' => 'التحويلات المرسلة', 'value' => '23', 'icon' => 'bi-send', 'color' => 'primary'],
            ['title' => 'التحويلات المستلمة', 'value' => '8', 'icon' => 'bi-inbox', 'color' => 'success'],
            ['title' => 'المبلغ المرسل', 'value' => '$12,500', 'icon' => 'bi-currency-dollar', 'color' => 'warning'],
            ['title' => 'الحد المتاح', 'value' => '$2,500', 'icon' => 'bi-speedometer', 'color' => 'info']
        ]
    ]
];

$user = $userData[$role];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-processing { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $user['name'] ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">
                            <i class="bi bi-person me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="bi bi-gear me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم
                        </h2>
                        <p class="text-white-50 mb-0">
                            أهلاً بك، <?= $user['name'] ?>! (<?= $user['role_ar'] ?>)
                        </p>
                    </div>
                    <?php if ($role === 'customer'): ?>
                    <div>
                        <a href="/transfers/create" class="btn btn-primary">
                            <i class="bi bi-send me-2"></i>
                            إرسال أموال
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <?php foreach ($user['stats'] as $stat): ?>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-<?= $stat['color'] ?>">
                    <i class="bi <?= $stat['icon'] ?> fs-1 mb-3"></i>
                    <h3><?= $stat['value'] ?></h3>
                    <p class="mb-0"><?= $stat['title'] ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="row">
            <!-- Quick Actions -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($role === 'admin'): ?>
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-primary">
                                <i class="bi bi-people me-2"></i>إدارة المستخدمين
                            </a>
                            <a href="#" class="btn btn-outline-success">
                                <i class="bi bi-list-ul me-2"></i>إدارة التحويلات
                            </a>
                            <a href="/reports" class="btn btn-outline-info">
                                <i class="bi bi-graph-up me-2"></i>التقارير
                            </a>
                            <a href="/admin/monitoring" class="btn btn-outline-warning">
                                <i class="bi bi-activity me-2"></i>مراقبة النظام
                            </a>
                            <a href="/compliance/dashboard" class="btn btn-outline-danger">
                                <i class="bi bi-shield-check me-2"></i>الامتثال التنظيمي
                            </a>
                        </div>
                        <?php elseif ($role === 'agent'): ?>
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-repeat me-2"></i>معالجة التحويلات
                            </a>
                            <a href="#" class="btn btn-outline-success">
                                <i class="bi bi-check-circle me-2"></i>جاهز للاستلام
                            </a>
                            <a href="#" class="btn btn-outline-info">
                                <i class="bi bi-person-plus me-2"></i>إضافة عميل
                            </a>
                            <a href="/reports" class="btn btn-outline-warning">
                                <i class="bi bi-file-text me-2"></i>التقرير اليومي
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="d-grid gap-2">
                            <a href="/transfers/create" class="btn btn-outline-primary">
                                <i class="bi bi-send me-2"></i>إرسال أموال
                            </a>
                            <a href="/track" class="btn btn-outline-success">
                                <i class="bi bi-search me-2"></i>تتبع التحويل
                            </a>
                            <a href="#" class="btn btn-outline-info">
                                <i class="bi bi-list-ul me-2"></i>تحويلاتي
                            </a>
                            <a href="#" class="btn btn-outline-warning">
                                <i class="bi bi-person-gear me-2"></i>إعدادات الملف الشخصي
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Activity Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            نشاط التحويلات
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="activityChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transfers -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            التحويلات الأخيرة
                        </h5>
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رمز التحويل</th>
                                        <th>من/إلى</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>TRF20250125001</strong></td>
                                        <td>السعودية → الإمارات</td>
                                        <td>$1,000.00</td>
                                        <td><span class="status-badge status-completed">مكتمل</span></td>
                                        <td>25 يناير 2025</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>TRF20250125002</strong></td>
                                        <td>الإمارات → مصر</td>
                                        <td>$750.00</td>
                                        <td><span class="status-badge status-processing">قيد المعالجة</span></td>
                                        <td>25 يناير 2025</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>TRF20250124003</strong></td>
                                        <td>الكويت → الأردن</td>
                                        <td>$500.00</td>
                                        <td><span class="status-badge status-pending">معلق</span></td>
                                        <td>24 يناير 2025</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Initialize activity chart with real data
        loadDashboardData();

        function loadDashboardData() {
            // Load dashboard statistics
            fetch('/api/dashboard-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateDashboardStats(data.data);
                    }
                })
                .catch(error => console.error('Error loading dashboard stats:', error));

            // Load chart data
            fetch('/api/chart-data?period=7days')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        initializeActivityChart(data.data);
                    }
                })
                .catch(error => console.error('Error loading chart data:', error));
        }

        function updateDashboardStats(stats) {
            // Update stats cards based on user role
            const role = '<?= $role ?>';

            if (role === 'admin') {
                updateStatCard(0, stats.total_transfers || '0');
                updateStatCard(1, stats.today_transfers || '0');
                updateStatCard(2, '$' + (stats.total_amount || '0'));
                updateStatCard(3, '$' + (stats.monthly_revenue || '0'));
            } else if (role === 'agent') {
                updateStatCard(0, stats.total_transfers || '0');
                updateStatCard(1, stats.pending_transfers || '0');
                updateStatCard(2, stats.completed_transfers || '0');
                updateStatCard(3, '$' + (stats.monthly_commission || '0'));
            } else {
                updateStatCard(0, stats.total_sent || '0');
                updateStatCard(1, stats.total_received || '0');
                updateStatCard(2, '$' + (stats.total_amount_sent || '0'));
                updateStatCard(3, '$' + (stats.available_limit || '0'));
            }
        }

        function updateStatCard(index, value) {
            const cards = document.querySelectorAll('.stats-card h3');
            if (cards[index]) {
                cards[index].textContent = value;
            }
        }

        function initializeActivityChart(chartData) {
            const ctx = document.getElementById('activityChart').getContext('2d');

            const labels = chartData.map(item => item.label);
            const data = chartData.map(item => item.count);

            const activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'التحويلات',
                        data: data,
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        }

        // Auto-refresh dashboard every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    // Fallback - redirect anyway
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
