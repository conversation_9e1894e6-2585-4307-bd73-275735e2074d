<?php
// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Require login
require_login();

$userData = get_user_data();
$role = $userData['role'];
$userName = $userData['name'];

// Connect to production database
try {
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Enable foreign keys and performance optimizations
    $db->exec("PRAGMA foreign_keys = ON");
    $db->exec("PRAGMA journal_mode = WAL");
    $db->exec("PRAGMA synchronous = NORMAL");
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get real-time statistics from database
function getStatistics($db, $role, $userId) {
    $stats = [];

    if ($role === 'admin' || $role === 'manager') {
        // Admin/Manager statistics - global data
        $totalTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn();
        $todayTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = DATE('now') AND deleted_at IS NULL")->fetchColumn();
        $totalAmount = $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn();
        $monthlyRevenue = $db->query("SELECT COALESCE(SUM(fee_amount), 0) FROM transfers WHERE status = 'completed' AND DATE(created_at) >= DATE('now', 'start of month') AND deleted_at IS NULL")->fetchColumn();
        $pendingTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE status IN ('pending', 'pending_payment') AND deleted_at IS NULL")->fetchColumn();
        $activeUsers = $db->query("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL")->fetchColumn();

        $stats = [
            ['title' => 'إجمالي التحويلات', 'value' => number_format($totalTransfers), 'icon' => 'bi-send', 'color' => 'primary'],
            ['title' => 'التحويلات اليوم', 'value' => number_format($todayTransfers), 'icon' => 'bi-calendar-day', 'color' => 'success'],
            ['title' => 'إجمالي المبلغ', 'value' => '$' . number_format($totalAmount, 0), 'icon' => 'bi-currency-dollar', 'color' => 'warning'],
            ['title' => 'الإيرادات الشهرية', 'value' => '$' . number_format($monthlyRevenue, 0), 'icon' => 'bi-graph-up', 'color' => 'info'],
            ['title' => 'التحويلات المعلقة', 'value' => number_format($pendingTransfers), 'icon' => 'bi-clock', 'color' => 'danger'],
            ['title' => 'المستخدمين النشطين', 'value' => number_format($activeUsers), 'icon' => 'bi-people', 'color' => 'secondary']
        ];

    } elseif ($role === 'agent') {
        // Agent statistics - transfers they handle
        $agentTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE agent_id = ? AND deleted_at IS NULL", [$userId])->fetchColumn();
        $pendingTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE agent_id = ? AND status IN ('pending', 'processing') AND deleted_at IS NULL", [$userId])->fetchColumn();
        $completedTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE agent_id = ? AND status = 'completed' AND deleted_at IS NULL", [$userId])->fetchColumn();
        $monthlyCommission = $db->query("SELECT COALESCE(SUM(fee_amount * 0.1), 0) FROM transfers WHERE agent_id = ? AND status = 'completed' AND DATE(created_at) >= DATE('now', 'start of month') AND deleted_at IS NULL", [$userId])->fetchColumn();

        $stats = [
            ['title' => 'تحويلاتي', 'value' => number_format($agentTransfers), 'icon' => 'bi-send', 'color' => 'primary'],
            ['title' => 'معلقة', 'value' => number_format($pendingTransfers), 'icon' => 'bi-clock', 'color' => 'warning'],
            ['title' => 'مكتملة', 'value' => number_format($completedTransfers), 'icon' => 'bi-check-circle', 'color' => 'success'],
            ['title' => 'العمولة الشهرية', 'value' => '$' . number_format($monthlyCommission, 0), 'icon' => 'bi-wallet', 'color' => 'info']
        ];

    } elseif ($role === 'compliance') {
        // Compliance statistics
        $pendingReview = $db->query("SELECT COUNT(*) FROM transfers WHERE compliance_status = 'pending' AND deleted_at IS NULL")->fetchColumn();
        $flaggedTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE aml_status = 'flagged' AND deleted_at IS NULL")->fetchColumn();
        $highRiskTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE risk_score >= 70 AND deleted_at IS NULL")->fetchColumn();
        $pendingKyc = $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'pending' AND deleted_at IS NULL")->fetchColumn();

        $stats = [
            ['title' => 'مراجعة معلقة', 'value' => number_format($pendingReview), 'icon' => 'bi-exclamation-triangle', 'color' => 'warning'],
            ['title' => 'تحويلات مشبوهة', 'value' => number_format($flaggedTransfers), 'icon' => 'bi-flag', 'color' => 'danger'],
            ['title' => 'عالية المخاطر', 'value' => number_format($highRiskTransfers), 'icon' => 'bi-shield-exclamation', 'color' => 'warning'],
            ['title' => 'KYC معلق', 'value' => number_format($pendingKyc), 'icon' => 'bi-person-check', 'color' => 'info']
        ];

    } else {
        // Customer statistics
        $sentTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE sender_id = ? AND deleted_at IS NULL", [$userId])->fetchColumn();
        $receivedTransfers = $db->query("SELECT COUNT(*) FROM transfers WHERE receiver_id = ? AND deleted_at IS NULL", [$userId])->fetchColumn();
        $totalSent = $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE sender_id = ? AND status = 'completed' AND deleted_at IS NULL", [$userId])->fetchColumn();
        $monthlyLimit = 10000; // Get from settings
        $monthlyUsed = $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE sender_id = ? AND status = 'completed' AND DATE(created_at) >= DATE('now', 'start of month') AND deleted_at IS NULL", [$userId])->fetchColumn();
        $availableLimit = $monthlyLimit - $monthlyUsed;

        $stats = [
            ['title' => 'التحويلات المرسلة', 'value' => number_format($sentTransfers), 'icon' => 'bi-send', 'color' => 'primary'],
            ['title' => 'التحويلات المستلمة', 'value' => number_format($receivedTransfers), 'icon' => 'bi-inbox', 'color' => 'success'],
            ['title' => 'المبلغ المرسل', 'value' => '$' . number_format($totalSent, 0), 'icon' => 'bi-currency-dollar', 'color' => 'warning'],
            ['title' => 'الحد المتاح', 'value' => '$' . number_format($availableLimit, 0), 'icon' => 'bi-speedometer', 'color' => 'info']
        ];
    }

    return $stats;
}

// Get role translations
$roleTranslations = [
    'admin' => 'مدير النظام',
    'manager' => 'مدير',
    'agent' => 'وكيل',
    'compliance' => 'مسؤول الامتثال',
    'customer' => 'عميل'
];

// Get user statistics
$userStats = getStatistics($db, $role, $userData['id']);
$roleAr = $roleTranslations[$role] ?? 'مستخدم';

// Get recent transfers based on role
function getRecentTransfers($db, $role, $userId, $limit = 5) {
    $transfers = [];

    if ($role === 'admin' || $role === 'manager') {
        // Admin/Manager can see all recent transfers
        $stmt = $db->prepare("
            SELECT t.*,
                   sc.name as sender_country_name, sc.code as sender_country_code,
                   rc.name as receiver_country_name, rc.code as receiver_country_code
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE t.deleted_at IS NULL
            ORDER BY t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);

    } elseif ($role === 'agent') {
        // Agent sees transfers they handle
        $stmt = $db->prepare("
            SELECT t.*,
                   sc.name as sender_country_name, sc.code as sender_country_code,
                   rc.name as receiver_country_name, rc.code as receiver_country_code
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE t.agent_id = ? AND t.deleted_at IS NULL
            ORDER BY t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);

    } elseif ($role === 'compliance') {
        // Compliance sees flagged or pending review transfers
        $stmt = $db->prepare("
            SELECT t.*,
                   sc.name as sender_country_name, sc.code as sender_country_code,
                   rc.name as receiver_country_name, rc.code as receiver_country_code
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE (t.compliance_status = 'pending' OR t.aml_status = 'flagged' OR t.risk_score >= 70)
            AND t.deleted_at IS NULL
            ORDER BY t.risk_score DESC, t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);

    } else {
        // Customer sees their own transfers
        $stmt = $db->prepare("
            SELECT t.*,
                   sc.name as sender_country_name, sc.code as sender_country_code,
                   rc.name as receiver_country_name, rc.code as receiver_country_code
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE (t.sender_id = ? OR t.receiver_id = ?) AND t.deleted_at IS NULL
            ORDER BY t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $userId, $limit]);
    }

    return $stmt->fetchAll();
}

// Get status translations and colors
$statusTranslations = [
    'pending' => 'في الانتظار',
    'pending_payment' => 'في انتظار الدفع',
    'paid' => 'تم الدفع',
    'processing' => 'قيد المعالجة',
    'ready_for_pickup' => 'جاهز للاستلام',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي',
    'refunded' => 'مسترد',
    'failed' => 'فاشل',
    'on_hold' => 'معلق'
];

$statusColors = [
    'pending' => 'warning',
    'pending_payment' => 'info',
    'paid' => 'primary',
    'processing' => 'info',
    'ready_for_pickup' => 'success',
    'completed' => 'success',
    'cancelled' => 'danger',
    'refunded' => 'secondary',
    'failed' => 'danger',
    'on_hold' => 'warning'
];

$recentTransfers = getRecentTransfers($db, $role, $userData['id'], 10);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-processing { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $user['name'] ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">
                            <i class="bi bi-person me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="bi bi-gear me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم
                        </h2>
                        <p class="text-white-50 mb-0">
                            أهلاً بك، <?= htmlspecialchars($userName) ?>! (<?= $roleAr ?>)
                        </p>
                        <small class="text-white-50 d-block">
                            <?= htmlspecialchars($userData['email']) ?> |
                            آخر دخول: <?= $userData['last_login_at'] ? date('Y-m-d H:i', strtotime($userData['last_login_at'])) : 'غير محدد' ?>
                        </small>
                    </div>
                    <?php if ($role === 'customer'): ?>
                    <div>
                        <a href="/transfers/create" class="btn btn-primary">
                            <i class="bi bi-send me-2"></i>
                            إرسال أموال
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <?php foreach ($userStats as $stat): ?>
            <div class="col-xl-<?= count($userStats) > 4 ? '2' : (12 / count($userStats)) ?> col-md-6 mb-4">
                <div class="stats-card bg-<?= $stat['color'] ?>">
                    <i class="bi <?= $stat['icon'] ?> fs-1 mb-3"></i>
                    <h3><?= $stat['value'] ?></h3>
                    <p class="mb-0"><?= $stat['title'] ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="row">
            <!-- Quick Actions -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($role === 'admin'): ?>
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-primary">
                                <i class="bi bi-people me-2"></i>إدارة المستخدمين
                            </a>
                            <a href="#" class="btn btn-outline-success">
                                <i class="bi bi-list-ul me-2"></i>إدارة التحويلات
                            </a>
                            <a href="/reports" class="btn btn-outline-info">
                                <i class="bi bi-graph-up me-2"></i>التقارير
                            </a>
                            <a href="/admin/monitoring" class="btn btn-outline-warning">
                                <i class="bi bi-activity me-2"></i>مراقبة النظام
                            </a>
                            <a href="/compliance/dashboard" class="btn btn-outline-danger">
                                <i class="bi bi-shield-check me-2"></i>الامتثال التنظيمي
                            </a>
                        </div>
                        <?php elseif ($role === 'agent'): ?>
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-repeat me-2"></i>معالجة التحويلات
                            </a>
                            <a href="#" class="btn btn-outline-success">
                                <i class="bi bi-check-circle me-2"></i>جاهز للاستلام
                            </a>
                            <a href="#" class="btn btn-outline-info">
                                <i class="bi bi-person-plus me-2"></i>إضافة عميل
                            </a>
                            <a href="/reports" class="btn btn-outline-warning">
                                <i class="bi bi-file-text me-2"></i>التقرير اليومي
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="d-grid gap-2">
                            <a href="/transfers/create" class="btn btn-outline-primary">
                                <i class="bi bi-send me-2"></i>إرسال أموال
                            </a>
                            <a href="/track" class="btn btn-outline-success">
                                <i class="bi bi-search me-2"></i>تتبع التحويل
                            </a>
                            <a href="#" class="btn btn-outline-info">
                                <i class="bi bi-list-ul me-2"></i>تحويلاتي
                            </a>
                            <a href="#" class="btn btn-outline-warning">
                                <i class="bi bi-person-gear me-2"></i>إعدادات الملف الشخصي
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Activity Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            نشاط التحويلات
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="activityChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transfers -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            التحويلات الأخيرة
                        </h5>
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رمز التحويل</th>
                                        <th>من/إلى</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($recentTransfers)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                            لا توجد تحويلات حالياً
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                        <?php foreach ($recentTransfers as $transfer): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($transfer['transfer_code']) ?></strong>
                                                <?php if ($role === 'compliance' && $transfer['risk_score'] >= 70): ?>
                                                <i class="bi bi-exclamation-triangle text-warning ms-1" title="عالي المخاطر"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?= htmlspecialchars($transfer['sender_country_name']) ?> →
                                                <?= htmlspecialchars($transfer['receiver_country_name']) ?>
                                            </td>
                                            <td>
                                                <strong><?= number_format($transfer['amount'], 2) ?> <?= htmlspecialchars($transfer['sender_currency']) ?></strong>
                                                <?php if ($transfer['sender_currency'] !== $transfer['receiver_currency']): ?>
                                                <br><small class="text-muted">
                                                    ≈ <?= number_format($transfer['converted_amount'], 2) ?> <?= htmlspecialchars($transfer['receiver_currency']) ?>
                                                </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?= $statusColors[$transfer['status']] ?? 'secondary' ?>">
                                                    <?= $statusTranslations[$transfer['status']] ?? $transfer['status'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('d M Y', strtotime($transfer['created_at'])) ?>
                                                <br><small class="text-muted"><?= date('H:i', strtotime($transfer['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/transfers/view?id=<?= $transfer['id'] ?>" class="btn btn-outline-primary" title="عرض">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <?php if ($role === 'admin' || $role === 'agent'): ?>
                                                    <button class="btn btn-outline-secondary" title="تعديل" onclick="editTransfer(<?= $transfer['id'] ?>)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Initialize activity chart with real data
        loadDashboardData();

        function loadDashboardData() {
            // Load dashboard statistics
            fetch('/api/dashboard-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateDashboardStats(data.data);
                    }
                })
                .catch(error => console.error('Error loading dashboard stats:', error));

            // Load chart data
            fetch('/api/chart-data?period=7days')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        initializeActivityChart(data.data);
                    }
                })
                .catch(error => console.error('Error loading chart data:', error));
        }

        function updateDashboardStats(stats) {
            // Update stats cards based on user role
            const role = '<?= $role ?>';

            if (role === 'admin') {
                updateStatCard(0, stats.total_transfers || '0');
                updateStatCard(1, stats.today_transfers || '0');
                updateStatCard(2, '$' + (stats.total_amount || '0'));
                updateStatCard(3, '$' + (stats.monthly_revenue || '0'));
            } else if (role === 'agent') {
                updateStatCard(0, stats.total_transfers || '0');
                updateStatCard(1, stats.pending_transfers || '0');
                updateStatCard(2, stats.completed_transfers || '0');
                updateStatCard(3, '$' + (stats.monthly_commission || '0'));
            } else {
                updateStatCard(0, stats.total_sent || '0');
                updateStatCard(1, stats.total_received || '0');
                updateStatCard(2, '$' + (stats.total_amount_sent || '0'));
                updateStatCard(3, '$' + (stats.available_limit || '0'));
            }
        }

        function updateStatCard(index, value) {
            const cards = document.querySelectorAll('.stats-card h3');
            if (cards[index]) {
                cards[index].textContent = value;
            }
        }

        function initializeActivityChart(chartData) {
            const ctx = document.getElementById('activityChart').getContext('2d');

            const labels = chartData.map(item => item.label);
            const data = chartData.map(item => item.count);

            const activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'التحويلات',
                        data: data,
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        }

        // Auto-refresh dashboard every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    // Fallback - redirect anyway
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
