#!/usr/bin/env php
<?php

/**
 * Elite Transfer System - Database Fix Script
 * This script fixes database connection issues and creates a clean database
 */

echo "\n";
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║           Elite Transfer System - Database Fix              ║\n";
echo "║                    Fixing All Issues                        ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";
echo "\n";

// Load environment variables
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Step 1: Clean up any corrupted SQLite files
echo "🧹 Step 1: Cleaning up corrupted database files...\n";

$dbDir = __DIR__ . '/database';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
    echo "   ✅ Created database directory\n";
}

$sqliteFiles = [
    $dbDir . '/elite_transfer.db',
    $dbDir . '/database.sqlite',
    $dbDir . '/elite_transfer.sqlite'
];

foreach ($sqliteFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "   🗑️  Removed corrupted file: " . basename($file) . "\n";
    }
}

// Step 2: Try MySQL connection first
echo "\n🔍 Step 2: Testing MySQL connection...\n";

$mysqlConfig = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'elite_transfer',
    'username' => $_ENV['DB_USERNAME'] ?? 'elite_user',
    'password' => $_ENV['DB_PASSWORD'] ?? 'elite_password_2025',
    'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4'
];

$mysqlWorking = false;

try {
    // Test basic MySQL connection
    $dsn = "mysql:host={$mysqlConfig['host']};port={$mysqlConfig['port']};charset={$mysqlConfig['charset']}";
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 5
    ]);
    
    echo "   ✅ MySQL server is accessible\n";
    
    // Try to create database
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$mysqlConfig['database']}` CHARACTER SET {$mysqlConfig['charset']} COLLATE {$mysqlConfig['charset']}_unicode_ci");
        echo "   ✅ Database '{$mysqlConfig['database']}' created/verified\n";
    } catch (Exception $e) {
        echo "   ⚠️  Database creation warning: " . $e->getMessage() . "\n";
    }
    
    // Try to create user (might fail if exists, that's OK)
    try {
        $pdo->exec("CREATE USER IF NOT EXISTS '{$mysqlConfig['username']}'@'localhost' IDENTIFIED BY '{$mysqlConfig['password']}'");
        $pdo->exec("GRANT ALL PRIVILEGES ON `{$mysqlConfig['database']}`.* TO '{$mysqlConfig['username']}'@'localhost'");
        $pdo->exec("FLUSH PRIVILEGES");
        echo "   ✅ User '{$mysqlConfig['username']}' created/verified\n";
    } catch (Exception $e) {
        echo "   ⚠️  User creation warning: " . $e->getMessage() . "\n";
    }
    
    // Test connection to specific database
    $dsn = "mysql:host={$mysqlConfig['host']};port={$mysqlConfig['port']};dbname={$mysqlConfig['database']};charset={$mysqlConfig['charset']}";
    $mysqlPdo = new PDO($dsn, $mysqlConfig['username'], $mysqlConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "   ✅ Connected to database successfully\n";
    $mysqlWorking = true;
    
} catch (Exception $e) {
    echo "   ❌ MySQL connection failed: " . $e->getMessage() . "\n";
    echo "   💡 Will use SQLite as fallback\n";
}

// Step 3: Create database tables
echo "\n🏗️  Step 3: Creating database tables...\n";

if ($mysqlWorking) {
    echo "   📊 Using MySQL database\n";
    $db = $mysqlPdo;
    $engine = "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $autoIncrement = "AUTO_INCREMENT";
    $primaryKey = "INT AUTO_INCREMENT PRIMARY KEY";
    $boolean = "BOOLEAN";
    $datetime = "TIMESTAMP";
    $text = "TEXT";
    $insertIgnore = "INSERT IGNORE INTO";
} else {
    echo "   📊 Using SQLite database\n";
    $dbPath = $dbDir . '/elite_transfer.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $engine = "";
    $autoIncrement = "AUTOINCREMENT";
    $primaryKey = "INTEGER PRIMARY KEY AUTOINCREMENT";
    $boolean = "INTEGER";
    $datetime = "DATETIME";
    $text = "TEXT";
    $insertIgnore = "INSERT OR IGNORE INTO";
}

// Define tables with compatible syntax
$tables = [
    'countries' => "
        CREATE TABLE IF NOT EXISTS countries (
            id {$primaryKey},
            name VARCHAR(255) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            currency VARCHAR(3) NOT NULL,
            flag_url VARCHAR(255),
            is_active {$boolean} DEFAULT 1,
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'users' => "
        CREATE TABLE IF NOT EXISTS users (
            id {$primaryKey},
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone VARCHAR(20),
            password VARCHAR(255) NOT NULL,
            role VARCHAR(20) DEFAULT 'customer',
            kyc_status VARCHAR(20) DEFAULT 'pending',
            kyc_level VARCHAR(20) DEFAULT 'none',
            is_active {$boolean} DEFAULT 1,
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP,
            updated_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'transfers' => "
        CREATE TABLE IF NOT EXISTS transfers (
            id {$primaryKey},
            transfer_code VARCHAR(20) UNIQUE NOT NULL,
            pickup_code VARCHAR(10) NOT NULL,
            sender_id INTEGER NOT NULL,
            sender_name VARCHAR(255) NOT NULL,
            sender_phone VARCHAR(20) NOT NULL,
            sender_country_id INTEGER NOT NULL,
            receiver_name VARCHAR(255) NOT NULL,
            receiver_phone VARCHAR(20) NOT NULL,
            receiver_country_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            converted_amount DECIMAL(15,2) NOT NULL,
            exchange_rate DECIMAL(10,6) NOT NULL,
            fee_amount DECIMAL(15,2) NOT NULL,
            total_amount DECIMAL(15,2) NOT NULL,
            sender_currency VARCHAR(3) NOT NULL,
            receiver_currency VARCHAR(3) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            payment_method VARCHAR(20) DEFAULT 'cash',
            pickup_method VARCHAR(20) DEFAULT 'cash',
            agent_id INTEGER NULL,
            notes {$text},
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP,
            updated_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'payments' => "
        CREATE TABLE IF NOT EXISTS payments (
            id {$primaryKey},
            transfer_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'USD',
            payment_method VARCHAR(50) NOT NULL,
            payment_provider VARCHAR(50),
            transaction_id VARCHAR(255),
            status VARCHAR(20) DEFAULT 'pending',
            response_data {$text},
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP,
            updated_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'exchange_rates' => "
        CREATE TABLE IF NOT EXISTS exchange_rates (
            id {$primaryKey},
            from_currency VARCHAR(3) NOT NULL,
            to_currency VARCHAR(3) NOT NULL,
            rate DECIMAL(15,6) NOT NULL,
            provider VARCHAR(50) DEFAULT 'fallback',
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'notifications' => "
        CREATE TABLE IF NOT EXISTS notifications (
            id {$primaryKey},
            user_id INTEGER NOT NULL,
            transfer_id INTEGER NULL,
            type VARCHAR(20) NOT NULL,
            title VARCHAR(255) NOT NULL,
            message {$text} NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            sent_at {$datetime} NULL,
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'system_settings' => "
        CREATE TABLE IF NOT EXISTS system_settings (
            id {$primaryKey},
            setting_key VARCHAR(255) UNIQUE NOT NULL,
            setting_value {$text},
            description {$text},
            updated_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'aml_checks' => "
        CREATE TABLE IF NOT EXISTS aml_checks (
            id {$primaryKey},
            transfer_id INTEGER NULL,
            user_id INTEGER NOT NULL,
            risk_score INTEGER DEFAULT 0,
            action VARCHAR(20) NOT NULL,
            checks_data {$text},
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'kyc_documents' => "
        CREATE TABLE IF NOT EXISTS kyc_documents (
            id {$primaryKey},
            user_id INTEGER NOT NULL,
            document_type VARCHAR(50) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    ",
    
    'external_api_logs' => "
        CREATE TABLE IF NOT EXISTS external_api_logs (
            id {$primaryKey},
            service_name VARCHAR(100) NOT NULL,
            provider VARCHAR(100) NOT NULL,
            endpoint VARCHAR(255),
            request_data {$text},
            response_data {$text},
            status_code INTEGER,
            response_time DECIMAL(10,3),
            success {$boolean} DEFAULT 1,
            error_message {$text},
            created_at {$datetime} DEFAULT CURRENT_TIMESTAMP
        ) {$engine}
    "
];

// Create tables
$createdTables = 0;
foreach ($tables as $tableName => $sql) {
    try {
        $db->exec($sql);
        echo "   ✅ Created table: {$tableName}\n";
        $createdTables++;
    } catch (PDOException $e) {
        echo "   ❌ Failed to create table {$tableName}: " . $e->getMessage() . "\n";
    }
}

// Step 4: Insert initial data
echo "\n📊 Step 4: Inserting initial data...\n";

try {
    // Insert countries
    $countries = [
        ['Saudi Arabia', 'SA', 'SAR'],
        ['United Arab Emirates', 'AE', 'AED'],
        ['Egypt', 'EG', 'EGP'],
        ['Kuwait', 'KW', 'KWD'],
        ['Jordan', 'JO', 'JOD'],
        ['United States', 'US', 'USD'],
        ['United Kingdom', 'GB', 'GBP'],
        ['European Union', 'EU', 'EUR'],
        ['Qatar', 'QA', 'QAR'],
        ['Bahrain', 'BH', 'BHD']
    ];
    
    $stmt = $db->prepare("{$insertIgnore} countries (name, code, currency) VALUES (?, ?, ?)");
    foreach ($countries as $country) {
        $stmt->execute($country);
    }
    echo "   ✅ Inserted countries (" . count($countries) . ")\n";
    
    // Insert admin user
    $adminPassword = password_hash('password', PASSWORD_DEFAULT);
    $stmt = $db->prepare("{$insertIgnore} users (name, email, password, role, kyc_status, kyc_level) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['System Administrator', '<EMAIL>', $adminPassword, 'admin', 'verified', 'full']);
    echo "   ✅ Created admin user: <EMAIL> / password\n";
    
    // Insert demo customer
    $customerPassword = password_hash('customer123', PASSWORD_DEFAULT);
    $stmt->execute(['Demo Customer', '<EMAIL>', $customerPassword, 'customer', 'verified', 'basic']);
    echo "   ✅ Created demo customer: <EMAIL> / customer123\n";
    
    // Insert system settings
    $settings = [
        ['app_name', 'Elite Transfer System', 'Application name'],
        ['app_version', '7.0', 'Application version'],
        ['max_transfer_amount', '50000', 'Maximum transfer amount'],
        ['min_transfer_amount', '1', 'Minimum transfer amount'],
        ['default_fee_percentage', '2.5', 'Default fee percentage'],
        ['default_fee_fixed', '5.00', 'Default fixed fee']
    ];
    
    $stmt = $db->prepare("{$insertIgnore} system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "   ✅ Inserted system settings (" . count($settings) . ")\n";
    
    // Insert exchange rates
    $rates = [
        ['USD', 'SAR', 3.7500],
        ['USD', 'AED', 3.6725],
        ['USD', 'EGP', 30.9000],
        ['USD', 'KWD', 0.3000],
        ['USD', 'JOD', 0.7090],
        ['EUR', 'USD', 1.0800],
        ['GBP', 'USD', 1.2400]
    ];
    
    $stmt = $db->prepare("{$insertIgnore} exchange_rates (from_currency, to_currency, rate) VALUES (?, ?, ?)");
    foreach ($rates as $rate) {
        $stmt->execute($rate);
    }
    echo "   ✅ Inserted exchange rates (" . count($rates) . ")\n";
    
} catch (Exception $e) {
    echo "   ⚠️  Warning inserting data: " . $e->getMessage() . "\n";
}

// Step 5: Verify setup
echo "\n🔍 Step 5: Verifying database setup...\n";

try {
    $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    echo "   📊 Countries: {$countryCount}\n";
    
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "   👤 Users: {$userCount}\n";
    
    $settingsCount = $db->query("SELECT COUNT(*) FROM system_settings")->fetchColumn();
    echo "   ⚙️  Settings: {$settingsCount}\n";
    
    $ratesCount = $db->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn();
    echo "   💱 Exchange rates: {$ratesCount}\n";
    
    echo "   ✅ Database verification successful\n";
    
} catch (Exception $e) {
    echo "   ❌ Verification failed: " . $e->getMessage() . "\n";
}

// Final summary
echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 Database Fix Complete!\n";
echo str_repeat("=", 60) . "\n";

if ($mysqlWorking) {
    echo "✅ Status: MySQL database ready\n";
    echo "🌐 phpMyAdmin: http://localhost/phpmyadmin\n";
    echo "👤 Username: {$mysqlConfig['username']}\n";
    echo "🔑 Password: {$mysqlConfig['password']}\n";
    echo "🗄️  Database: {$mysqlConfig['database']}\n";
} else {
    echo "✅ Status: SQLite database ready (fallback mode)\n";
    echo "📁 Database file: {$dbPath}\n";
}

echo "\n🚀 Next Steps:\n";
echo "   1. Start the application: php -S localhost:8000 -t public\n";
echo "   2. Visit: http://localhost:8000\n";
echo "   3. Login as admin: <EMAIL> / password\n";
echo "   4. Login as customer: <EMAIL> / customer123\n";

echo "\n💡 Tables created: {$createdTables}\n";
echo "🎯 System is ready to use!\n\n";

?>
