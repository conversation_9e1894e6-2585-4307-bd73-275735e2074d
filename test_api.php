<?php

echo "🧪 Testing API Health directly\n\n";

// Test 1: Direct file execution
echo "Test 1: Direct file execution\n";
echo "==============================\n";

ob_start();
include 'public/api/health.php';
$output = ob_get_clean();

echo "Output:\n";
echo $output . "\n\n";

// Test 2: Simulate HTTP request
echo "Test 2: Simulate HTTP request via cURL\n";
echo "=======================================\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/health');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HEADER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "HTTP Code: $httpCode\n";
    echo "Response:\n";
    echo $response . "\n";
}

echo "\n";

// Test 3: Check server status
echo "Test 3: Check server status\n";
echo "============================\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_NOBODY, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "Server not accessible: $error\n";
} else {
    echo "Server HTTP Code: $httpCode\n";
    echo "Server Status: " . ($httpCode == 200 ? "OK" : "Error") . "\n";
}

?>
