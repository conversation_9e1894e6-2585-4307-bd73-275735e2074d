<?php
// Load session helper
require_once __DIR__ . '/../../app/Helpers/SessionHelper.php';

// Start session safely
SessionHelper::start();
header('Content-Type: application/json');

// Check if user is logged in
if (!SessionHelper::isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

// Validate required fields
$required_fields = [
    'sender_country_id', 'receiver_country_id', 'amount', 'purpose',
    'receiver_name', 'receiver_phone', 'receiver_address', 
    'pickup_method', 'payment_method'
];

foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        echo json_encode(['success' => false, 'message' => "الحقل {$field} مطلوب"]);
        exit;
    }
}

try {
    // Connect to database
    $dbPath = __DIR__ . '/../../database/elite_transfer.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Generate transfer code and pickup code
    $transferCode = 'ET' . date('Ymd') . rand(1000, 9999);
    $pickupCode = rand(100000, 999999);
    
    // Get exchange rates (simplified - in production, fetch from rates table)
    $exchangeRates = [
        '1_6' => 8.24,  // SAR to EGP
        '1_7' => 0.189, // SAR to JOD
        '1_8' => 0.08,  // SAR to KWD
        '1_9' => 0.97,  // SAR to QAR
        '1_10' => 0.10, // SAR to BHD
        '2_6' => 8.42,  // AED to EGP
        '2_7' => 0.193, // AED to JOD
        '2_8' => 0.082, // AED to KWD
        '2_9' => 0.99,  // AED to QAR
        '2_10' => 0.103, // AED to BHD
        '3_6' => 30.90, // USD to EGP
        '3_7' => 0.709, // USD to JOD
        '3_8' => 0.30,  // USD to KWD
        '3_9' => 3.64,  // USD to QAR
        '3_10' => 0.376 // USD to BHD
    ];
    
    // Get currencies (simplified mapping)
    $currencies = [
        '1' => 'SAR', '2' => 'AED', '3' => 'USD', '4' => 'EUR', '5' => 'GBP',
        '6' => 'EGP', '7' => 'JOD', '8' => 'KWD', '9' => 'QAR', '10' => 'BHD'
    ];
    
    $senderCountryId = $input['sender_country_id'];
    $receiverCountryId = $input['receiver_country_id'];
    $rateKey = $senderCountryId . '_' . $receiverCountryId;
    
    $exchangeRate = $exchangeRates[$rateKey] ?? 1.0;
    $senderCurrency = $currencies[$senderCountryId] ?? 'USD';
    $receiverCurrency = $currencies[$receiverCountryId] ?? 'USD';
    
    // Calculate amounts
    $amount = floatval($input['amount']);
    $convertedAmount = $amount * $exchangeRate;
    $feePercentage = 0.025; // 2.5%
    $fixedFee = 5.00;
    $feeAmount = ($amount * $feePercentage) + $fixedFee;
    $totalAmount = $amount + $feeAmount;
    
    // Validate amount limits
    if ($amount < 1) {
        echo json_encode(['success' => false, 'message' => 'الحد الأدنى للتحويل هو 1']);
        exit;
    }
    
    if ($amount > 50000) {
        echo json_encode(['success' => false, 'message' => 'الحد الأقصى للتحويل هو 50,000']);
        exit;
    }
    
    // Get user info
    $stmt = $db->prepare("SELECT name, phone FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
        exit;
    }
    
    // Insert transfer into database
    $stmt = $db->prepare("
        INSERT INTO transfers (
            transfer_code, pickup_code, sender_id, sender_name, sender_phone,
            sender_country_id, receiver_name, receiver_phone, receiver_country_id,
            amount, converted_amount, exchange_rate, fee_amount, total_amount,
            sender_currency, receiver_currency, status, payment_method, pickup_method,
            notes, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, datetime('now'))
    ");
    
    $notes = "الغرض: " . $input['purpose'] . "\nالعنوان: " . $input['receiver_address'];
    if (!empty($input['bank_account'])) {
        $notes .= "\nرقم الحساب: " . $input['bank_account'];
    }
    
    $stmt->execute([
        $transferCode,
        $pickupCode,
        SessionHelper::getUserId(),
        $user['name'],
        $user['phone'] ?? '',
        $senderCountryId,
        $input['receiver_name'],
        $input['receiver_phone'],
        $receiverCountryId,
        $amount,
        $convertedAmount,
        $exchangeRate,
        $feeAmount,
        $totalAmount,
        $senderCurrency,
        $receiverCurrency,
        $input['payment_method'],
        $input['pickup_method'],
        $notes
    ]);
    
    $transferId = $db->lastInsertId();
    
    // Create payment record
    $stmt = $db->prepare("
        INSERT INTO payments (transfer_id, amount, currency, payment_method, status, created_at)
        VALUES (?, ?, ?, ?, 'pending', datetime('now'))
    ");
    
    $stmt->execute([
        $transferId,
        $totalAmount,
        $senderCurrency,
        $input['payment_method']
    ]);
    
    // Send notification
    $notificationTitle = 'تم إنشاء تحويل جديد';
    $notificationMessage = "تم إنشاء تحويلك بنجاح.\nرمز التحويل: {$transferCode}\nرمز الاستلام: {$pickupCode}\nالمبلغ: {$amount} {$senderCurrency}\nالمستقبل: {$input['receiver_name']}";
    
    $stmt = $db->prepare("
        INSERT INTO notifications (user_id, transfer_id, type, title, message, status, created_at)
        VALUES (?, ?, 'email', ?, ?, 'pending', datetime('now'))
    ");
    
    $stmt->execute([
        SessionHelper::getUserId(),
        $transferId,
        $notificationTitle,
        $notificationMessage
    ]);
    
    // Log the API call
    $stmt = $db->prepare("
        INSERT INTO external_api_logs (service_name, provider, endpoint, request_data, response_data, status_code, success, created_at)
        VALUES ('transfer_creation', 'internal', '/api/create-transfer', ?, ?, 200, 1, datetime('now'))
    ");
    
    $requestData = json_encode($input);
    $responseData = json_encode([
        'transfer_code' => $transferCode,
        'pickup_code' => $pickupCode,
        'transfer_id' => $transferId
    ]);
    
    $stmt->execute([$requestData, $responseData]);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء التحويل بنجاح',
        'data' => [
            'transfer_code' => $transferCode,
            'pickup_code' => $pickupCode,
            'transfer_id' => $transferId,
            'amount' => $amount,
            'converted_amount' => $convertedAmount,
            'fee_amount' => $feeAmount,
            'total_amount' => $totalAmount,
            'sender_currency' => $senderCurrency,
            'receiver_currency' => $receiverCurrency,
            'exchange_rate' => $exchangeRate,
            'receiver_name' => $input['receiver_name'],
            'status' => 'pending'
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in create-transfer: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات'
    ]);
    
} catch (Exception $e) {
    error_log("General error in create-transfer: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ غير متوقع'
    ]);
}
?>
