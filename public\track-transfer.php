<?php
// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Get transfer code from URL parameter or form
$transferCode = $_GET['code'] ?? $_POST['transfer_code'] ?? '';
$transfer = null;
$error = '';

if ($transferCode) {
    try {
        $dbPath = __DIR__ . '/../database/elite_transfer.db';
        $db = new PDO('sqlite:' . $dbPath);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $db->prepare("
            SELECT t.*,
                   sc.name as sender_country_name, sc.currency as sender_currency_name,
                   rc.name as receiver_country_name, rc.currency as receiver_currency_name
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE t.transfer_code = ? OR t.pickup_code = ?
        ");
        $stmt->execute([$transferCode, $transferCode]);
        $transfer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$transfer) {
            $error = 'لم يتم العثور على التحويل. يرجى التحقق من رمز التحويل.';
        }
        
    } catch (Exception $e) {
        error_log("Error fetching transfer: " . $e->getMessage());
        $error = 'حدث خطأ في البحث عن التحويل. يرجى المحاولة مرة أخرى.';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع التحويل - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e5e7eb;
            padding: 12px 20px;
            font-size: 16px;
        }
        
        .form-control:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .status-timeline {
            position: relative;
            padding: 20px 0;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 20px;
            font-size: 20px;
            z-index: 2;
        }
        
        .status-icon.completed {
            background: #10b981;
            color: white;
        }
        
        .status-icon.current {
            background: #f59e0b;
            color: white;
        }
        
        .status-icon.pending {
            background: #e5e7eb;
            color: #6b7280;
        }
        
        .status-line {
            position: absolute;
            right: 25px;
            top: 50px;
            width: 2px;
            height: 40px;
            background: #e5e7eb;
        }
        
        .status-line.completed {
            background: #10b981;
        }
        
        .transfer-info {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        
        .info-value {
            color: #6b7280;
        }
        
        .amount-display {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        .amount-display h3 {
            margin: 0;
            font-size: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank me-2"></i>
                Elite Transfer
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/">الرئيسية</a>
                <a class="nav-link text-white" href="/create-transfer">تحويل جديد</a>
                <?php if (SessionHelper::isLoggedIn()): ?>
                    <a class="nav-link text-white" href="/dashboard">لوحة التحكم</a>
                <?php else: ?>
                    <a class="nav-link text-white" href="/login">تسجيل الدخول</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-transparent border-0 text-center py-4">
                        <h2 class="mb-1">
                            <i class="bi bi-search me-2 text-danger"></i>
                            تتبع التحويل
                        </h2>
                        <p class="text-muted mb-0">تابع حالة تحويلك في الوقت الفعلي</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php if (!$transfer): ?>
                            <!-- Search Form -->
                            <form method="POST" action="/track-transfer">
                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        <i class="bi bi-key me-1"></i>
                                        رمز التحويل
                                    </label>
                                    <input type="text" class="form-control" id="code" name="transfer_code"
                                           placeholder="أدخل رمز التحويل (مثال: ET20250115XXXX)" 
                                           value="<?= htmlspecialchars($transferCode) ?>" required>
                                    <div class="form-text">
                                        يمكنك العثور على رمز التحويل في رسالة التأكيد المرسلة إليك
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-search me-2"></i>
                                        تتبع التحويل
                                    </button>
                                </div>
                            </form>
                            
                            <?php if ($transferCode && !$transfer): ?>
                                <div class="alert alert-warning mt-3">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    لم يتم العثور على تحويل بهذا الرمز. تأكد من صحة الرمز المدخل.
                                </div>
                            <?php endif; ?>
                            
                        <?php else: ?>
                            <!-- Transfer Details -->
                            <div class="text-center mb-4">
                                <h4 class="text-success">
                                    <i class="bi bi-check-circle me-2"></i>
                                    تم العثور على التحويل
                                </h4>
                                <p class="text-muted">رمز التحويل: <strong><?= htmlspecialchars($transfer['transfer_code']) ?></strong></p>
                            </div>
                            
                            <!-- Amount Display -->
                            <div class="amount-display">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>المبلغ المرسل</h6>
                                        <h3><?= number_format($transfer['amount'], 2) ?> <?= $transfer['sender_currency'] ?></h3>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>المبلغ المستلم</h6>
                                        <h3><?= number_format($transfer['converted_amount'], 2) ?> <?= $transfer['receiver_currency'] ?></h3>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Status Timeline -->
                            <div class="status-timeline">
                                <h5 class="mb-4">
                                    <i class="bi bi-clock-history me-2"></i>
                                    حالة التحويل
                                </h5>
                                
                                <?php
                                $statuses = [
                                    'pending' => ['تم إنشاء التحويل', 'تم إنشاء طلب التحويل بنجاح'],
                                    'pending_payment' => ['في انتظار الدفع', 'يرجى إكمال عملية الدفع'],
                                    'paid' => ['تم الدفع', 'تم استلام الدفع بنجاح'],
                                    'processing' => ['قيد المعالجة', 'جاري معالجة التحويل'],
                                    'ready_for_pickup' => ['جاهز للاستلام', 'يمكن للمستقبل استلام المبلغ'],
                                    'completed' => ['مكتمل', 'تم إكمال التحويل بنجاح'],
                                    'cancelled' => ['ملغي', 'تم إلغاء التحويل'],
                                    'refunded' => ['مسترد', 'تم استرداد المبلغ']
                                ];
                                
                                $currentStatus = $transfer['status'];
                                $statusOrder = ['pending', 'pending_payment', 'paid', 'processing', 'ready_for_pickup', 'completed'];
                                $currentIndex = array_search($currentStatus, $statusOrder);
                                
                                foreach ($statusOrder as $index => $status):
                                    if (!isset($statuses[$status])) continue;
                                    
                                    $isCompleted = $index < $currentIndex || ($currentStatus === 'completed' && $status === 'completed');
                                    $isCurrent = $status === $currentStatus;
                                    $isPending = $index > $currentIndex && $currentStatus !== 'completed';
                                    
                                    $iconClass = $isCompleted ? 'completed' : ($isCurrent ? 'current' : 'pending');
                                    $icon = $isCompleted ? 'bi-check-circle-fill' : ($isCurrent ? 'bi-clock-fill' : 'bi-circle');
                                ?>
                                    <div class="status-item">
                                        <div class="status-icon <?= $iconClass ?>">
                                            <i class="bi <?= $icon ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?= $statuses[$status][0] ?></h6>
                                            <p class="text-muted mb-0"><?= $statuses[$status][1] ?></p>
                                            <?php if ($isCurrent): ?>
                                                <small class="text-primary">الحالة الحالية</small>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($index < count($statusOrder) - 1): ?>
                                            <div class="status-line <?= $isCompleted ? 'completed' : '' ?>"></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <!-- Transfer Information -->
                            <div class="transfer-info">
                                <h5 class="mb-3">
                                    <i class="bi bi-info-circle me-2"></i>
                                    تفاصيل التحويل
                                </h5>
                                
                                <div class="info-item">
                                    <span class="info-label">رمز الاستلام:</span>
                                    <span class="info-value fw-bold text-danger"><?= $transfer['pickup_code'] ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">المرسل:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['sender_name']) ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">المستقبل:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['receiver_name']) ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">هاتف المستقبل:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['receiver_phone']) ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">من:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['sender_country_name'] ?? 'غير محدد') ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">إلى:</span>
                                    <span class="info-value"><?= htmlspecialchars($transfer['receiver_country_name'] ?? 'غير محدد') ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">سعر الصرف:</span>
                                    <span class="info-value"><?= number_format($transfer['exchange_rate'], 4) ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">رسوم التحويل:</span>
                                    <span class="info-value"><?= number_format($transfer['fee_amount'], 2) ?> <?= $transfer['sender_currency'] ?></span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">طريقة الدفع:</span>
                                    <span class="info-value">
                                        <?php
                                        $paymentMethods = [
                                            'card' => 'بطاقة ائتمانية',
                                            'bank' => 'تحويل بنكي',
                                            'cash' => 'نقداً'
                                        ];
                                        echo $paymentMethods[$transfer['payment_method']] ?? $transfer['payment_method'];
                                        ?>
                                    </span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">طريقة الاستلام:</span>
                                    <span class="info-value">
                                        <?php
                                        $pickupMethods = [
                                            'cash' => 'استلام نقدي',
                                            'bank_deposit' => 'إيداع بنكي',
                                            'mobile_wallet' => 'محفظة إلكترونية'
                                        ];
                                        echo $pickupMethods[$transfer['pickup_method']] ?? $transfer['pickup_method'];
                                        ?>
                                    </span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">تاريخ الإنشاء:</span>
                                    <span class="info-value"><?= date('Y-m-d H:i', strtotime($transfer['created_at'])) ?></span>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex gap-2 mt-4">
                                <a href="/track-transfer" class="btn btn-outline-secondary">
                                    <i class="bi bi-search me-2"></i>
                                    تتبع تحويل آخر
                                </a>
                                
                                <?php if (SessionHelper::isLoggedIn()): ?>
                                    <a href="/dashboard" class="btn btn-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة التحكم
                                    </a>
                                <?php endif; ?>
                                
                                <button class="btn btn-success" onclick="window.print()">
                                    <i class="bi bi-printer me-2"></i>
                                    طباعة
                                </button>
                            </div>
                            
                            <!-- Important Notes -->
                            <div class="alert alert-info mt-4">
                                <h6><i class="bi bi-info-circle me-2"></i>ملاحظات مهمة:</h6>
                                <ul class="mb-0">
                                    <li>احتفظ برمز الاستلام <strong><?= $transfer['pickup_code'] ?></strong> وشاركه مع المستقبل</li>
                                    <li>سيحتاج المستقبل إلى إثبات هوية صالح لاستلام المبلغ</li>
                                    <li>يمكن استلام المبلغ خلال 30 يوماً من تاريخ الإرسال</li>
                                    <li>في حالة وجود أي استفسار، تواصل معنا على الرقم: 920000000</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
