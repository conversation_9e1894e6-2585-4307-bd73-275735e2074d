<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/">
                    <i class="bi bi-house me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link text-white" href="/register">
                    <i class="bi bi-person-plus me-1"></i>
                    إنشاء حساب
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-bank2 text-primary" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 mb-1">أهلاً بعودتك</h2>
                            <p class="text-muted">سجل دخولك إلى حساب Elite Transfer</p>
                        </div>

                        <!-- Demo Accounts Info -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>حسابات تجريبية متاحة:</h6>
                            <small>
                                <strong>مدير:</strong> <EMAIL> / password123<br>
                                <strong>وكيل:</strong> <EMAIL> / password123<br>
                                <strong>عميل:</strong> <EMAIL> / password123
                            </small>
                        </div>

                        <!-- Login Form -->
                        <form id="loginForm" method="POST" action="/dashboard">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       required 
                                       autofocus
                                       placeholder="أدخل بريدك الإلكتروني">
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>
                                    كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           required
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>

                            <div class="text-center">
                                <a href="#" class="text-decoration-none">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-0">ليس لديك حساب؟</p>
                            <a href="/register" class="btn btn-outline-primary mt-2">
                                <i class="bi bi-person-plus me-2"></i>
                                إنشاء حساب جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري تسجيل الدخول...';

            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم تسجيل الدخول بنجاح!');
                    window.location.href = data.redirect;
                } else {
                    alert(data.message || 'حدث خطأ أثناء تسجيل الدخول');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>تسجيل الدخول';
            });
        });

        // Auto-fill demo credentials
        function fillDemo(type) {
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            
            passwordField.value = 'password123';
            
            switch(type) {
                case 'admin':
                    emailField.value = '<EMAIL>';
                    break;
                case 'agent':
                    emailField.value = '<EMAIL>';
                    break;
                case 'customer':
                    emailField.value = '<EMAIL>';
                    break;
            }
        }

        // Add click handlers for demo accounts
        document.addEventListener('DOMContentLoaded', function() {
            const alertDiv = document.querySelector('.alert-info');
            alertDiv.innerHTML += `
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="fillDemo('admin')">مدير</button>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="fillDemo('agent')">وكيل</button>
                    <button class="btn btn-sm btn-outline-info" onclick="fillDemo('customer')">عميل</button>
                </div>
            `;
        });
    </script>
</body>
</html>
