<?php

echo "🧪 اختبار جميع صفحات النظام...\n\n";

// List of all pages to test
$pages = [
    // Public pages
    '/' => 'الصفحة الرئيسية',
    '/login' => 'صفحة تسجيل الدخول',
    '/register' => 'صفحة التسجيل',
    '/track-transfer' => 'تتبع التحويل',
    
    // Dashboard pages (require login)
    '/dashboard' => 'لوحة التحكم',
    '/create-transfer' => 'إنشاء تحويل',
    '/my-transfers' => 'تحويلاتي',
    '/profile' => 'الملف الشخصي',
    
    // Admin pages (require admin login)
    '/admin/users' => 'إدارة المستخدمين',
    '/admin/transfers' => 'إدارة التحويلات',
    '/admin/reports' => 'التقارير',
    '/admin/settings' => 'الإعدادات',
    '/admin/monitoring' => 'مراقبة النظام',
    '/compliance/dashboard' => 'لوحة الامتثال',
    
    // API endpoints
    '/api/dashboard-stats' => 'إحصائيات لوحة التحكم',
    '/api/system-health' => 'حالة النظام',
    '/api/performance-stats' => 'إحصائيات الأداء'
];

$baseUrl = 'http://localhost:8000';
$results = [];
$totalPages = count($pages);
$successCount = 0;
$errorCount = 0;

echo "📊 اختبار {$totalPages} صفحة...\n";
echo str_repeat("=", 60) . "\n";

foreach ($pages as $path => $name) {
    $url = $baseUrl . $path;
    
    echo "🔍 اختبار: {$name} ({$path})... ";
    
    try {
        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Elite Transfer System Test Bot');
        
        // Execute request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "❌ خطأ في الاتصال: {$error}\n";
            $results[$path] = ['status' => 'error', 'message' => $error, 'name' => $name];
            $errorCount++;
        } elseif ($httpCode >= 200 && $httpCode < 400) {
            echo "✅ نجح ({$httpCode})\n";
            $results[$path] = ['status' => 'success', 'code' => $httpCode, 'name' => $name];
            $successCount++;
        } elseif ($httpCode == 404) {
            echo "⚠️  غير موجود (404)\n";
            $results[$path] = ['status' => 'not_found', 'code' => $httpCode, 'name' => $name];
            $errorCount++;
        } elseif ($httpCode >= 400) {
            echo "⚠️  خطأ ({$httpCode})\n";
            $results[$path] = ['status' => 'error', 'code' => $httpCode, 'name' => $name];
            $errorCount++;
        } else {
            echo "❓ غير معروف ({$httpCode})\n";
            $results[$path] = ['status' => 'unknown', 'code' => $httpCode, 'name' => $name];
            $errorCount++;
        }
        
        // Small delay to avoid overwhelming the server
        usleep(100000); // 0.1 second
        
    } catch (Exception $e) {
        echo "❌ استثناء: {$e->getMessage()}\n";
        $results[$path] = ['status' => 'exception', 'message' => $e->getMessage(), 'name' => $name];
        $errorCount++;
    }
}

echo str_repeat("=", 60) . "\n";

// Summary
echo "\n📋 ملخص النتائج:\n";
echo "   ✅ صفحات تعمل: {$successCount}\n";
echo "   ❌ صفحات بها مشاكل: {$errorCount}\n";
echo "   📊 إجمالي الصفحات: {$totalPages}\n";
echo "   📈 معدل النجاح: " . round(($successCount / $totalPages) * 100, 1) . "%\n\n";

// Detailed results
echo "📝 تفاصيل النتائج:\n";
echo str_repeat("-", 60) . "\n";

$categories = [
    'success' => ['title' => '✅ صفحات تعمل بنجاح', 'color' => 'green'],
    'not_found' => ['title' => '⚠️  صفحات غير موجودة (404)', 'color' => 'yellow'],
    'error' => ['title' => '❌ صفحات بها أخطاء', 'color' => 'red'],
    'exception' => ['title' => '💥 صفحات بها استثناءات', 'color' => 'red']
];

foreach ($categories as $status => $info) {
    $categoryPages = array_filter($results, function($result) use ($status) {
        return $result['status'] === $status;
    });
    
    if (!empty($categoryPages)) {
        echo "\n{$info['title']}:\n";
        foreach ($categoryPages as $path => $result) {
            $code = isset($result['code']) ? " ({$result['code']})" : '';
            $message = isset($result['message']) ? " - {$result['message']}" : '';
            echo "   • {$result['name']} ({$path}){$code}{$message}\n";
        }
    }
}

// Recommendations
echo "\n💡 التوصيات:\n";

if ($errorCount > 0) {
    echo "   🔧 يُنصح بإصلاح الصفحات التي بها مشاكل\n";
    
    $notFoundPages = array_filter($results, function($result) {
        return $result['status'] === 'not_found';
    });
    
    if (!empty($notFoundPages)) {
        echo "   📁 تحقق من وجود ملفات الصفحات المفقودة\n";
        echo "   🔗 تحقق من التوجيهات في bootstrap/simple-app.php\n";
    }
    
    $errorPages = array_filter($results, function($result) {
        return $result['status'] === 'error';
    });
    
    if (!empty($errorPages)) {
        echo "   🐛 تحقق من سجلات الأخطاء للصفحات التي بها مشاكل\n";
        echo "   🔍 تحقق من قاعدة البيانات والاتصالات\n";
    }
}

if ($successCount === $totalPages) {
    echo "   🎉 ممتاز! جميع الصفحات تعمل بشكل صحيح\n";
    echo "   🚀 النظام جاهز للاستخدام\n";
} elseif ($successCount / $totalPages >= 0.8) {
    echo "   👍 جيد! معظم الصفحات تعمل بشكل صحيح\n";
    echo "   🔧 أصلح الصفحات المتبقية لتحسين الأداء\n";
} else {
    echo "   ⚠️  يحتاج النظام إلى مراجعة شاملة\n";
    echo "   🛠️  أصلح المشاكل الأساسية أولاً\n";
}

// Database test
echo "\n🗄️  اختبار قاعدة البيانات:\n";

try {
    $db = new PDO('sqlite:database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test basic queries
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $transferCount = $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn();
    $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    
    echo "   ✅ قاعدة البيانات متصلة\n";
    echo "   👥 المستخدمون: {$userCount}\n";
    echo "   💸 التحويلات: {$transferCount}\n";
    echo "   🌍 الدول: {$countryCount}\n";
    
    if ($userCount > 0 && $countryCount > 0) {
        echo "   ✅ البيانات الأساسية موجودة\n";
    } else {
        echo "   ⚠️  قد تحتاج إلى تشغيل سكريپت إعداد البيانات\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ خطأ في قاعدة البيانات: {$e->getMessage()}\n";
    echo "   🔧 تشغيل: php quick_fix.php\n";
}

// Server test
echo "\n🖥️  اختبار الخادم:\n";

$serverInfo = [
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'Server Name' => $_SERVER['SERVER_NAME'] ?? 'localhost',
    'Server Port' => $_SERVER['SERVER_PORT'] ?? '8000'
];

foreach ($serverInfo as $key => $value) {
    echo "   📊 {$key}: {$value}\n";
}

// Extensions test
echo "\n🔌 اختبار الامتدادات المطلوبة:\n";

$requiredExtensions = ['pdo', 'pdo_sqlite', 'curl', 'json', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext}: مثبت\n";
    } else {
        echo "   ❌ {$ext}: غير مثبت\n";
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    echo "   🎉 جميع الامتدادات المطلوبة مثبتة\n";
} else {
    echo "   ⚠️  امتدادات مفقودة: " . implode(', ', $missingExtensions) . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🏁 انتهى الاختبار الشامل\n";

if ($successCount === $totalPages && empty($missingExtensions)) {
    echo "🎊 النظام يعمل بشكل مثالي! جاهز للاستخدام\n";
} else {
    echo "🔧 يحتاج النظام إلى بعض الإصلاحات\n";
}

echo "🌐 زيارة النظام: {$baseUrl}\n";
echo "👤 تسجيل الدخول: <EMAIL> / password\n\n";

?>
