<?php
// Redirect page for transfers/create
// This handles the routing issue with PHP built-in server

// Check if user is trying to access transfers/create
$requestUri = $_SERVER['REQUEST_URI'];
if (strpos($requestUri, 'transfers/create') !== false) {
    // Redirect to the working new-transfer page
    header('Location: /new-transfer');
    exit;
}

// If accessed directly, show the create transfer page
include __DIR__ . '/new-transfer.php';
?>
