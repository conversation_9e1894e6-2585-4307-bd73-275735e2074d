<?php

echo "🧪 Testing Production Database\n\n";

try {
    // Connect to production database
    $db = new PDO('sqlite:database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to production database\n\n";
    
    // Check tables
    echo "📋 Checking tables:\n";
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "  ✅ $table\n";
    }
    
    echo "\n📊 Table counts:\n";
    foreach ($tables as $table) {
        $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
        echo "  $table: $count records\n";
    }
    
    // Insert a test country
    echo "\n🌍 Testing country insertion:\n";
    $stmt = $db->prepare("INSERT INTO countries (name, code, currency, is_active) VALUES (?, ?, ?, ?)");
    $result = $stmt->execute(['Test Country', 'TC', 'TCC', 1]);
    
    if ($result) {
        echo "✅ Country inserted successfully\n";
        $countryId = $db->lastInsertId();
        echo "  Country ID: $countryId\n";
        
        // Delete test country
        $db->exec("DELETE FROM countries WHERE id = $countryId");
        echo "✅ Test country deleted\n";
    }
    
    // Test user insertion
    echo "\n👤 Testing user insertion:\n";
    $stmt = $db->prepare("INSERT INTO users (user_code, name, email, password_hash, role) VALUES (?, ?, ?, ?, ?)");
    $result = $stmt->execute(['TEST001', 'Test User', '<EMAIL>', password_hash('test123', PASSWORD_DEFAULT), 'customer']);
    
    if ($result) {
        echo "✅ User inserted successfully\n";
        $userId = $db->lastInsertId();
        echo "  User ID: $userId\n";
        
        // Delete test user
        $db->exec("DELETE FROM users WHERE id = $userId");
        echo "✅ Test user deleted\n";
    }
    
    echo "\n🎉 Production database is working correctly!\n";
    echo "🔧 Ready for real data population\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
