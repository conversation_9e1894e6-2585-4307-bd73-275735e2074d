<?php
// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مسؤول الامتثال';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الامتثال - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .alert-card {
            background: linear-gradient(135deg, var(--bs-danger) 0%, var(--bs-danger) 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .alert-card:hover {
            transform: translateY(-5px);
        }
        
        .alert-card h4 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .compliance-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-approved { background: #d1fae5; color: #065f46; }
        .status-rejected { background: #fee2e2; color: #991b1b; }
        .status-review { background: #dbeafe; color: #1e40af; }
        
        .risk-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 5px;
        }
        
        .risk-low { background-color: #10b981; }
        .risk-medium { background-color: #f59e0b; }
        .risk-high { background-color: #ef4444; }
        .risk-critical { background-color: #7c2d12; }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-shield-check me-2"></i>
                Elite Transfer - Compliance
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/admin/monitoring">
                    <i class="bi bi-activity me-1"></i>
                    المراقبة
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-shield-exclamation me-2"></i>
                            لوحة تحكم الامتثال
                        </h2>
                        <p class="text-white-50 mb-0">
                            مراقبة وإدارة الامتثال التنظيمي ومكافحة غسيل الأموال
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-outline-light me-2" onclick="generateComplianceReport()">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            تقرير الامتثال
                        </button>
                        <button class="btn btn-outline-light" onclick="refreshAlerts()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="alert-card bg-danger">
                    <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                    <h4 id="pendingReviews">12</h4>
                    <p class="mb-0">تحويلات تحتاج مراجعة</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="alert-card bg-warning">
                    <i class="bi bi-file-earmark-medical fs-1 mb-3"></i>
                    <h4 id="pendingKYC">8</h4>
                    <p class="mb-0">طلبات KYC معلقة</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="alert-card bg-info">
                    <i class="bi bi-flag fs-1 mb-3"></i>
                    <h4 id="sarReports">3</h4>
                    <p class="mb-0">تقارير نشاط مشبوه</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="alert-card bg-success">
                    <i class="bi bi-currency-dollar fs-1 mb-3"></i>
                    <h4 id="ctrReports">15</h4>
                    <p class="mb-0">تقارير CTR هذا الشهر</p>
                </div>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Pending Reviews -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            التحويلات المعلقة للمراجعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="pendingReviewsTable">
                                <thead>
                                    <tr>
                                        <th>رمز التحويل</th>
                                        <th>المرسل</th>
                                        <th>المبلغ</th>
                                        <th>مستوى المخاطر</th>
                                        <th>السبب</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>TXN001234</td>
                                        <td>أحمد محمد</td>
                                        <td>$15,000</td>
                                        <td>
                                            <span class="risk-indicator risk-high"></span>
                                            عالي
                                        </td>
                                        <td>مبلغ كبير</td>
                                        <td>2025-01-15</td>
                                        <td>
                                            <button class="btn btn-sm btn-success me-1" onclick="approveTransfer('TXN001234')">
                                                <i class="bi bi-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="rejectTransfer('TXN001234')">
                                                <i class="bi bi-x"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>TXN001235</td>
                                        <td>فاطمة علي</td>
                                        <td>$8,500</td>
                                        <td>
                                            <span class="risk-indicator risk-medium"></span>
                                            متوسط
                                        </td>
                                        <td>نمط مشبوه</td>
                                        <td>2025-01-15</td>
                                        <td>
                                            <button class="btn btn-sm btn-success me-1" onclick="approveTransfer('TXN001235')">
                                                <i class="bi bi-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="rejectTransfer('TXN001235')">
                                                <i class="bi bi-x"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- KYC Requests -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-check me-2"></i>
                            طلبات التحقق من الهوية (KYC)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="kycRequestsTable">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>نوع الطلب</th>
                                        <th>الوثائق</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التقديم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>محمد أحمد</td>
                                        <td>KYC كامل</td>
                                        <td>
                                            <span class="badge bg-primary">هوية</span>
                                            <span class="badge bg-primary">عنوان</span>
                                            <span class="badge bg-primary">دخل</span>
                                        </td>
                                        <td><span class="compliance-status status-pending">معلق</span></td>
                                        <td>2025-01-14</td>
                                        <td>
                                            <button class="btn btn-sm btn-info me-1" onclick="reviewKYC('KYC001')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-success me-1" onclick="approveKYC('KYC001')">
                                                <i class="bi bi-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="rejectKYC('KYC001')">
                                                <i class="bi bi-x"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Risk Distribution -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart me-2"></i>
                            توزيع المخاطر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="riskChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Alerts -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bell me-2"></i>
                            التنبيهات الأخيرة
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تحذير AML:</strong> تم اكتشاف نمط تحويلات مشبوه للمستخدم #1234
                            <small class="d-block text-muted">منذ 5 دقائق</small>
                        </div>
                        
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>طلب KYC:</strong> طلب جديد للتحقق من الهوية من المستخدم أحمد محمد
                            <small class="d-block text-muted">منذ 15 دقيقة</small>
                        </div>
                        
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-shield-exclamation me-2"></i>
                            <strong>تحويل محظور:</strong> تم حظر تحويل بقيمة $25,000 لتجاوز الحد المسموح
                            <small class="d-block text-muted">منذ 30 دقيقة</small>
                        </div>
                    </div>
                </div>

                <!-- Compliance Statistics -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            إحصائيات الامتثال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <h4 class="text-success">98.5%</h4>
                                <small class="text-muted">معدل الامتثال</small>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-info">2.3%</h4>
                                <small class="text-muted">معدل الرفض</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">45</h4>
                                <small class="text-muted">متوسط وقت المراجعة (دقيقة)</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-primary">156</h4>
                                <small class="text-muted">التحويلات المراجعة اليوم</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize compliance dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDataTables();
            initializeCharts();
            loadComplianceData();
        });
        
        function initializeDataTables() {
            $('#pendingReviewsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                pageLength: 5,
                order: [[5, 'desc']]
            });
            
            $('#kycRequestsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                pageLength: 5,
                order: [[4, 'desc']]
            });
        }
        
        function initializeCharts() {
            // Risk Distribution Chart
            const ctx = document.getElementById('riskChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['منخفض', 'متوسط', 'عالي', 'حرج'],
                    datasets: [{
                        data: [65, 25, 8, 2],
                        backgroundColor: [
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#7c2d12'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function loadComplianceData() {
            // Load real compliance data
            // This would fetch from APIs
            console.log('Loading compliance data...');
        }
        
        function approveTransfer(transferCode) {
            if (confirm('هل أنت متأكد من الموافقة على هذا التحويل؟')) {
                // API call to approve transfer
                console.log('Approving transfer:', transferCode);
                alert('تم الموافقة على التحويل');
            }
        }
        
        function rejectTransfer(transferCode) {
            const reason = prompt('يرجى إدخال سبب الرفض:');
            if (reason) {
                // API call to reject transfer
                console.log('Rejecting transfer:', transferCode, 'Reason:', reason);
                alert('تم رفض التحويل');
            }
        }
        
        function approveKYC(kycId) {
            if (confirm('هل أنت متأكد من الموافقة على طلب KYC؟')) {
                // API call to approve KYC
                console.log('Approving KYC:', kycId);
                alert('تم الموافقة على طلب KYC');
            }
        }
        
        function rejectKYC(kycId) {
            const reason = prompt('يرجى إدخال سبب رفض KYC:');
            if (reason) {
                // API call to reject KYC
                console.log('Rejecting KYC:', kycId, 'Reason:', reason);
                alert('تم رفض طلب KYC');
            }
        }
        
        function reviewKYC(kycId) {
            // Open KYC review modal or page
            console.log('Reviewing KYC:', kycId);
            alert('فتح صفحة مراجعة KYC');
        }
        
        function generateComplianceReport() {
            alert('تصدير تقرير الامتثال - قريباً!');
        }
        
        function refreshAlerts() {
            location.reload();
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
