<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: /login');
    exit;
}

// Connect to database
try {
    $db = new PDO('sqlite:' . __DIR__ . '/../../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle transfer actions
$message = '';
$messageType = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_status':
                try {
                    $stmt = $db->prepare("UPDATE transfers SET status = ?, updated_at = datetime('now') WHERE id = ?");
                    $stmt->execute([$_POST['status'], $_POST['transfer_id']]);
                    $message = 'تم تحديث حالة التحويل بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'خطأ في تحديث حالة التحويل: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'add_note':
                try {
                    $stmt = $db->prepare("UPDATE transfers SET notes = ?, updated_at = datetime('now') WHERE id = ?");
                    $stmt->execute([$_POST['notes'], $_POST['transfer_id']]);
                    $message = 'تم إضافة الملاحظة بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'خطأ في إضافة الملاحظة: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get transfers with pagination and filters
$page = $_GET['page'] ?? 1;
$limit = 15;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$whereClause = '';
$params = [];

if ($search) {
    $whereClause .= " WHERE (t.transfer_code LIKE ? OR t.pickup_code LIKE ? OR t.sender_name LIKE ? OR t.receiver_name LIKE ?)";
    $params = ["%$search%", "%$search%", "%$search%", "%$search%"];
}

if ($status_filter) {
    $whereClause .= $whereClause ? " AND t.status = ?" : " WHERE t.status = ?";
    $params[] = $status_filter;
}

if ($date_from) {
    $whereClause .= $whereClause ? " AND DATE(t.created_at) >= ?" : " WHERE DATE(t.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $whereClause .= $whereClause ? " AND DATE(t.created_at) <= ?" : " WHERE DATE(t.created_at) <= ?";
    $params[] = $date_to;
}

// Get total count
$countSql = "SELECT COUNT(*) FROM transfers t" . $whereClause;
$countStmt = $db->prepare($countSql);
$countStmt->execute($params);
$totalTransfers = $countStmt->fetchColumn();
$totalPages = ceil($totalTransfers / $limit);

// Get transfers
$sql = "SELECT t.*, 
               sc.name as sender_country_name, sc.currency as sender_currency_name,
               rc.name as receiver_country_name, rc.currency as receiver_currency_name,
               u.name as sender_user_name
        FROM transfers t
        LEFT JOIN countries sc ON t.sender_country_id = sc.id
        LEFT JOIN countries rc ON t.receiver_country_id = rc.id
        LEFT JOIN users u ON t.sender_id = u.id" . 
        $whereClause . " ORDER BY t.created_at DESC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$stmt = $db->prepare($sql);
$stmt->execute($params);
$transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [
    'total' => $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn(),
    'pending' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'pending'")->fetchColumn(),
    'completed' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed'")->fetchColumn(),
    'processing' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'processing'")->fetchColumn(),
    'cancelled' => $db->query("SELECT COUNT(*) FROM transfers WHERE status = 'cancelled'")->fetchColumn(),
    'total_amount' => $db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn(),
    'today' => $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = DATE('now')")->fetchColumn()
];

// Status translations
$statusTranslations = [
    'pending' => 'في الانتظار',
    'pending_payment' => 'في انتظار الدفع',
    'paid' => 'تم الدفع',
    'processing' => 'قيد المعالجة',
    'ready_for_pickup' => 'جاهز للاستلام',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي',
    'refunded' => 'مسترد'
];

$statusColors = [
    'pending' => 'warning',
    'pending_payment' => 'info',
    'paid' => 'primary',
    'processing' => 'info',
    'ready_for_pickup' => 'success',
    'completed' => 'success',
    'cancelled' => 'danger',
    'refunded' => 'secondary'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .table td {
            font-size: 0.9rem;
            vertical-align: middle;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 6px 10px;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 0.75rem;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .transfer-amount {
            font-weight: 600;
            color: #28a745;
        }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4><i class="bi bi-bank me-2"></i>Elite Transfer</h4>
                    <small>نظام إدارة التحويلات</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                    </a>
                    <a class="nav-link" href="/admin/users">
                        <i class="bi bi-people me-2"></i>إدارة المستخدمين
                    </a>
                    <a class="nav-link active" href="/admin/transfers">
                        <i class="bi bi-arrow-left-right me-2"></i>إدارة التحويلات
                    </a>
                    <a class="nav-link" href="/admin/reports">
                        <i class="bi bi-graph-up me-2"></i>التقارير
                    </a>
                    <a class="nav-link" href="/admin/settings">
                        <i class="bi bi-gear me-2"></i>الإعدادات
                    </a>
                    <a class="nav-link" href="/compliance/dashboard">
                        <i class="bi bi-shield-check me-2"></i>الامتثال
                    </a>
                    <a class="nav-link" href="/admin/monitoring">
                        <i class="bi bi-activity me-2"></i>المراقبة
                    </a>
                </nav>
                
                <div class="mt-auto pt-4">
                    <div class="text-center">
                        <small>مرحباً، <?= htmlspecialchars($_SESSION['name']) ?></small>
                        <br>
                        <a href="/logout" class="btn btn-outline-light btn-sm mt-2">
                            <i class="bi bi-box-arrow-right me-1"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-arrow-left-right me-2"></i>إدارة التحويلات</h2>
                    <div>
                        <a href="/create-transfer" class="btn btn-success me-2">
                            <i class="bi bi-plus-circle me-2"></i>تحويل جديد
                        </a>
                        <a href="/track-transfer" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>تتبع التحويل
                        </a>
                    </div>
                </div>
                
                <!-- Alert Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-arrow-left-right fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['total'] ?></h4>
                                    <small>إجمالي التحويلات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card success">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['completed'] ?></h4>
                                    <small>مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card warning">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-clock fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['pending'] ?></h4>
                                    <small>في الانتظار</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card info">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-gear fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['processing'] ?></h4>
                                    <small>قيد المعالجة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card danger">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-x-circle fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['cancelled'] ?></h4>
                                    <small>ملغية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-calendar-day fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['today'] ?></h4>
                                    <small>اليوم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="البحث برمز التحويل أو الاسم" 
                                       value="<?= htmlspecialchars($search) ?>">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="">جميع الحالات</option>
                                    <?php foreach ($statusTranslations as $status => $translation): ?>
                                        <option value="<?= $status ?>" <?= $status_filter === $status ? 'selected' : '' ?>>
                                            <?= $translation ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="date_from" 
                                       placeholder="من تاريخ" value="<?= htmlspecialchars($date_from) ?>">
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="date_to" 
                                       placeholder="إلى تاريخ" value="<?= htmlspecialchars($date_to) ?>">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="/admin/transfers" class="btn btn-outline-secondary w-100">
                                    <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Transfers Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة التحويلات (<?= $totalTransfers ?> تحويل)</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رمز التحويل</th>
                                        <th>المرسل</th>
                                        <th>المستلم</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>طريقة الدفع</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transfers as $transfer): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <span class="transfer-code"><?= htmlspecialchars($transfer['transfer_code']) ?></span>
                                                    <br>
                                                    <small class="text-muted">رمز الاستلام: <?= htmlspecialchars($transfer['pickup_code']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($transfer['sender_name']) ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($transfer['sender_country_name']) ?></small>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($transfer['sender_phone']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($transfer['receiver_name']) ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($transfer['receiver_country_name']) ?></small>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($transfer['receiver_phone']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="transfer-amount">
                                                        <?= number_format($transfer['amount'], 2) ?> <?= $transfer['sender_currency'] ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        → <?= number_format($transfer['converted_amount'], 2) ?> <?= $transfer['receiver_currency'] ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-muted">رسوم: <?= number_format($transfer['fee_amount'], 2) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $statusColors[$transfer['status']] ?>">
                                                    <?= $statusTranslations[$transfer['status']] ?? $transfer['status'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <small>دفع: <?= ucfirst($transfer['payment_method']) ?></small>
                                                    <br>
                                                    <small>استلام: <?= ucfirst($transfer['pickup_method']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('Y-m-d', strtotime($transfer['created_at'])) ?>
                                                    <br>
                                                    <?= date('H:i', strtotime($transfer['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical" role="group">
                                                    <button class="btn btn-outline-primary btn-sm mb-1" 
                                                            onclick="viewTransfer(<?= htmlspecialchars(json_encode($transfer)) ?>)">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm mb-1" 
                                                            onclick="updateStatus(<?= $transfer['id'] ?>, '<?= $transfer['status'] ?>')">
                                                        <i class="bi bi-arrow-repeat"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm" 
                                                            onclick="addNote(<?= $transfer['id'] ?>, '<?= htmlspecialchars($transfer['notes'] ?? '') ?>')">
                                                        <i class="bi bi-chat-text"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- View Transfer Modal -->
    <div class="modal fade" id="viewTransferModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل التحويل</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transferDetails">
                    <!-- Transfer details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="window.print()">طباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث حالة التحويل</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="updateStatusForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="transfer_id" id="status_transfer_id">
                        
                        <div class="mb-3">
                            <label class="form-label">الحالة الجديدة</label>
                            <select class="form-select" name="status" id="new_status" required>
                                <?php foreach ($statusTranslations as $status => $translation): ?>
                                    <option value="<?= $status ?>"><?= $translation ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Note Modal -->
    <div class="modal fade" id="addNoteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة ملاحظة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="addNoteForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_note">
                        <input type="hidden" name="transfer_id" id="note_transfer_id">
                        
                        <div class="mb-3">
                            <label class="form-label">الملاحظة</label>
                            <textarea class="form-control" name="notes" id="transfer_notes" rows="4" 
                                      placeholder="أدخل ملاحظة حول التحويل..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ الملاحظة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function viewTransfer(transfer) {
            const statusTranslations = <?= json_encode($statusTranslations) ?>;
            
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات التحويل</h6>
                        <table class="table table-sm">
                            <tr><td><strong>رمز التحويل:</strong></td><td>${transfer.transfer_code}</td></tr>
                            <tr><td><strong>رمز الاستلام:</strong></td><td>${transfer.pickup_code}</td></tr>
                            <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-primary">${statusTranslations[transfer.status] || transfer.status}</span></td></tr>
                            <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${new Date(transfer.created_at).toLocaleString('ar-SA')}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">التفاصيل المالية</h6>
                        <table class="table table-sm">
                            <tr><td><strong>المبلغ المرسل:</strong></td><td>${parseFloat(transfer.amount).toFixed(2)} ${transfer.sender_currency}</td></tr>
                            <tr><td><strong>سعر الصرف:</strong></td><td>${parseFloat(transfer.exchange_rate).toFixed(4)}</td></tr>
                            <tr><td><strong>الرسوم:</strong></td><td>${parseFloat(transfer.fee_amount).toFixed(2)} ${transfer.sender_currency}</td></tr>
                            <tr><td><strong>المبلغ المستلم:</strong></td><td class="text-success"><strong>${parseFloat(transfer.converted_amount).toFixed(2)} ${transfer.receiver_currency}</strong></td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-info">معلومات المرسل</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم:</strong></td><td>${transfer.sender_name}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${transfer.sender_phone}</td></tr>
                            <tr><td><strong>البلد:</strong></td><td>${transfer.sender_country_name}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">معلومات المستلم</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم:</strong></td><td>${transfer.receiver_name}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${transfer.receiver_phone}</td></tr>
                            <tr><td><strong>البلد:</strong></td><td>${transfer.receiver_country_name}</td></tr>
                        </table>
                    </div>
                </div>
                ${transfer.notes ? `<div class="mt-3"><h6>الملاحظات</h6><p class="bg-light p-3 rounded">${transfer.notes}</p></div>` : ''}
            `;
            
            document.getElementById('transferDetails').innerHTML = details;
            new bootstrap.Modal(document.getElementById('viewTransferModal')).show();
        }
        
        function updateStatus(transferId, currentStatus) {
            document.getElementById('status_transfer_id').value = transferId;
            document.getElementById('new_status').value = currentStatus;
            
            new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
        }
        
        function addNote(transferId, currentNotes) {
            document.getElementById('note_transfer_id').value = transferId;
            document.getElementById('transfer_notes').value = currentNotes;
            
            new bootstrap.Modal(document.getElementById('addNoteModal')).show();
        }
    </script>
</body>
</html>
