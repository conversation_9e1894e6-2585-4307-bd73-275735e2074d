#!/usr/bin/env php
<?php

/**
 * Elite Transfer System - Database Setup Script
 * 
 * This script sets up the MySQL database for the Elite Transfer System
 * Usage: php setup_database.php
 */

echo "\n";
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║           Elite Transfer System - Database Setup             ║\n";
echo "║                     MySQL Configuration                      ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";
echo "\n";

// Load environment variables
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$config = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'elite_transfer',
    'username' => $_ENV['DB_USERNAME'] ?? 'elite_user',
    'password' => $_ENV['DB_PASSWORD'] ?? 'elite_password_2025',
    'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4'
];

echo "🔧 Database Configuration:\n";
echo "   Host: {$config['host']}:{$config['port']}\n";
echo "   Database: {$config['database']}\n";
echo "   Username: {$config['username']}\n";
echo "   Charset: {$config['charset']}\n\n";

// Step 1: Create database and user
echo "📋 Step 1: Creating database and user...\n";

try {
    // Connect to MySQL server (without database)
    $dsn = "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}";
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET {$config['charset']} COLLATE {$config['charset']}_unicode_ci");
    echo "   ✅ Database '{$config['database']}' created successfully\n";
    
    // Create user and grant privileges
    $pdo->exec("CREATE USER IF NOT EXISTS '{$config['username']}'@'localhost' IDENTIFIED BY '{$config['password']}'");
    $pdo->exec("GRANT ALL PRIVILEGES ON `{$config['database']}`.* TO '{$config['username']}'@'localhost'");
    $pdo->exec("FLUSH PRIVILEGES");
    echo "   ✅ User '{$config['username']}' created with full privileges\n";
    
} catch (PDOException $e) {
    echo "   ❌ Error creating database/user: " . $e->getMessage() . "\n";
    echo "   💡 Make sure MySQL is running and you have root access\n";
    echo "   💡 You may need to run this script as administrator\n\n";
    
    // Try to continue with existing database
    echo "🔄 Attempting to continue with existing database...\n";
}

// Step 2: Connect to the database and run setup
echo "\n📋 Step 2: Setting up database tables...\n";

try {
    // Connect to the specific database
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "   ✅ Connected to database successfully\n";
    
    // Read and execute SQL setup file
    $sqlFile = __DIR__ . '/database/mysql_setup.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL setup file not found: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty(trim($statement))) continue;
        
        try {
            $pdo->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            $errorCount++;
            echo "   ⚠️  Warning executing statement: " . $e->getMessage() . "\n";
        }
    }
    
    echo "   ✅ Executed {$successCount} SQL statements successfully\n";
    if ($errorCount > 0) {
        echo "   ⚠️  {$errorCount} statements had warnings (likely already existed)\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error setting up database: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 3: Verify database setup
echo "\n📋 Step 3: Verifying database setup...\n";

try {
    // Check if main tables exist
    $tables = [
        'users', 'countries', 'transfers', 'payments', 'exchange_rates',
        'notifications', 'aml_checks', 'kyc_documents', 'system_settings'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?");
        $stmt->execute([$config['database'], $table]);
        
        if ($stmt->fetchColumn() > 0) {
            $existingTables[] = $table;
        } else {
            $missingTables[] = $table;
        }
    }
    
    echo "   ✅ Found " . count($existingTables) . " tables: " . implode(', ', $existingTables) . "\n";
    
    if (!empty($missingTables)) {
        echo "   ❌ Missing tables: " . implode(', ', $missingTables) . "\n";
    }
    
    // Check data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries");
    $countryCount = $stmt->fetch()['count'];
    echo "   📊 Countries loaded: {$countryCount}\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $adminCount = $stmt->fetch()['count'];
    echo "   👤 Admin users: {$adminCount}\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_settings");
    $settingsCount = $stmt->fetch()['count'];
    echo "   ⚙️  System settings: {$settingsCount}\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM exchange_rates");
    $ratesCount = $stmt->fetch()['count'];
    echo "   💱 Exchange rates: {$ratesCount}\n";
    
} catch (Exception $e) {
    echo "   ❌ Error verifying database: " . $e->getMessage() . "\n";
}

// Step 4: Test application connection
echo "\n📋 Step 4: Testing application connection...\n";

try {
    require_once __DIR__ . '/app/Database/MySQLConnection.php';
    
    $mysqlConnection = MySQLConnection::getInstance();
    $testResult = $mysqlConnection->testConnection();
    
    if ($testResult['success']) {
        echo "   ✅ Application can connect to database successfully\n";
        echo "   📊 Database: {$testResult['database']}\n";
        echo "   🕐 Server time: {$testResult['server_time']}\n";
        
        // Get database statistics
        $stats = $mysqlConnection->getDatabaseStats();
        echo "   📈 Database statistics:\n";
        
        $totalSize = 0;
        foreach ($stats as $stat) {
            echo "      - {$stat['table_name']}: {$stat['row_count']} rows, {$stat['size_mb']} MB\n";
            $totalSize += $stat['size_mb'];
        }
        echo "   💾 Total database size: " . round($totalSize, 2) . " MB\n";
        
    } else {
        echo "   ❌ Application connection test failed: {$testResult['message']}\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error testing application connection: " . $e->getMessage() . "\n";
}

// Step 5: Create phpMyAdmin access info
echo "\n📋 Step 5: phpMyAdmin Access Information...\n";

echo "   🌐 phpMyAdmin URL: http://localhost/phpmyadmin\n";
echo "   👤 Username: {$config['username']}\n";
echo "   🔑 Password: {$config['password']}\n";
echo "   🗄️  Database: {$config['database']}\n";

// Final summary
echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 Database Setup Complete!\n";
echo str_repeat("=", 60) . "\n";

if (empty($missingTables)) {
    echo "✅ Status: SUCCESS - All tables created successfully\n";
    echo "🚀 Your Elite Transfer System is ready to use!\n\n";
    
    echo "📋 Next Steps:\n";
    echo "   1. Access phpMyAdmin: http://localhost/phpmyadmin\n";
    echo "   2. Login with username: {$config['username']}\n";
    echo "   3. Start the application: php -S localhost:8000 -t public\n";
    echo "   4. Visit: http://localhost:8000\n";
    echo "   5. Login as admin: <EMAIL> / password\n";
    
} else {
    echo "⚠️  Status: PARTIAL - Some tables are missing\n";
    echo "🔧 Please check the error messages above and fix any issues\n";
}

echo "\n💡 Troubleshooting:\n";
echo "   - Make sure MySQL/XAMPP is running\n";
echo "   - Check your database credentials in .env file\n";
echo "   - Ensure you have proper MySQL privileges\n";
echo "   - Check MySQL error logs for detailed information\n";

echo "\n📞 Support: If you need help, check the README.md file\n";
echo "\n";

?>
