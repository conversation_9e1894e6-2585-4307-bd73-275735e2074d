<?php

class MySQLConnection {
    private static $instance = null;
    private $connection;
    private $config;
    
    private function __construct() {
        $this->config = $this->getConfig();
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new MySQLConnection();
        }
        return self::$instance;
    }
    
    private function getConfig() {
        return [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'elite_transfer',
            'username' => $_ENV['DB_USERNAME'] ?? 'elite_user',
            'password' => $_ENV['DB_PASSWORD'] ?? 'elite_password_2025',
            'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ];
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            
            $this->connection = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                $this->config['options']
            );
            
            // Set timezone
            $this->connection->exec("SET time_zone = '+00:00'");
            
            // Enable strict mode
            $this->connection->exec("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed. Please check your configuration.");
        }
    }
    
    public function getConnection() {
        // Check if connection is still alive
        if (!$this->isConnected()) {
            $this->connect();
        }
        
        return $this->connection;
    }
    
    private function isConnected() {
        try {
            $this->connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    public function prepare($sql) {
        return $this->connection->prepare($sql);
    }
    
    public function query($sql) {
        return $this->connection->query($sql);
    }
    
    public function exec($sql) {
        return $this->connection->exec($sql);
    }
    
    /**
     * Execute a query with parameters
     */
    public function execute($sql, $params = []) {
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    /**
     * Fetch single row
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Insert data and return last insert ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($data);
        
        return $this->connection->lastInsertId();
    }
    
    /**
     * Update data
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setParts = [];
        foreach (array_keys($data) as $key) {
            $setParts[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->connection->prepare($sql);
        
        return $stmt->execute($params);
    }
    
    /**
     * Delete data
     */
    public function delete($table, $where, $whereParams = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute($whereParams);
    }
    
    /**
     * Check if table exists
     */
    public function tableExists($tableName) {
        $sql = "SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = ? AND table_name = ?";
        
        $stmt = $this->connection->prepare($sql);
        $stmt->execute([$this->config['database'], $tableName]);
        
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Get table structure
     */
    public function getTableStructure($tableName) {
        $sql = "DESCRIBE {$tableName}";
        return $this->fetchAll($sql);
    }
    
    /**
     * Get database statistics
     */
    public function getDatabaseStats() {
        $sql = "SELECT 
                    TABLE_NAME as table_name,
                    TABLE_ROWS as row_count,
                    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as size_mb
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = ?
                ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC";
        
        return $this->fetchAll($sql, [$this->config['database']]);
    }
    
    /**
     * Optimize database tables
     */
    public function optimizeTables() {
        $tables = $this->fetchAll("SHOW TABLES");
        $results = [];
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            try {
                $this->connection->exec("OPTIMIZE TABLE {$tableName}");
                $results[$tableName] = 'optimized';
            } catch (PDOException $e) {
                $results[$tableName] = 'failed: ' . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * Create database backup
     */
    public function createBackup($backupPath = null) {
        if (!$backupPath) {
            $backupPath = __DIR__ . '/../../storage/backups/';
        }
        
        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }
        
        $filename = 'elite_transfer_backup_' . date('Y-m-d_H-i-s') . '.sql';
        $fullPath = $backupPath . $filename;
        
        $command = sprintf(
            'mysqldump -h%s -P%s -u%s -p%s %s > %s',
            $this->config['host'],
            $this->config['port'],
            $this->config['username'],
            $this->config['password'],
            $this->config['database'],
            $fullPath
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            return [
                'success' => true,
                'filename' => $filename,
                'path' => $fullPath,
                'size' => filesize($fullPath)
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Backup failed',
                'output' => $output
            ];
        }
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $result = $this->fetchOne("SELECT 1 as test, NOW() as server_time");

            return [
                'success' => true,
                'message' => 'Database connection successful',
                'server_time' => $result['server_time'],
                'database' => $this->config['database'],
                'host' => $this->config['host']
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Database connection failed',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get connection info
     */
    public function getConnectionInfo() {
        try {
            $version = $this->fetchOne("SELECT VERSION() as version");
            $status = $this->fetchAll("SHOW STATUS WHERE Variable_name IN ('Threads_connected', 'Uptime', 'Questions')");
            
            $info = [
                'database' => $this->config['database'],
                'host' => $this->config['host'],
                'port' => $this->config['port'],
                'username' => $this->config['username'],
                'charset' => $this->config['charset'],
                'mysql_version' => $version['version'],
                'status' => []
            ];
            
            foreach ($status as $stat) {
                $info['status'][$stat['Variable_name']] = $stat['Value'];
            }
            
            return $info;
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
    
    public function __destruct() {
        $this->connection = null;
    }
}
