<?php
// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مدير النظام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .metric-card {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border-left: 4px solid #dc2626;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
        }
        
        .report-section {
            margin-bottom: 2rem;
        }
        
        .kpi-value {
            font-size: 2rem;
            font-weight: 700;
            color: #dc2626;
        }
        
        .kpi-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }
        
        .trend-up {
            color: #059669;
        }
        
        .trend-down {
            color: #dc2626;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .export-btn:hover {
            background: linear-gradient(135deg, #b91c1c, #991b1b);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/dashboard">
                <i class="bi bi-graph-up me-2"></i>
                Elite Transfer - التقارير
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/admin/users">
                    <i class="bi bi-people me-1"></i>
                    المستخدمين
                </a>
                <a class="nav-link text-white" href="/admin/transfers">
                    <i class="bi bi-arrow-left-right me-1"></i>
                    التحويلات
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-graph-up me-2"></i>
                            التقارير والإحصائيات
                        </h2>
                        <p class="text-white-50 mb-0">
                            تحليل شامل لأداء النظام والعمليات المالية
                        </p>
                    </div>
                    <div>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-light" onclick="exportReport('pdf')">
                                <i class="bi bi-file-pdf me-2"></i>PDF
                            </button>
                            <button class="btn btn-outline-light" onclick="exportReport('excel')">
                                <i class="bi bi-file-excel me-2"></i>Excel
                            </button>
                            <button class="btn btn-outline-light" onclick="refreshReports()">
                                <i class="bi bi-arrow-clockwise me-2"></i>تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="fromDate" value="2025-01-01">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="toDate" value="2025-01-15">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">نوع التقرير</label>
                                <select class="form-control" id="reportType">
                                    <option value="all">جميع التقارير</option>
                                    <option value="financial">التقارير المالية</option>
                                    <option value="operational">التقارير التشغيلية</option>
                                    <option value="compliance">تقارير الامتثال</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="generateReports()">
                                    <i class="bi bi-bar-chart me-2"></i>إنشاء التقارير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="kpi-value" id="totalRevenue">$125,430</div>
                                <div class="kpi-label">إجمالي الإيرادات</div>
                                <small class="trend-up">
                                    <i class="bi bi-arrow-up"></i> +12.5%
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="kpi-value" id="totalTransfers">1,247</div>
                                <div class="kpi-label">إجمالي التحويلات</div>
                                <small class="trend-up">
                                    <i class="bi bi-arrow-up"></i> +8.3%
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-arrow-left-right text-primary" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="kpi-value" id="avgTransferAmount">$1,205</div>
                                <div class="kpi-label">متوسط قيمة التحويل</div>
                                <small class="trend-down">
                                    <i class="bi bi-arrow-down"></i> -2.1%
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-calculator text-warning" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="kpi-value" id="successRate">98.7%</div>
                                <div class="kpi-label">معدل النجاح</div>
                                <small class="trend-up">
                                    <i class="bi bi-arrow-up"></i> +0.5%
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Revenue Chart -->
            <div class="col-xl-8 col-lg-7 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            الإيرادات الشهرية
                        </h5>
                        <button class="export-btn" onclick="exportChart('revenueChart')">
                            <i class="bi bi-download me-1"></i>تصدير
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Transfer Status Chart -->
            <div class="col-xl-4 col-lg-5 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart me-2"></i>
                            حالة التحويلات
                        </h5>
                        <button class="export-btn" onclick="exportChart('statusChart')">
                            <i class="bi bi-download me-1"></i>تصدير
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Country Analysis -->
        <div class="row mb-4">
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-globe me-2"></i>
                            التحويلات حسب البلد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="countryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Top Customers -->
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-people me-2"></i>
                            أفضل العملاء
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>عدد التحويلات</th>
                                        <th>إجمالي المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody id="topCustomersTable">
                                    <tr>
                                        <td>أحمد محمد</td>
                                        <td>25</td>
                                        <td>$15,430</td>
                                    </tr>
                                    <tr>
                                        <td>فاطمة علي</td>
                                        <td>18</td>
                                        <td>$12,250</td>
                                    </tr>
                                    <tr>
                                        <td>خالد سعد</td>
                                        <td>15</td>
                                        <td>$9,800</td>
                                    </tr>
                                    <tr>
                                        <td>مريم أحمد</td>
                                        <td>12</td>
                                        <td>$8,500</td>
                                    </tr>
                                    <tr>
                                        <td>عبدالله محمد</td>
                                        <td>10</td>
                                        <td>$7,200</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Compliance Reports -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>
                            تقارير الامتثال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h4 class="text-warning">15</h4>
                                    <small>تقارير AML</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h4 class="text-info">8</h4>
                                    <small>تقارير SAR</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h4 class="text-success">142</h4>
                                    <small>KYC مكتملة</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h4 class="text-danger">3</h4>
                                    <small>تحويلات مشبوهة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });
        
        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات ($)',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: '#dc2626',
                        backgroundColor: 'rgba(220, 38, 38, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            
            // Status Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مكتمل', 'قيد المعالجة', 'في الانتظار', 'ملغي'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: [
                            '#059669',
                            '#3b82f6',
                            '#f59e0b',
                            '#dc2626'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Country Chart
            const countryCtx = document.getElementById('countryChart').getContext('2d');
            new Chart(countryCtx, {
                type: 'bar',
                data: {
                    labels: ['مصر', 'الإمارات', 'الأردن', 'الكويت', 'لبنان'],
                    datasets: [{
                        label: 'عدد التحويلات',
                        data: [450, 320, 280, 180, 120],
                        backgroundColor: [
                            '#dc2626',
                            '#059669',
                            '#3b82f6',
                            '#f59e0b',
                            '#7c3aed'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        function generateReports() {
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            const reportType = document.getElementById('reportType').value;
            
            alert(`إنشاء التقارير:\nمن: ${fromDate}\nإلى: ${toDate}\nالنوع: ${reportType}`);
            
            // Refresh charts with new data
            initializeCharts();
        }
        
        function exportReport(format) {
            alert(`تصدير التقرير بصيغة: ${format.toUpperCase()}`);
        }
        
        function exportChart(chartId) {
            alert(`تصدير الرسم البياني: ${chartId}`);
        }
        
        function refreshReports() {
            alert('تحديث التقارير...');
            initializeCharts();
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
