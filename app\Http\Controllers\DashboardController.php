<?php

namespace App\Http\Controllers;

use App\Models\Transfer;
use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get user-specific statistics
        $stats = $this->getUserStatistics($user);
        
        // Get recent transfers
        $recentTransfers = $this->getRecentTransfers($user);
        
        // Get quick actions based on user role
        $quickActions = $this->getQuickActions($user);
        
        // Get notifications
        $notifications = $this->getNotifications($user);
        
        return view('dashboard.index', compact(
            'stats',
            'recentTransfers',
            'quickActions',
            'notifications'
        ));
    }

    /**
     * Get user-specific statistics
     */
    private function getUserStatistics(User $user): array
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();
        $thisYear = now()->startOfYear();

        if ($user->isAdmin()) {
            return [
                'total_transfers' => Transfer::count(),
                'today_transfers' => Transfer::whereDate('created_at', $today)->count(),
                'total_amount' => Transfer::sum('amount'),
                'today_amount' => Transfer::whereDate('created_at', $today)->sum('amount'),
                'pending_transfers' => Transfer::where('status', 'pending')->count(),
                'completed_transfers' => Transfer::where('status', 'completed')->count(),
                'total_users' => User::where('role', 'customer')->count(),
                'total_agents' => User::where('role', 'agent')->count(),
                'monthly_revenue' => Transfer::whereDate('created_at', '>=', $thisMonth)->sum('fee_amount'),
                'yearly_revenue' => Transfer::whereDate('created_at', '>=', $thisYear)->sum('fee_amount'),
            ];
        } elseif ($user->isAgent()) {
            return [
                'total_transfers' => Transfer::where('agent_id', $user->id)->count(),
                'today_transfers' => Transfer::where('agent_id', $user->id)
                    ->whereDate('created_at', $today)->count(),
                'pending_transfers' => Transfer::where('agent_id', $user->id)
                    ->where('status', 'pending')->count(),
                'completed_transfers' => Transfer::where('agent_id', $user->id)
                    ->where('status', 'completed')->count(),
                'monthly_commission' => Transfer::where('agent_id', $user->id)
                    ->whereDate('created_at', '>=', $thisMonth)
                    ->sum('commission_amount'),
                'total_commission' => Transfer::where('agent_id', $user->id)
                    ->sum('commission_amount'),
                'branch_transfers' => Transfer::where('branch_id', $user->branch_id)->count(),
                'ready_for_pickup' => Transfer::where('agent_id', $user->id)
                    ->where('status', 'ready_for_pickup')->count(),
            ];
        } else {
            return [
                'total_sent' => Transfer::where('sender_id', $user->id)->count(),
                'total_received' => Transfer::where('receiver_id', $user->id)->count(),
                'total_amount_sent' => Transfer::where('sender_id', $user->id)->sum('amount'),
                'total_amount_received' => Transfer::where('receiver_id', $user->id)->sum('converted_amount'),
                'pending_transfers' => Transfer::where('sender_id', $user->id)
                    ->where('status', 'pending')->count(),
                'completed_transfers' => Transfer::where('sender_id', $user->id)
                    ->where('status', 'completed')->count(),
                'monthly_sent' => Transfer::where('sender_id', $user->id)
                    ->whereDate('created_at', '>=', $thisMonth)->count(),
                'available_limit' => $user->daily_limit - Transfer::where('sender_id', $user->id)
                    ->whereDate('created_at', $today)->sum('amount'),
            ];
        }
    }

    /**
     * Get recent transfers based on user role
     */
    private function getRecentTransfers(User $user): \Illuminate\Database\Eloquent\Collection
    {
        $query = Transfer::with([
            'sender:id,name',
            'receiver:id,name', 
            'senderCountry:id,name,name_ar',
            'receiverCountry:id,name,name_ar',
            'senderCurrency:id,code,symbol',
            'receiverCurrency:id,code,symbol'
        ]);

        if ($user->isAdmin()) {
            $query->latest()->limit(10);
        } elseif ($user->isAgent()) {
            $query->where(function($q) use ($user) {
                $q->where('agent_id', $user->id)
                  ->orWhere('branch_id', $user->branch_id);
            })->latest()->limit(10);
        } else {
            $query->where(function($q) use ($user) {
                $q->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
            })->latest()->limit(5);
        }

        return $query->get();
    }

    /**
     * Get quick actions based on user role
     */
    private function getQuickActions(User $user): array
    {
        if ($user->isAdmin()) {
            return [
                [
                    'title' => 'Manage Transfers',
                    'description' => 'View and manage all transfers',
                    'icon' => 'bi-list-ul',
                    'url' => route('admin.transfers'),
                    'color' => 'primary'
                ],
                [
                    'title' => 'User Management',
                    'description' => 'Manage users and agents',
                    'icon' => 'bi-people',
                    'url' => route('admin.users'),
                    'color' => 'success'
                ],
                [
                    'title' => 'Reports',
                    'description' => 'View financial reports',
                    'icon' => 'bi-graph-up',
                    'url' => route('admin.reports.transfers'),
                    'color' => 'info'
                ],
                [
                    'title' => 'System Settings',
                    'description' => 'Configure system settings',
                    'icon' => 'bi-gear',
                    'url' => route('admin.settings'),
                    'color' => 'warning'
                ]
            ];
        } elseif ($user->isAgent()) {
            return [
                [
                    'title' => 'Process Transfers',
                    'description' => 'Handle pending transfers',
                    'icon' => 'bi-arrow-repeat',
                    'url' => route('agent.transfers.pending'),
                    'color' => 'primary'
                ],
                [
                    'title' => 'Ready for Pickup',
                    'description' => 'Transfers ready for collection',
                    'icon' => 'bi-check-circle',
                    'url' => route('agent.transfers.ready-pickup'),
                    'color' => 'success'
                ],
                [
                    'title' => 'Add Customer',
                    'description' => 'Register new customer',
                    'icon' => 'bi-person-plus',
                    'url' => route('agent.customers.create'),
                    'color' => 'info'
                ],
                [
                    'title' => 'Daily Report',
                    'description' => 'View today\'s activity',
                    'icon' => 'bi-file-text',
                    'url' => route('agent.reports.daily'),
                    'color' => 'warning'
                ]
            ];
        } else {
            return [
                [
                    'title' => 'Send Money',
                    'description' => 'Send money worldwide',
                    'icon' => 'bi-send',
                    'url' => route('transfers.create'),
                    'color' => 'primary'
                ],
                [
                    'title' => 'Track Transfer',
                    'description' => 'Track your transfers',
                    'icon' => 'bi-search',
                    'url' => route('track.form'),
                    'color' => 'success'
                ],
                [
                    'title' => 'My Transfers',
                    'description' => 'View transfer history',
                    'icon' => 'bi-list-ul',
                    'url' => route('transfers.my'),
                    'color' => 'info'
                ],
                [
                    'title' => 'Profile Settings',
                    'description' => 'Update your profile',
                    'icon' => 'bi-person-gear',
                    'url' => route('profile.show'),
                    'color' => 'warning'
                ]
            ];
        }
    }

    /**
     * Get user notifications
     */
    private function getNotifications(User $user): array
    {
        $notifications = [];

        // KYC status notification
        if ($user->kyc_status === 'pending') {
            $notifications[] = [
                'type' => 'warning',
                'icon' => 'bi-exclamation-triangle',
                'title' => 'KYC Verification Pending',
                'message' => 'Please complete your KYC verification to increase your transfer limits.',
                'action' => [
                    'text' => 'Complete KYC',
                    'url' => route('profile.show') . '#kyc'
                ]
            ];
        }

        // 2FA notification
        if (!$user->two_factor_enabled) {
            $notifications[] = [
                'type' => 'info',
                'icon' => 'bi-shield-check',
                'title' => 'Enable Two-Factor Authentication',
                'message' => 'Secure your account with 2FA for enhanced security.',
                'action' => [
                    'text' => 'Enable 2FA',
                    'url' => route('profile.show') . '#security'
                ]
            ];
        }

        // Transfer limit notification
        if ($user->isCustomer()) {
            $todayUsed = Transfer::where('sender_id', $user->id)
                ->whereDate('created_at', today())
                ->sum('amount');
            
            $limitPercentage = ($todayUsed / $user->daily_limit) * 100;
            
            if ($limitPercentage > 80) {
                $notifications[] = [
                    'type' => 'warning',
                    'icon' => 'bi-exclamation-circle',
                    'title' => 'Daily Limit Alert',
                    'message' => "You've used {$limitPercentage}% of your daily transfer limit.",
                    'action' => null
                ];
            }
        }

        // Agent-specific notifications
        if ($user->isAgent()) {
            $pendingCount = Transfer::where('agent_id', $user->id)
                ->where('status', 'pending')
                ->count();
            
            if ($pendingCount > 0) {
                $notifications[] = [
                    'type' => 'primary',
                    'icon' => 'bi-clock',
                    'title' => 'Pending Transfers',
                    'message' => "You have {$pendingCount} transfers waiting for processing.",
                    'action' => [
                        'text' => 'Process Now',
                        'url' => route('agent.transfers.pending')
                    ]
                ];
            }
        }

        return $notifications;
    }

    /**
     * Get dashboard chart data
     */
    public function getChartData(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', '7days');
        
        $data = [];
        
        if ($period === '7days') {
            $dates = collect();
            for ($i = 6; $i >= 0; $i--) {
                $dates->push(now()->subDays($i)->format('Y-m-d'));
            }
            
            $transfers = Transfer::selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as amount')
                ->where('created_at', '>=', now()->subDays(7))
                ->when(!$user->isAdmin(), function($query) use ($user) {
                    if ($user->isAgent()) {
                        $query->where('agent_id', $user->id);
                    } else {
                        $query->where('sender_id', $user->id);
                    }
                })
                ->groupBy('date')
                ->get()
                ->keyBy('date');
            
            $data = $dates->map(function($date) use ($transfers) {
                $transfer = $transfers->get($date);
                return [
                    'date' => $date,
                    'count' => $transfer ? $transfer->count : 0,
                    'amount' => $transfer ? $transfer->amount : 0,
                ];
            });
        }
        
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}
