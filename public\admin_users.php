<?php
// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مدير النظام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active { background: #d1fae5; color: #065f46; }
        .status-inactive { background: #fee2e2; color: #991b1b; }
        .status-pending { background: #fef3c7; color: #92400e; }
        
        .role-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .role-admin { background: #dbeafe; color: #1e40af; }
        .role-agent { background: #d1fae5; color: #065f46; }
        .role-customer { background: #f3e8ff; color: #7c3aed; }
        
        .btn-action {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 8px;
            border: none;
            font-size: 0.8rem;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #dc2626 !important;
            border-color: #dc2626 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/dashboard">
                <i class="bi bi-people-fill me-2"></i>
                Elite Transfer - إدارة المستخدمين
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/admin/transfers">
                    <i class="bi bi-arrow-left-right me-1"></i>
                    التحويلات
                </a>
                <a class="nav-link text-white" href="/admin/reports">
                    <i class="bi bi-graph-up me-1"></i>
                    التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-people-fill me-2"></i>
                            إدارة المستخدمين
                        </h2>
                        <p class="text-white-50 mb-0">
                            إدارة حسابات المستخدمين والصلاحيات
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-outline-light me-2" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة مستخدم جديد
                        </button>
                        <button class="btn btn-outline-light" onclick="refreshUsers()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3">
                                <i class="bi bi-people"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="totalUsers">0</h5>
                                <p class="card-text text-muted mb-0">إجمالي المستخدمين</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #059669, #047857);">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="activeUsers">0</h5>
                                <p class="card-text text-muted mb-0">المستخدمين النشطين</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #7c3aed, #6d28d9);">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="adminUsers">0</h5>
                                <p class="card-text text-muted mb-0">المديرين</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <i class="bi bi-person-exclamation"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1" id="pendingUsers">0</h5>
                                <p class="card-text text-muted mb-0">في انتظار التحقق</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-table me-2"></i>
                            قائمة المستخدمين
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الدور</th>
                                        <th>حالة KYC</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- Users will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة مستخدم جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userName" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="userName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="userEmail" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="userPhone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userRole" class="form-label">الدور</label>
                                <select class="form-control" id="userRole" required>
                                    <option value="">اختر الدور</option>
                                    <option value="customer">عميل</option>
                                    <option value="agent">وكيل</option>
                                    <option value="admin">مدير</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="userPassword" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="kycStatus" class="form-label">حالة KYC</label>
                                <select class="form-control" id="kycStatus">
                                    <option value="pending">في الانتظار</option>
                                    <option value="verified">مُحقق</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userStatus" class="form-label">حالة الحساب</label>
                                <select class="form-control" id="userStatus">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addUser()">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة المستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-gear me-2"></i>
                        تعديل المستخدم
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editUserName" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="editUserName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editUserEmail" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="editUserEmail" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editUserPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="editUserPhone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editUserRole" class="form-label">الدور</label>
                                <select class="form-control" id="editUserRole" required>
                                    <option value="customer">عميل</option>
                                    <option value="agent">وكيل</option>
                                    <option value="admin">مدير</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editKycStatus" class="form-label">حالة KYC</label>
                                <select class="form-control" id="editKycStatus">
                                    <option value="pending">في الانتظار</option>
                                    <option value="verified">مُحقق</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editUserStatus" class="form-label">حالة الحساب</label>
                                <select class="form-control" id="editUserStatus">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">
                        <i class="bi bi-check-lg me-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- DataTables -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            initializeDataTable();
        });
        
        let usersTable;
        
        function initializeDataTable() {
            usersTable = $('#usersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                pageLength: 10,
                order: [[6, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        }
        
        function loadUsers() {
            // Mock data - replace with actual API call
            const mockUsers = [
                {
                    id: 1,
                    name: 'System Administrator',
                    email: '<EMAIL>',
                    phone: '+966501234567',
                    role: 'admin',
                    kyc_status: 'verified',
                    is_active: 1,
                    created_at: '2025-01-01'
                },
                {
                    id: 2,
                    name: 'Demo Customer',
                    email: '<EMAIL>',
                    phone: '+966501234568',
                    role: 'customer',
                    kyc_status: 'verified',
                    is_active: 1,
                    created_at: '2025-01-02'
                },
                {
                    id: 3,
                    name: 'أحمد محمد',
                    email: '<EMAIL>',
                    phone: '+966501234569',
                    role: 'customer',
                    kyc_status: 'pending',
                    is_active: 1,
                    created_at: '2025-01-10'
                },
                {
                    id: 4,
                    name: 'فاطمة علي',
                    email: '<EMAIL>',
                    phone: '+966501234570',
                    role: 'agent',
                    kyc_status: 'verified',
                    is_active: 1,
                    created_at: '2025-01-12'
                }
            ];
            
            displayUsers(mockUsers);
            updateStatistics(mockUsers);
        }
        
        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';
            
            users.forEach(user => {
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    ${user.name.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <strong>${user.name}</strong>
                                </div>
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td>${user.phone || '-'}</td>
                        <td>
                            <span class="role-badge role-${user.role}">
                                ${getRoleText(user.role)}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${user.kyc_status}">
                                ${getKycStatusText(user.kyc_status)}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge ${user.is_active ? 'status-active' : 'status-inactive'}">
                                ${user.is_active ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>${formatDate(user.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-primary btn-action" onclick="editUser(${user.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-info btn-action" onclick="viewUser(${user.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="toggleUserStatus(${user.id})" title="تغيير الحالة">
                                <i class="bi bi-toggle-on"></i>
                            </button>
                            ${user.role !== 'admin' ? `
                            <button class="btn btn-sm btn-danger btn-action" onclick="deleteUser(${user.id})" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
            
            // Refresh DataTable
            if (usersTable) {
                usersTable.destroy();
                initializeDataTable();
            }
        }
        
        function updateStatistics(users) {
            document.getElementById('totalUsers').textContent = users.length;
            document.getElementById('activeUsers').textContent = users.filter(u => u.is_active).length;
            document.getElementById('adminUsers').textContent = users.filter(u => u.role === 'admin').length;
            document.getElementById('pendingUsers').textContent = users.filter(u => u.kyc_status === 'pending').length;
        }
        
        function getRoleText(role) {
            const roles = {
                'admin': 'مدير',
                'agent': 'وكيل',
                'customer': 'عميل'
            };
            return roles[role] || role;
        }
        
        function getKycStatusText(status) {
            const statuses = {
                'pending': 'في الانتظار',
                'verified': 'مُحقق',
                'rejected': 'مرفوض'
            };
            return statuses[status] || status;
        }
        
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }
        
        function addUser() {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);
            
            // Validate passwords match
            const password = document.getElementById('userPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                alert('كلمات المرور غير متطابقة');
                return;
            }
            
            // Mock API call
            alert('تم إضافة المستخدم بنجاح');
            $('#addUserModal').modal('hide');
            form.reset();
            loadUsers();
        }
        
        function editUser(userId) {
            // Mock data loading
            const user = {
                id: userId,
                name: 'أحمد محمد',
                email: '<EMAIL>',
                phone: '+966501234569',
                role: 'customer',
                kyc_status: 'pending',
                is_active: 1
            };
            
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUserName').value = user.name;
            document.getElementById('editUserEmail').value = user.email;
            document.getElementById('editUserPhone').value = user.phone;
            document.getElementById('editUserRole').value = user.role;
            document.getElementById('editKycStatus').value = user.kyc_status;
            document.getElementById('editUserStatus').value = user.is_active;
            
            $('#editUserModal').modal('show');
        }
        
        function updateUser() {
            // Mock API call
            alert('تم تحديث المستخدم بنجاح');
            $('#editUserModal').modal('hide');
            loadUsers();
        }
        
        function viewUser(userId) {
            alert(`عرض تفاصيل المستخدم رقم: ${userId}`);
        }
        
        function toggleUserStatus(userId) {
            if (confirm('هل أنت متأكد من تغيير حالة المستخدم؟')) {
                alert('تم تغيير حالة المستخدم');
                loadUsers();
            }
        }
        
        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                alert('تم حذف المستخدم');
                loadUsers();
            }
        }
        
        function refreshUsers() {
            loadUsers();
            alert('تم تحديث قائمة المستخدمين');
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
