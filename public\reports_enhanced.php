<?php
// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

// Require login
require_login();
$userData = get_user_data();

if (!in_array($userData['role'], ['admin', 'manager', 'compliance'])) {
    header('Location: /dashboard');
    exit;
}

// Connect to production database
try {
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // Enable foreign keys and performance optimizations
    $db->exec("PRAGMA foreign_keys = ON");
    $db->exec("PRAGMA journal_mode = WAL");
    $db->exec("PRAGMA synchronous = NORMAL");
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get date range
$dateFrom = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$dateTo = $_GET['date_to'] ?? date('Y-m-d'); // Today
$reportType = $_GET['type'] ?? 'overview';

// Generate comprehensive reports
function generateOverviewReport($db, $dateFrom, $dateTo) {
    $report = [];
    
    // Total transfers
    $report['total_transfers'] = $db->prepare("
        SELECT COUNT(*) FROM transfers 
        WHERE DATE(created_at) BETWEEN ? AND ? AND deleted_at IS NULL
    ")->execute([$dateFrom, $dateTo]) ? $db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' AND deleted_at IS NULL")->fetchColumn() : 0;
    
    // Total amount
    $report['total_amount'] = $db->query("
        SELECT COALESCE(SUM(amount), 0) FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND status = 'completed' AND deleted_at IS NULL
    ")->fetchColumn();
    
    // Total fees
    $report['total_fees'] = $db->query("
        SELECT COALESCE(SUM(fee_amount), 0) FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND status = 'completed' AND deleted_at IS NULL
    ")->fetchColumn();
    
    // Status breakdown
    $report['status_breakdown'] = $db->query("
        SELECT status, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount
        FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' AND deleted_at IS NULL
        GROUP BY status
        ORDER BY count DESC
    ")->fetchAll();
    
    // Country breakdown
    $report['country_breakdown'] = $db->query("
        SELECT 
            sc.name as sender_country,
            rc.name as receiver_country,
            COUNT(*) as transfer_count,
            COALESCE(SUM(t.amount), 0) as total_amount
        FROM transfers t
        LEFT JOIN countries sc ON t.sender_country_id = sc.id
        LEFT JOIN countries rc ON t.receiver_country_id = rc.id
        WHERE DATE(t.created_at) BETWEEN '$dateFrom' AND '$dateTo' AND t.deleted_at IS NULL
        GROUP BY t.sender_country_id, t.receiver_country_id
        ORDER BY transfer_count DESC
        LIMIT 10
    ")->fetchAll();
    
    // Daily trends
    $report['daily_trends'] = $db->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as transfer_count,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(SUM(fee_amount), 0) as total_fees
        FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' AND deleted_at IS NULL
        GROUP BY DATE(created_at)
        ORDER BY date
    ")->fetchAll();
    
    // Top agents
    $report['top_agents'] = $db->query("
        SELECT 
            u.name as agent_name,
            COUNT(t.id) as transfer_count,
            COALESCE(SUM(t.amount), 0) as total_amount,
            COALESCE(SUM(t.fee_amount), 0) as total_fees
        FROM transfers t
        LEFT JOIN users u ON t.agent_id = u.id
        WHERE DATE(t.created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND t.deleted_at IS NULL AND u.role = 'agent'
        GROUP BY t.agent_id
        ORDER BY transfer_count DESC
        LIMIT 10
    ")->fetchAll();
    
    return $report;
}

function generateComplianceReport($db, $dateFrom, $dateTo) {
    $report = [];
    
    // High risk transfers
    $report['high_risk_transfers'] = $db->query("
        SELECT COUNT(*) FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND risk_score >= 70 AND deleted_at IS NULL
    ")->fetchColumn();
    
    // Flagged transfers
    $report['flagged_transfers'] = $db->query("
        SELECT COUNT(*) FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND aml_status = 'flagged' AND deleted_at IS NULL
    ")->fetchColumn();
    
    // Pending compliance review
    $report['pending_compliance'] = $db->query("
        SELECT COUNT(*) FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND compliance_status = 'pending' AND deleted_at IS NULL
    ")->fetchColumn();
    
    // KYC status breakdown
    $report['kyc_breakdown'] = $db->query("
        SELECT kyc_status, COUNT(*) as count
        FROM users 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' AND deleted_at IS NULL
        GROUP BY kyc_status
    ")->fetchAll();
    
    // Risk score distribution
    $report['risk_distribution'] = $db->query("
        SELECT 
            CASE 
                WHEN risk_score >= 70 THEN 'High (70+)'
                WHEN risk_score >= 40 THEN 'Medium (40-69)'
                ELSE 'Low (0-39)'
            END as risk_level,
            COUNT(*) as count,
            COALESCE(SUM(amount), 0) as total_amount
        FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN risk_score >= 70 THEN 'High (70+)'
                WHEN risk_score >= 40 THEN 'Medium (40-69)'
                ELSE 'Low (0-39)'
            END
    ")->fetchAll();
    
    return $report;
}

function generateFinancialReport($db, $dateFrom, $dateTo) {
    $report = [];
    
    // Revenue breakdown
    $report['revenue'] = $db->query("
        SELECT 
            COALESCE(SUM(fee_amount), 0) as total_fees,
            COALESCE(SUM(tax_amount), 0) as total_taxes,
            COALESCE(SUM(profit_amount), 0) as total_profit,
            COUNT(*) as completed_transfers
        FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND status = 'completed' AND deleted_at IS NULL
    ")->fetch();
    
    // Currency breakdown
    $report['currency_breakdown'] = $db->query("
        SELECT 
            sender_currency,
            COUNT(*) as transfer_count,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(SUM(fee_amount), 0) as total_fees
        FROM transfers 
        WHERE DATE(created_at) BETWEEN '$dateFrom' AND '$dateTo' 
        AND status = 'completed' AND deleted_at IS NULL
        GROUP BY sender_currency
        ORDER BY total_amount DESC
    ")->fetchAll();
    
    // Monthly comparison
    $report['monthly_comparison'] = $db->query("
        SELECT 
            strftime('%Y-%m', created_at) as month,
            COUNT(*) as transfer_count,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(SUM(fee_amount), 0) as total_fees
        FROM transfers 
        WHERE status = 'completed' AND deleted_at IS NULL
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month DESC
        LIMIT 12
    ")->fetchAll();
    
    return $report;
}

// Generate report based on type
$reportData = [];
switch ($reportType) {
    case 'compliance':
        $reportData = generateComplianceReport($db, $dateFrom, $dateTo);
        break;
    case 'financial':
        $reportData = generateFinancialReport($db, $dateFrom, $dateTo);
        break;
    default:
        $reportData = generateOverviewReport($db, $dateFrom, $dateTo);
        break;
}

// Status translations
$statusTranslations = [
    'pending' => 'في الانتظار',
    'pending_payment' => 'في انتظار الدفع',
    'paid' => 'تم الدفع',
    'processing' => 'قيد المعالجة',
    'ready_for_pickup' => 'جاهز للاستلام',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي',
    'refunded' => 'مسترد',
    'failed' => 'فاشل',
    'on_hold' => 'معلق'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المتقدمة - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .btn-export {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .btn-export:hover {
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-1">التقارير المتقدمة</h1>
                    <p class="mb-0">تحليل شامل لأداء النظام والعمليات المالية</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/dashboard" class="btn btn-light">
                        <i class="bi bi-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select class="form-select" name="type">
                        <option value="overview" <?= $reportType === 'overview' ? 'selected' : '' ?>>نظرة عامة</option>
                        <option value="financial" <?= $reportType === 'financial' ? 'selected' : '' ?>>التقرير المالي</option>
                        <option value="compliance" <?= $reportType === 'compliance' ? 'selected' : '' ?>>تقرير الامتثال</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" value="<?= $dateFrom ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" value="<?= $dateTo ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-1"></i>
                            إنشاء التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Report Content -->
        <?php if ($reportType === 'overview'): ?>
            <!-- Overview Report -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?= number_format($reportData['total_transfers']) ?></h3>
                    <p>إجمالي التحويلات</p>
                </div>
                <div class="stat-card">
                    <h3>$<?= number_format($reportData['total_amount'], 0) ?></h3>
                    <p>إجمالي المبلغ</p>
                </div>
                <div class="stat-card">
                    <h3>$<?= number_format($reportData['total_fees'], 0) ?></h3>
                    <p>إجمالي الرسوم</p>
                </div>
                <div class="stat-card">
                    <h3><?= number_format(($reportData['total_fees'] / max($reportData['total_amount'], 1)) * 100, 2) ?>%</h3>
                    <p>نسبة الرسوم</p>
                </div>
            </div>

            <!-- Status Breakdown -->
            <div class="table-container">
                <div class="p-3 border-bottom">
                    <h5 class="mb-0">توزيع حالات التحويل</h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>عدد التحويلات</th>
                                <th>إجمالي المبلغ</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData['status_breakdown'] as $status): ?>
                            <tr>
                                <td><?= $statusTranslations[$status['status']] ?? $status['status'] ?></td>
                                <td><?= number_format($status['count']) ?></td>
                                <td>$<?= number_format($status['total_amount'], 0) ?></td>
                                <td><?= number_format(($status['count'] / max($reportData['total_transfers'], 1)) * 100, 1) ?>%</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Country Breakdown -->
            <div class="table-container">
                <div class="p-3 border-bottom">
                    <h5 class="mb-0">أهم المسارات (الدول)</h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>المسار</th>
                                <th>عدد التحويلات</th>
                                <th>إجمالي المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData['country_breakdown'] as $route): ?>
                            <tr>
                                <td><?= htmlspecialchars($route['sender_country']) ?> → <?= htmlspecialchars($route['receiver_country']) ?></td>
                                <td><?= number_format($route['transfer_count']) ?></td>
                                <td>$<?= number_format($route['total_amount'], 0) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

        <?php elseif ($reportType === 'compliance'): ?>
            <!-- Compliance Report -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?= number_format($reportData['high_risk_transfers']) ?></h3>
                    <p>تحويلات عالية المخاطر</p>
                </div>
                <div class="stat-card">
                    <h3><?= number_format($reportData['flagged_transfers']) ?></h3>
                    <p>تحويلات مشبوهة</p>
                </div>
                <div class="stat-card">
                    <h3><?= number_format($reportData['pending_compliance']) ?></h3>
                    <p>مراجعة معلقة</p>
                </div>
            </div>

            <!-- Risk Distribution -->
            <div class="table-container">
                <div class="p-3 border-bottom">
                    <h5 class="mb-0">توزيع المخاطر</h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>مستوى المخاطر</th>
                                <th>عدد التحويلات</th>
                                <th>إجمالي المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData['risk_distribution'] as $risk): ?>
                            <tr>
                                <td><?= htmlspecialchars($risk['risk_level']) ?></td>
                                <td><?= number_format($risk['count']) ?></td>
                                <td>$<?= number_format($risk['total_amount'], 0) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

        <?php elseif ($reportType === 'financial'): ?>
            <!-- Financial Report -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>$<?= number_format($reportData['revenue']['total_fees'], 0) ?></h3>
                    <p>إجمالي الرسوم</p>
                </div>
                <div class="stat-card">
                    <h3>$<?= number_format($reportData['revenue']['total_taxes'], 0) ?></h3>
                    <p>إجمالي الضرائب</p>
                </div>
                <div class="stat-card">
                    <h3>$<?= number_format($reportData['revenue']['total_profit'], 0) ?></h3>
                    <p>صافي الربح</p>
                </div>
                <div class="stat-card">
                    <h3><?= number_format($reportData['revenue']['completed_transfers']) ?></h3>
                    <p>التحويلات المكتملة</p>
                </div>
            </div>

            <!-- Currency Breakdown -->
            <div class="table-container">
                <div class="p-3 border-bottom">
                    <h5 class="mb-0">توزيع العملات</h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>العملة</th>
                                <th>عدد التحويلات</th>
                                <th>إجمالي المبلغ</th>
                                <th>إجمالي الرسوم</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData['currency_breakdown'] as $currency): ?>
                            <tr>
                                <td><?= htmlspecialchars($currency['sender_currency']) ?></td>
                                <td><?= number_format($currency['transfer_count']) ?></td>
                                <td><?= number_format($currency['total_amount'], 0) ?> <?= htmlspecialchars($currency['sender_currency']) ?></td>
                                <td><?= number_format($currency['total_fees'], 0) ?> <?= htmlspecialchars($currency['sender_currency']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- Export Options -->
        <div class="text-center mb-4">
            <button class="btn btn-export me-2" onclick="exportToPDF()">
                <i class="bi bi-file-pdf me-1"></i>
                تصدير PDF
            </button>
            <button class="btn btn-export me-2" onclick="exportToExcel()">
                <i class="bi bi-file-excel me-1"></i>
                تصدير Excel
            </button>
            <button class="btn btn-export" onclick="window.print()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function exportToPDF() {
            // Implement PDF export
            alert('سيتم تطوير تصدير PDF قريباً');
        }
        
        function exportToExcel() {
            // Implement Excel export
            alert('سيتم تطوير تصدير Excel قريباً');
        }
    </script>
</body>
</html>
