@extends('layouts.app')

@section('title', 'Dashboard - Elite Financial Transfer System')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-white mb-1">
                        <i class="bi bi-speedometer2 me-2"></i>
                        {{ __('Dashboard') }}
                    </h2>
                    <p class="text-white-50 mb-0">
                        {{ __('Welcome back,') }} {{ Auth::user()->name }}!
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-light" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        {{ __('Refresh') }}
                    </button>
                    @if(Auth::user()->isCustomer())
                        <a href="{{ route('transfers.create') }}" class="btn btn-primary">
                            <i class="bi bi-send me-2"></i>
                            {{ __('Send Money') }}
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    @if(count($notifications) > 0)
        <div class="row mb-4">
            <div class="col-12">
                @foreach($notifications as $notification)
                    <div class="alert alert-{{ $notification['type'] }} alert-dismissible fade show" role="alert">
                        <i class="{{ $notification['icon'] }} me-2"></i>
                        <strong>{{ $notification['title'] }}</strong> {{ $notification['message'] }}
                        @if($notification['action'])
                            <a href="{{ $notification['action']['url'] }}" class="btn btn-sm btn-outline-{{ $notification['type'] }} ms-3">
                                {{ $notification['action']['text'] }}
                            </a>
                        @endif
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row mb-4">
        @if(Auth::user()->isAdmin())
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-primary">
                    <h3>{{ number_format($stats['total_transfers']) }}</h3>
                    <p class="mb-0">{{ __('Total Transfers') }}</p>
                    <small>+{{ $stats['today_transfers'] }} {{ __('today') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-success">
                    <h3>${{ number_format($stats['total_amount'], 2) }}</h3>
                    <p class="mb-0">{{ __('Total Volume') }}</p>
                    <small>${{ number_format($stats['today_amount'], 2) }} {{ __('today') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-warning">
                    <h3>{{ number_format($stats['pending_transfers']) }}</h3>
                    <p class="mb-0">{{ __('Pending Transfers') }}</p>
                    <small>{{ number_format($stats['completed_transfers']) }} {{ __('completed') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-info">
                    <h3>${{ number_format($stats['monthly_revenue'], 2) }}</h3>
                    <p class="mb-0">{{ __('Monthly Revenue') }}</p>
                    <small>${{ number_format($stats['yearly_revenue'], 2) }} {{ __('this year') }}</small>
                </div>
            </div>
        @elseif(Auth::user()->isAgent())
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-primary">
                    <h3>{{ number_format($stats['total_transfers']) }}</h3>
                    <p class="mb-0">{{ __('My Transfers') }}</p>
                    <small>+{{ $stats['today_transfers'] }} {{ __('today') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-warning">
                    <h3>{{ number_format($stats['pending_transfers']) }}</h3>
                    <p class="mb-0">{{ __('Pending') }}</p>
                    <small>{{ number_format($stats['ready_for_pickup']) }} {{ __('ready for pickup') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-success">
                    <h3>{{ number_format($stats['completed_transfers']) }}</h3>
                    <p class="mb-0">{{ __('Completed') }}</p>
                    <small>{{ __('This month') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-info">
                    <h3>${{ number_format($stats['monthly_commission'], 2) }}</h3>
                    <p class="mb-0">{{ __('Monthly Commission') }}</p>
                    <small>${{ number_format($stats['total_commission'], 2) }} {{ __('total') }}</small>
                </div>
            </div>
        @else
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-primary">
                    <h3>{{ number_format($stats['total_sent']) }}</h3>
                    <p class="mb-0">{{ __('Transfers Sent') }}</p>
                    <small>{{ number_format($stats['monthly_sent']) }} {{ __('this month') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-success">
                    <h3>${{ number_format($stats['total_amount_sent'], 2) }}</h3>
                    <p class="mb-0">{{ __('Amount Sent') }}</p>
                    <small>{{ __('Total sent') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-warning">
                    <h3>{{ number_format($stats['pending_transfers']) }}</h3>
                    <p class="mb-0">{{ __('Pending') }}</p>
                    <small>{{ number_format($stats['completed_transfers']) }} {{ __('completed') }}</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card bg-gradient-info">
                    <h3>${{ number_format($stats['available_limit'], 2) }}</h3>
                    <p class="mb-0">{{ __('Available Limit') }}</p>
                    <small>{{ __('Daily limit remaining') }}</small>
                </div>
            </div>
        @endif
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>
                        {{ __('Quick Actions') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @foreach($quickActions as $action)
                            <div class="col-6">
                                <a href="{{ $action['url'] }}" class="text-decoration-none">
                                    <div class="card border-0 bg-{{ $action['color'] }} bg-opacity-10 h-100">
                                        <div class="card-body text-center p-3">
                                            <i class="{{ $action['icon'] }} text-{{ $action['color'] }} fs-2 mb-2"></i>
                                            <h6 class="card-title text-{{ $action['color'] }} mb-1">{{ $action['title'] }}</h6>
                                            <small class="text-muted">{{ $action['description'] }}</small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        {{ __('Transfer Activity') }}
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="chartPeriod" id="7days" value="7days" checked>
                        <label class="btn btn-outline-primary" for="7days">{{ __('7 Days') }}</label>
                        
                        <input type="radio" class="btn-check" name="chartPeriod" id="30days" value="30days">
                        <label class="btn btn-outline-primary" for="30days">{{ __('30 Days') }}</label>
                        
                        <input type="radio" class="btn-check" name="chartPeriod" id="12months" value="12months">
                        <label class="btn btn-outline-primary" for="12months">{{ __('12 Months') }}</label>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transfers -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        {{ __('Recent Transfers') }}
                    </h5>
                    @if(Auth::user()->isCustomer())
                        <a href="{{ route('transfers.my') }}" class="btn btn-sm btn-outline-primary">
                            {{ __('View All') }}
                        </a>
                    @elseif(Auth::user()->isAgent())
                        <a href="{{ route('agent.transfers') }}" class="btn btn-sm btn-outline-primary">
                            {{ __('View All') }}
                        </a>
                    @else
                        <a href="{{ route('admin.transfers') }}" class="btn btn-sm btn-outline-primary">
                            {{ __('View All') }}
                        </a>
                    @endif
                </div>
                <div class="card-body p-0">
                    @if($recentTransfers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>{{ __('Transfer Code') }}</th>
                                        <th>{{ __('From/To') }}</th>
                                        <th>{{ __('Amount') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Date') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentTransfers as $transfer)
                                        <tr>
                                            <td>
                                                <strong>{{ $transfer->transfer_code }}</strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2">{{ $transfer->senderCountry->name }}</span>
                                                    <i class="bi bi-arrow-right text-muted"></i>
                                                    <span class="ms-2">{{ $transfer->receiverCountry->name }}</span>
                                                </div>
                                                @if(Auth::user()->isAdmin() || Auth::user()->isAgent())
                                                    <small class="text-muted d-block">
                                                        {{ $transfer->sender->name }} → {{ $transfer->receiver_name }}
                                                    </small>
                                                @endif
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $transfer->senderCurrency->symbol }}{{ number_format($transfer->amount, 2) }}</strong>
                                                </div>
                                                <small class="text-muted">
                                                    {{ $transfer->receiverCurrency->symbol }}{{ number_format($transfer->converted_amount, 2) }}
                                                </small>
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ $transfer->status }}">
                                                    {{ ucfirst(str_replace('_', ' ', $transfer->status)) }}
                                                </span>
                                            </td>
                                            <td>
                                                <div>{{ $transfer->created_at->format('M d, Y') }}</div>
                                                <small class="text-muted">{{ $transfer->created_at->format('H:i') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    @if(Auth::user()->isAdmin())
                                                        <a href="{{ route('admin.transfers.show', $transfer) }}" class="btn btn-outline-primary">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    @elseif(Auth::user()->isAgent())
                                                        <a href="{{ route('agent.transfers.show', $transfer) }}" class="btn btn-outline-primary">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    @else
                                                        <a href="{{ route('transfers.show', $transfer) }}" class="btn btn-outline-primary">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    @endif
                                                    
                                                    @if($transfer->canBeCancelled() && (Auth::user()->id === $transfer->sender_id || Auth::user()->isAdmin()))
                                                        <button class="btn btn-outline-danger" onclick="cancelTransfer('{{ $transfer->id }}')">
                                                            <i class="bi bi-x"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-inbox fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">{{ __('No transfers found') }}</h5>
                            <p class="text-muted">{{ __('Your recent transfers will appear here') }}</p>
                            @if(Auth::user()->isCustomer())
                                <a href="{{ route('transfers.create') }}" class="btn btn-primary">
                                    <i class="bi bi-send me-2"></i>
                                    {{ __('Send Your First Transfer') }}
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let activityChart;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    setupChartPeriodChange();
});

function initializeChart() {
    const ctx = document.getElementById('activityChart').getContext('2d');
    
    activityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '{{ __("Transfers") }}',
                data: [],
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    
    loadChartData('7days');
}

function setupChartPeriodChange() {
    document.querySelectorAll('input[name="chartPeriod"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                loadChartData(this.value);
            }
        });
    });
}

function loadChartData(period) {
    axios.get('/dashboard/chart-data', { params: { period } })
        .then(response => {
            if (response.data.success) {
                const data = response.data.data;
                
                activityChart.data.labels = data.map(item => {
                    const date = new Date(item.date);
                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                });
                
                activityChart.data.datasets[0].data = data.map(item => item.count);
                activityChart.update();
            }
        })
        .catch(error => {
            console.error('Error loading chart data:', error);
        });
}

function refreshDashboard() {
    location.reload();
}

function cancelTransfer(transferId) {
    if (confirm('{{ __("Are you sure you want to cancel this transfer?") }}')) {
        axios.post(`/transfers/${transferId}/cancel`)
            .then(response => {
                if (response.data.success) {
                    showNotification(response.data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(response.data.message, 'error');
                }
            })
            .catch(error => {
                const message = error.response?.data?.message || 'Failed to cancel transfer';
                showNotification(message, 'error');
            });
    }
}

// Auto-refresh dashboard every 5 minutes
setInterval(() => {
    loadChartData(document.querySelector('input[name="chartPeriod"]:checked').value);
}, 300000);
</script>
@endpush
