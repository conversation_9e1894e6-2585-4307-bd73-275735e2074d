<?php

echo "🔍 Database Structure Check - Elite Transfer System\n\n";

try {
    // Connect to database
    $db = new PDO('sqlite:database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful\n\n";
    
    // Get transfers table structure
    echo "📋 Transfers table structure:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $result = $db->query("PRAGMA table_info(transfers)");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($columns)) {
        echo "❌ Table 'transfers' does not exist!\n";
    } else {
        foreach ($columns as $column) {
            echo sprintf("%-20s %-15s %-10s %-10s\n", 
                $column['name'], 
                $column['type'], 
                $column['notnull'] ? 'NOT NULL' : 'NULL', 
                $column['pk'] ? 'PRIMARY' : ''
            );
        }
    }
    
    echo "\n";
    
    // Check if updated_at column exists
    $hasUpdatedAt = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'updated_at') {
            $hasUpdatedAt = true;
            break;
        }
    }
    
    if ($hasUpdatedAt) {
        echo "✅ Column 'updated_at' exists\n";
    } else {
        echo "❌ Column 'updated_at' is missing!\n";
        echo "🔧 Need to add this column to the table\n";
    }
    
    echo "\n";
    
    // Show sample data
    echo "📊 Sample transfers data:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $transfers = $db->query("SELECT * FROM transfers LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($transfers)) {
        echo "ℹ️ No transfers found in database\n";
    } else {
        foreach ($transfers as $i => $transfer) {
            echo "Transfer " . ($i + 1) . ":\n";
            foreach ($transfer as $key => $value) {
                echo "  $key: $value\n";
            }
            echo "\n";
        }
    }
    
    // Check other tables
    echo "📋 All tables in database:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
        echo sprintf("%-20s %d records\n", $table, $count);
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

?>
