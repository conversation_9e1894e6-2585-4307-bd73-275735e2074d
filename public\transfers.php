<?php
// Load session helper
require_once __DIR__ . '/includes/session_helper.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Require login
    if (!is_logged_in()) {
        throw new Exception('Authentication required', 401);
    }
    
    $userData = get_user_data();
    
    // Connect to database
    $db = new PDO('sqlite:' . __DIR__ . '/../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Build query based on user role
    if ($userData['role'] === 'admin') {
        // Admin can see all transfers
        $transfersQuery = "
            SELECT 
                t.*,
                sc.name as sender_country_name,
                sc.code as sender_country_code,
                sc.currency as sender_country_currency,
                rc.name as receiver_country_name,
                rc.code as receiver_country_code,
                rc.currency as receiver_country_currency,
                u.name as sender_user_name,
                u.email as sender_user_email
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            LEFT JOIN users u ON t.sender_id = u.id
            ORDER BY t.created_at DESC 
            LIMIT 100
        ";
        $stmt = $db->prepare($transfersQuery);
        $stmt->execute();
    } else {
        // Regular users can only see their own transfers
        $transfersQuery = "
            SELECT 
                t.*,
                sc.name as sender_country_name,
                sc.code as sender_country_code,
                sc.currency as sender_country_currency,
                rc.name as receiver_country_name,
                rc.code as receiver_country_code,
                rc.currency as receiver_country_currency
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE t.sender_id = ?
            ORDER BY t.created_at DESC 
            LIMIT 50
        ";
        $stmt = $db->prepare($transfersQuery);
        $stmt->execute([$userData['id']]);
    }
    
    $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format transfer data
    $formattedTransfers = [];
    foreach ($transfers as $transfer) {
        $formattedTransfer = [
            'id' => (int)$transfer['id'],
            'transfer_code' => $transfer['transfer_code'],
            'pickup_code' => $transfer['pickup_code'],
            'sender' => [
                'id' => (int)$transfer['sender_id'],
                'name' => $transfer['sender_name'],
                'phone' => $transfer['sender_phone'],
                'country' => [
                    'id' => (int)$transfer['sender_country_id'],
                    'name' => $transfer['sender_country_name'],
                    'code' => $transfer['sender_country_code'],
                    'currency' => $transfer['sender_country_currency']
                ]
            ],
            'receiver' => [
                'name' => $transfer['receiver_name'],
                'phone' => $transfer['receiver_phone'],
                'country' => [
                    'id' => (int)$transfer['receiver_country_id'],
                    'name' => $transfer['receiver_country_name'],
                    'code' => $transfer['receiver_country_code'],
                    'currency' => $transfer['receiver_country_currency']
                ]
            ],
            'financial' => [
                'amount' => (float)$transfer['amount'],
                'converted_amount' => (float)$transfer['converted_amount'],
                'exchange_rate' => (float)$transfer['exchange_rate'],
                'fee_amount' => (float)$transfer['fee_amount'],
                'total_amount' => (float)$transfer['total_amount'],
                'sender_currency' => $transfer['sender_currency'],
                'receiver_currency' => $transfer['receiver_currency']
            ],
            'status' => $transfer['status'],
            'payment_method' => $transfer['payment_method'],
            'pickup_method' => $transfer['pickup_method'],
            'agent_id' => $transfer['agent_id'] ? (int)$transfer['agent_id'] : null,
            'notes' => $transfer['notes'],
            'created_at' => $transfer['created_at'],
            'updated_at' => $transfer['updated_at']
        ];
        
        // Add sender user info for admin
        if ($userData['role'] === 'admin' && isset($transfer['sender_user_name'])) {
            $formattedTransfer['sender']['user_name'] = $transfer['sender_user_name'];
            $formattedTransfer['sender']['user_email'] = $transfer['sender_user_email'];
        }
        
        $formattedTransfers[] = $formattedTransfer;
    }
    
    // Get transfer statistics
    if ($userData['role'] === 'admin') {
        // Admin gets global statistics
        $stats = [
            'total_transfers' => (int)$db->query("SELECT COUNT(*) FROM transfers")->fetchColumn(),
            'by_status' => [],
            'financial_summary' => [
                'total_amount' => (float)$db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn(),
                'total_fees' => (float)$db->query("SELECT COALESCE(SUM(fee_amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn(),
                'average_amount' => (float)$db->query("SELECT COALESCE(AVG(amount), 0) FROM transfers WHERE status = 'completed'")->fetchColumn()
            ],
            'time_periods' => [
                'today' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = DATE('now')")->fetchColumn(),
                'this_week' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) >= DATE('now', '-7 days')")->fetchColumn(),
                'this_month' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) >= DATE('now', 'start of month')")->fetchColumn()
            ]
        ];
        
        // Get status breakdown
        $statusStats = $db->query("
            SELECT status, COUNT(*) as count, SUM(amount) as total_amount
            FROM transfers 
            GROUP BY status
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = [
                'count' => (int)$stat['count'],
                'total_amount' => (float)$stat['total_amount']
            ];
        }
        
    } else {
        // Regular user gets personal statistics
        $stats = [
            'total_transfers' => (int)$db->query("SELECT COUNT(*) FROM transfers WHERE sender_id = ?", [$userData['id']])->fetchColumn(),
            'by_status' => [],
            'financial_summary' => [
                'total_sent' => (float)$db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE sender_id = ? AND status = 'completed'", [$userData['id']])->fetchColumn(),
                'total_fees_paid' => (float)$db->query("SELECT COALESCE(SUM(fee_amount), 0) FROM transfers WHERE sender_id = ? AND status = 'completed'", [$userData['id']])->fetchColumn(),
                'average_transfer' => (float)$db->query("SELECT COALESCE(AVG(amount), 0) FROM transfers WHERE sender_id = ? AND status = 'completed'", [$userData['id']])->fetchColumn()
            ]
        ];
        
        // Get personal status breakdown
        $stmt = $db->prepare("
            SELECT status, COUNT(*) as count, SUM(amount) as total_amount
            FROM transfers 
            WHERE sender_id = ?
            GROUP BY status
        ");
        $stmt->execute([$userData['id']]);
        $statusStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = [
                'count' => (int)$stat['count'],
                'total_amount' => (float)$stat['total_amount']
            ];
        }
    }
    
    $response = [
        'success' => true,
        'data' => [
            'transfers' => $formattedTransfers,
            'statistics' => $stats
        ],
        'count' => count($formattedTransfers),
        'user_role' => $userData['role'],
        'timestamp' => date('Y-m-d H:i:s'),
        'generated_by' => $userData['name']
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $statusCode = $e->getCode() ?: 500;
    $response = [
        'success' => false,
        'error' => [
            'message' => $e->getMessage(),
            'code' => $statusCode,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    http_response_code($statusCode);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
