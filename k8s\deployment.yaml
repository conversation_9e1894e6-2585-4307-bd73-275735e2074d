apiVersion: v1
kind: Namespace
metadata:
  name: elite-transfer
  labels:
    name: elite-transfer
    environment: production

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: elite-transfer-config
  namespace: elite-transfer
data:
  APP_ENV: "production"
  APP_DEBUG: "false"
  DB_HOST: "mysql-service"
  DB_DATABASE: "elite_transfer"
  REDIS_HOST: "redis-service"
  CACHE_DRIVER: "redis"
  SESSION_DRIVER: "redis"

---
apiVersion: v1
kind: Secret
metadata:
  name: elite-transfer-secrets
  namespace: elite-transfer
type: Opaque
data:
  DB_USERNAME: ZWxpdGVfdXNlcg== # elite_user (base64)
  DB_PASSWORD: ZWxpdGVfcGFzc3dvcmRfMjAyNQ== # elite_password_2025 (base64)
  JWT_SECRET: ZWxpdGVfand0X3NlY3JldF9rZXlfMjAyNQ== # elite_jwt_secret_key_2025 (base64)
  STRIPE_SECRET_KEY: c2tfbGl2ZV95b3VyX3N0cmlwZV9zZWNyZXRfa2V5 # sk_live_your_stripe_secret_key (base64)
  SENDGRID_API_KEY: eW91cl9zZW5kZ3JpZF9hcGlfa2V5 # your_sendgrid_api_key (base64)

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elite-transfer-app
  namespace: elite-transfer
  labels:
    app: elite-transfer
    tier: application
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: elite-transfer
      tier: application
  template:
    metadata:
      labels:
        app: elite-transfer
        tier: application
    spec:
      containers:
      - name: elite-transfer
        image: elite-transfer:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        env:
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: elite-transfer-config
              key: APP_ENV
        - name: APP_DEBUG
          valueFrom:
            configMapKeyRef:
              name: elite-transfer-config
              key: APP_DEBUG
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: elite-transfer-config
              key: DB_HOST
        - name: DB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: elite-transfer-config
              key: DB_DATABASE
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: elite-transfer-secrets
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elite-transfer-secrets
              key: DB_PASSWORD
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: elite-transfer-config
              key: REDIS_HOST
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: elite-transfer-secrets
              key: JWT_SECRET
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: elite-transfer-secrets
              key: STRIPE_SECRET_KEY
        - name: SENDGRID_API_KEY
          valueFrom:
            secretKeyRef:
              name: elite-transfer-secrets
              key: SENDGRID_API_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: storage-volume
          mountPath: /var/www/html/storage
        - name: logs-volume
          mountPath: /var/log/app
      volumes:
      - name: storage-volume
        persistentVolumeClaim:
          claimName: elite-transfer-storage-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: elite-transfer-logs-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: elite-transfer-service
  namespace: elite-transfer
  labels:
    app: elite-transfer
    tier: application
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: elite-transfer
    tier: application

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: elite-transfer
  labels:
    app: mysql
    tier: database
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: mysql
      tier: database
  template:
    metadata:
      labels:
        app: mysql
        tier: database
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "elite_root_password_2025"
        - name: MYSQL_DATABASE
          value: "elite_transfer"
        - name: MYSQL_USER
          value: "elite_user"
        - name: MYSQL_PASSWORD
          value: "elite_password_2025"
        ports:
        - containerPort: 3306
          name: mysql
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 1
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: elite-transfer
  labels:
    app: mysql
    tier: database
spec:
  type: ClusterIP
  ports:
  - port: 3306
    targetPort: 3306
    protocol: TCP
    name: mysql
  selector:
    app: mysql
    tier: database

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: elite-transfer
  labels:
    app: redis
    tier: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
      tier: cache
  template:
    metadata:
      labels:
        app: redis
        tier: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: elite-transfer
  labels:
    app: redis
    tier: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
    tier: cache

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pvc
  namespace: elite-transfer
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: elite-transfer
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elite-transfer-storage-pvc
  namespace: elite-transfer
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: shared-storage

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elite-transfer-logs-pvc
  namespace: elite-transfer
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
  storageClassName: shared-storage
