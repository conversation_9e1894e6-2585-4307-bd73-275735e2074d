<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مستخدم';
$userRole = $_SESSION['user_role'] ?? 'customer';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء تحويل جديد - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e5e7eb;
            padding: 12px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 38, 38, 0.3);
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .step.active {
            background: #dc2626;
            color: white;
        }
        
        .step.completed {
            background: #10b981;
            color: white;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #e5e7eb;
            margin-top: 19px;
        }
        
        .step-line.completed {
            background: #10b981;
        }
        
        .currency-display {
            background: #f3f4f6;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }
        
        .exchange-rate {
            font-size: 1.2rem;
            font-weight: 600;
            color: #dc2626;
        }
        
        .fee-breakdown {
            background: #fef3c7;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .form-step {
            display: none;
        }
        
        .form-step.active {
            display: block;
        }
        
        .country-flag {
            width: 24px;
            height: 16px;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank me-2"></i>
                Elite Transfer
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/track-transfer">
                    <i class="bi bi-search me-1"></i>
                    تتبع التحويل
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/profile">
                            <i class="bi bi-person me-2"></i>الملف الشخصي
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-transparent border-0 text-center py-4">
                        <h2 class="mb-1">
                            <i class="bi bi-send me-2 text-danger"></i>
                            إنشاء تحويل جديد
                        </h2>
                        <p class="text-muted mb-0">أرسل الأموال بسرعة وأمان إلى جميع أنحاء العالم</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step active" id="step1">1</div>
                            <div class="step-line" id="line1"></div>
                            <div class="step" id="step2">2</div>
                            <div class="step-line" id="line2"></div>
                            <div class="step" id="step3">3</div>
                            <div class="step-line" id="line3"></div>
                            <div class="step" id="step4">4</div>
                        </div>

                        <!-- Transfer Form -->
                        <form id="transferForm" onsubmit="return false;">
                            <!-- Step 1: Transfer Details -->
                            <div class="form-step active" id="formStep1">
                                <h4 class="mb-4">
                                    <i class="bi bi-info-circle me-2"></i>
                                    تفاصيل التحويل
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="senderCountry" class="form-label">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            البلد المرسل
                                        </label>
                                        <select class="form-select" id="senderCountry" required>
                                            <option value="">اختر البلد</option>
                                            <option value="1" data-currency="SAR">🇸🇦 السعودية (ريال سعودي)</option>
                                            <option value="2" data-currency="AED">🇦🇪 الإمارات (درهم إماراتي)</option>
                                            <option value="3" data-currency="USD">🇺🇸 أمريكا (دولار أمريكي)</option>
                                            <option value="4" data-currency="EUR">🇪🇺 أوروبا (يورو)</option>
                                            <option value="5" data-currency="GBP">🇬🇧 بريطانيا (جنيه إسترليني)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="receiverCountry" class="form-label">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            البلد المستقبل
                                        </label>
                                        <select class="form-select" id="receiverCountry" required>
                                            <option value="">اختر البلد</option>
                                            <option value="6" data-currency="EGP">🇪🇬 مصر (جنيه مصري)</option>
                                            <option value="7" data-currency="JOD">🇯🇴 الأردن (دينار أردني)</option>
                                            <option value="8" data-currency="KWD">🇰🇼 الكويت (دينار كويتي)</option>
                                            <option value="9" data-currency="QAR">🇶🇦 قطر (ريال قطري)</option>
                                            <option value="10" data-currency="BHD">🇧🇭 البحرين (دينار بحريني)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="amount" class="form-label">
                                            <i class="bi bi-currency-dollar me-1"></i>
                                            المبلغ المرسل
                                        </label>
                                        <input type="number" class="form-control" id="amount" 
                                               placeholder="أدخل المبلغ" min="1" max="50000" step="0.01" required>
                                        <div class="form-text">الحد الأدنى: 1 | الحد الأقصى: 50,000</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="purpose" class="form-label">
                                            <i class="bi bi-tag me-1"></i>
                                            الغرض من التحويل
                                        </label>
                                        <select class="form-select" id="purpose" required>
                                            <option value="">اختر الغرض</option>
                                            <option value="family_support">دعم الأسرة</option>
                                            <option value="education">تعليم</option>
                                            <option value="medical">علاج طبي</option>
                                            <option value="business">أعمال تجارية</option>
                                            <option value="investment">استثمار</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Exchange Rate Display -->
                                <div class="currency-display" id="exchangeDisplay" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-4 text-center">
                                            <div class="exchange-rate" id="sendAmount">0.00</div>
                                            <small class="text-muted" id="sendCurrency">SAR</small>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <i class="bi bi-arrow-right fs-2 text-danger"></i>
                                            <div class="exchange-rate" id="exchangeRate">0.0000</div>
                                            <small class="text-muted">سعر الصرف</small>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="exchange-rate" id="receiveAmount">0.00</div>
                                            <small class="text-muted" id="receiveCurrency">EGP</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Fee Breakdown -->
                                <div class="fee-breakdown" id="feeBreakdown" style="display: none;">
                                    <h6><i class="bi bi-calculator me-2"></i>تفاصيل الرسوم</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small>المبلغ الأساسي:</small>
                                            <div class="fw-bold" id="baseAmount">0.00</div>
                                        </div>
                                        <div class="col-6">
                                            <small>رسوم التحويل:</small>
                                            <div class="fw-bold" id="transferFee">0.00</div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-12 text-center">
                                            <strong>إجمالي المبلغ المطلوب: <span id="totalAmount" class="text-danger">0.00</span></strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary btn-lg" onclick="nextStep(2)">
                                        التالي - معلومات المستقبل
                                        <i class="bi bi-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 2: Receiver Information -->
                            <div class="form-step" id="formStep2">
                                <h4 class="mb-4">
                                    <i class="bi bi-person me-2"></i>
                                    معلومات المستقبل
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="receiverName" class="form-label">
                                            <i class="bi bi-person me-1"></i>
                                            اسم المستقبل الكامل
                                        </label>
                                        <input type="text" class="form-control" id="receiverName" 
                                               placeholder="أدخل الاسم الكامل" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="receiverPhone" class="form-label">
                                            <i class="bi bi-telephone me-1"></i>
                                            رقم الهاتف
                                        </label>
                                        <input type="tel" class="form-control" id="receiverPhone" 
                                               placeholder="+20xxxxxxxxx" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="receiverAddress" class="form-label">
                                        <i class="bi bi-geo-alt me-1"></i>
                                        العنوان الكامل
                                    </label>
                                    <textarea class="form-control" id="receiverAddress" rows="3" 
                                              placeholder="أدخل العنوان الكامل" required></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="pickupMethod" class="form-label">
                                            <i class="bi bi-credit-card me-1"></i>
                                            طريقة الاستلام
                                        </label>
                                        <select class="form-select" id="pickupMethod" required>
                                            <option value="">اختر طريقة الاستلام</option>
                                            <option value="cash">استلام نقدي</option>
                                            <option value="bank_deposit">إيداع بنكي</option>
                                            <option value="mobile_wallet">محفظة إلكترونية</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3" id="bankDetailsDiv" style="display: none;">
                                        <label for="bankAccount" class="form-label">
                                            <i class="bi bi-bank me-1"></i>
                                            رقم الحساب البنكي
                                        </label>
                                        <input type="text" class="form-control" id="bankAccount" 
                                               placeholder="أدخل رقم الحساب">
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                        <i class="bi bi-arrow-right me-2"></i>
                                        السابق
                                    </button>
                                    <button type="button" class="btn btn-primary flex-fill" onclick="nextStep(3)">
                                        التالي - طريقة الدفع
                                        <i class="bi bi-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 3: Payment Method -->
                            <div class="form-step" id="formStep3">
                                <h4 class="mb-4">
                                    <i class="bi bi-credit-card me-2"></i>
                                    طريقة الدفع
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 payment-option" data-method="card">
                                            <div class="card-body text-center">
                                                <i class="bi bi-credit-card fs-1 text-primary mb-3"></i>
                                                <h5>بطاقة ائتمانية</h5>
                                                <p class="text-muted">دفع فوري وآمن</p>
                                                <small class="text-success">رسوم إضافية: 2.9%</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 payment-option" data-method="bank">
                                            <div class="card-body text-center">
                                                <i class="bi bi-bank fs-1 text-success mb-3"></i>
                                                <h5>تحويل بنكي</h5>
                                                <p class="text-muted">تحويل مباشر من البنك</p>
                                                <small class="text-success">بدون رسوم إضافية</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <input type="hidden" id="selectedPaymentMethod" required>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                        <i class="bi bi-arrow-right me-2"></i>
                                        السابق
                                    </button>
                                    <button type="button" class="btn btn-primary flex-fill" onclick="nextStep(4)">
                                        التالي - مراجعة وتأكيد
                                        <i class="bi bi-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 4: Review and Confirm -->
                            <div class="form-step" id="formStep4">
                                <h4 class="mb-4">
                                    <i class="bi bi-check-circle me-2"></i>
                                    مراجعة وتأكيد التحويل
                                </h4>
                                
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div id="reviewSummary">
                                            <!-- Summary will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-check mt-3">
                                    <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                    <label class="form-check-label" for="agreeTerms">
                                        أوافق على <a href="#" class="text-decoration-none">الشروط والأحكام</a> 
                                        و <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                                    </label>
                                </div>
                                
                                <div class="d-flex gap-2 mt-4">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(3)">
                                        <i class="bi bi-arrow-right me-2"></i>
                                        السابق
                                    </button>
                                    <button type="button" class="btn btn-success flex-fill btn-lg" onclick="submitTransfer()">
                                        <i class="bi bi-send me-2"></i>
                                        تأكيد وإرسال التحويل
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStep = 1;
        let exchangeRates = {
            'SAR_EGP': 8.24,
            'SAR_JOD': 0.189,
            'SAR_KWD': 0.08,
            'SAR_QAR': 0.97,
            'SAR_BHD': 0.10,
            'AED_EGP': 8.42,
            'AED_JOD': 0.193,
            'AED_KWD': 0.082,
            'AED_QAR': 0.99,
            'AED_BHD': 0.103,
            'USD_EGP': 30.90,
            'USD_JOD': 0.709,
            'USD_KWD': 0.30,
            'USD_QAR': 3.64,
            'USD_BHD': 0.376
        };
        
        // Initialize form
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners
            document.getElementById('amount').addEventListener('input', calculateExchange);
            document.getElementById('senderCountry').addEventListener('change', calculateExchange);
            document.getElementById('receiverCountry').addEventListener('change', calculateExchange);
            document.getElementById('pickupMethod').addEventListener('change', toggleBankDetails);
            
            // Payment method selection
            document.querySelectorAll('.payment-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('border-primary'));
                    this.classList.add('border-primary');
                    document.getElementById('selectedPaymentMethod').value = this.dataset.method;
                });
            });
        });
        
        function nextStep(step) {
            if (validateCurrentStep()) {
                // Hide current step
                document.getElementById(`formStep${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');
                
                if (currentStep < 4) {
                    document.getElementById(`line${currentStep}`).classList.add('completed');
                }
                
                // Show next step
                currentStep = step;
                document.getElementById(`formStep${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                
                if (step === 4) {
                    generateReviewSummary();
                }
            }
        }
        
        function prevStep(step) {
            // Hide current step
            document.getElementById(`formStep${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep}`).classList.remove('active');
            
            // Show previous step
            currentStep = step;
            document.getElementById(`formStep${currentStep}`).classList.add('active');
            document.getElementById(`step${currentStep}`).classList.remove('completed');
            document.getElementById(`step${currentStep}`).classList.add('active');
            
            if (step < 4) {
                document.getElementById(`line${step}`).classList.remove('completed');
            }
        }
        
        function validateCurrentStep() {
            const currentForm = document.getElementById(`formStep${currentStep}`);
            const requiredFields = currentForm.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }
            }
            
            return true;
        }
        
        function calculateExchange() {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const senderCountry = document.getElementById('senderCountry');
            const receiverCountry = document.getElementById('receiverCountry');
            
            if (amount > 0 && senderCountry.value && receiverCountry.value) {
                const senderCurrency = senderCountry.options[senderCountry.selectedIndex].dataset.currency;
                const receiverCurrency = receiverCountry.options[receiverCountry.selectedIndex].dataset.currency;
                
                const rateKey = `${senderCurrency}_${receiverCurrency}`;
                const rate = exchangeRates[rateKey] || 1;
                
                const convertedAmount = amount * rate;
                const fee = (amount * 0.025) + 5; // 2.5% + $5 fixed
                const totalAmount = amount + fee;
                
                // Update display
                document.getElementById('sendAmount').textContent = amount.toFixed(2);
                document.getElementById('sendCurrency').textContent = senderCurrency;
                document.getElementById('receiveAmount').textContent = convertedAmount.toFixed(2);
                document.getElementById('receiveCurrency').textContent = receiverCurrency;
                document.getElementById('exchangeRate').textContent = rate.toFixed(4);
                
                document.getElementById('baseAmount').textContent = amount.toFixed(2) + ' ' + senderCurrency;
                document.getElementById('transferFee').textContent = fee.toFixed(2) + ' ' + senderCurrency;
                document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' ' + senderCurrency;
                
                document.getElementById('exchangeDisplay').style.display = 'block';
                document.getElementById('feeBreakdown').style.display = 'block';
            }
        }
        
        function toggleBankDetails() {
            const pickupMethod = document.getElementById('pickupMethod').value;
            const bankDetailsDiv = document.getElementById('bankDetailsDiv');
            
            if (pickupMethod === 'bank_deposit') {
                bankDetailsDiv.style.display = 'block';
                document.getElementById('bankAccount').required = true;
            } else {
                bankDetailsDiv.style.display = 'none';
                document.getElementById('bankAccount').required = false;
            }
        }
        
        function generateReviewSummary() {
            const senderCountry = document.getElementById('senderCountry');
            const receiverCountry = document.getElementById('receiverCountry');
            const amount = document.getElementById('amount').value;
            const receiverName = document.getElementById('receiverName').value;
            const receiverPhone = document.getElementById('receiverPhone').value;
            const pickupMethod = document.getElementById('pickupMethod');
            const paymentMethod = document.getElementById('selectedPaymentMethod').value;
            
            const senderCurrency = senderCountry.options[senderCountry.selectedIndex].dataset.currency;
            const receiverCurrency = receiverCountry.options[receiverCountry.selectedIndex].dataset.currency;
            
            const summary = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>تفاصيل التحويل:</h6>
                        <p><strong>من:</strong> ${senderCountry.options[senderCountry.selectedIndex].text}</p>
                        <p><strong>إلى:</strong> ${receiverCountry.options[receiverCountry.selectedIndex].text}</p>
                        <p><strong>المبلغ:</strong> ${amount} ${senderCurrency}</p>
                        <p><strong>طريقة الدفع:</strong> ${paymentMethod === 'card' ? 'بطاقة ائتمانية' : 'تحويل بنكي'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات المستقبل:</h6>
                        <p><strong>الاسم:</strong> ${receiverName}</p>
                        <p><strong>الهاتف:</strong> ${receiverPhone}</p>
                        <p><strong>طريقة الاستلام:</strong> ${pickupMethod.options[pickupMethod.selectedIndex].text}</p>
                    </div>
                </div>
            `;
            
            document.getElementById('reviewSummary').innerHTML = summary;
        }
        
        function submitTransfer() {
            if (!document.getElementById('agreeTerms').checked) {
                alert('يرجى الموافقة على الشروط والأحكام');
                return;
            }
            
            // Collect form data
            const formData = {
                sender_country_id: document.getElementById('senderCountry').value,
                receiver_country_id: document.getElementById('receiverCountry').value,
                amount: document.getElementById('amount').value,
                purpose: document.getElementById('purpose').value,
                receiver_name: document.getElementById('receiverName').value,
                receiver_phone: document.getElementById('receiverPhone').value,
                receiver_address: document.getElementById('receiverAddress').value,
                pickup_method: document.getElementById('pickupMethod').value,
                payment_method: document.getElementById('selectedPaymentMethod').value,
                bank_account: document.getElementById('bankAccount').value
            };
            
            // Submit to server
            fetch('/api/create-transfer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إنشاء التحويل بنجاح!\nرمز التحويل: ' + data.transfer_code);
                    window.location.href = '/track-transfer?code=' + data.transfer_code;
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
