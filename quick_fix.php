<?php

echo "🔧 Elite Transfer System - Quick Database Fix\n\n";

// Create database directory
$dbDir = __DIR__ . '/database';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

// Remove any corrupted files
$files = glob($dbDir . '/*.db');
foreach ($files as $file) {
    if (file_exists($file)) {
        unlink($file);
    }
}

echo "✅ Cleaned up old database files\n";

// Create new SQLite database
$dbPath = $dbDir . '/elite_transfer.db';

try {
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Created new SQLite database\n";
    
    // Create essential tables
    $tables = [
        "CREATE TABLE IF NOT EXISTS countries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT NOT NULL UNIQUE,
            currency TEXT NOT NULL,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            phone TEXT,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'customer',
            kyc_status TEXT DEFAULT 'pending',
            kyc_level TEXT DEFAULT 'none',
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_code TEXT UNIQUE NOT NULL,
            pickup_code TEXT NOT NULL,
            sender_id INTEGER NOT NULL,
            sender_name TEXT NOT NULL,
            sender_phone TEXT NOT NULL,
            sender_country_id INTEGER NOT NULL,
            receiver_name TEXT NOT NULL,
            receiver_phone TEXT NOT NULL,
            receiver_country_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            converted_amount DECIMAL(15,2) NOT NULL,
            exchange_rate DECIMAL(10,6) NOT NULL,
            fee_amount DECIMAL(15,2) NOT NULL,
            total_amount DECIMAL(15,2) NOT NULL,
            sender_currency TEXT NOT NULL,
            receiver_currency TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            payment_method TEXT DEFAULT 'cash',
            pickup_method TEXT DEFAULT 'cash',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            currency TEXT DEFAULT 'USD',
            payment_method TEXT NOT NULL,
            payment_provider TEXT,
            transaction_id TEXT,
            status TEXT DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS exchange_rates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            from_currency TEXT NOT NULL,
            to_currency TEXT NOT NULL,
            rate DECIMAL(15,6) NOT NULL,
            provider TEXT DEFAULT 'fallback',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            transfer_id INTEGER NULL,
            type TEXT NOT NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS external_api_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            service_name TEXT NOT NULL,
            provider TEXT NOT NULL,
            endpoint TEXT,
            request_data TEXT,
            response_data TEXT,
            status_code INTEGER,
            success INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )"
    ];
    
    foreach ($tables as $sql) {
        $db->exec($sql);
    }
    
    echo "✅ Created 8 database tables\n";
    
    // Insert initial data
    $countries = [
        ['Saudi Arabia', 'SA', 'SAR'],
        ['United Arab Emirates', 'AE', 'AED'],
        ['Egypt', 'EG', 'EGP'],
        ['Kuwait', 'KW', 'KWD'],
        ['Jordan', 'JO', 'JOD'],
        ['United States', 'US', 'USD']
    ];
    
    $stmt = $db->prepare("INSERT OR IGNORE INTO countries (name, code, currency) VALUES (?, ?, ?)");
    foreach ($countries as $country) {
        $stmt->execute($country);
    }
    
    echo "✅ Inserted " . count($countries) . " countries\n";
    
    // Create admin user
    $adminPassword = password_hash('password', PASSWORD_DEFAULT);
    $stmt = $db->prepare("INSERT OR IGNORE INTO users (name, email, password, role, kyc_status, kyc_level) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['System Administrator', '<EMAIL>', $adminPassword, 'admin', 'verified', 'full']);
    
    echo "✅ Created admin user: <EMAIL> / password\n";
    
    // Create demo customer
    $customerPassword = password_hash('customer123', PASSWORD_DEFAULT);
    $stmt->execute(['Demo Customer', '<EMAIL>', $customerPassword, 'customer', 'verified', 'basic']);
    
    echo "✅ Created demo customer: <EMAIL> / customer123\n";
    
    // Insert system settings
    $settings = [
        ['app_name', 'Elite Transfer System', 'Application name'],
        ['max_transfer_amount', '50000', 'Maximum transfer amount'],
        ['min_transfer_amount', '1', 'Minimum transfer amount'],
        ['default_fee_percentage', '2.5', 'Default fee percentage'],
        ['default_fee_fixed', '5.00', 'Default fixed fee']
    ];
    
    $stmt = $db->prepare("INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    echo "✅ Inserted " . count($settings) . " system settings\n";
    
    // Insert exchange rates
    $rates = [
        ['USD', 'SAR', 3.7500],
        ['USD', 'AED', 3.6725],
        ['USD', 'EGP', 30.9000],
        ['USD', 'KWD', 0.3000],
        ['USD', 'JOD', 0.7090]
    ];
    
    $stmt = $db->prepare("INSERT OR IGNORE INTO exchange_rates (from_currency, to_currency, rate) VALUES (?, ?, ?)");
    foreach ($rates as $rate) {
        $stmt->execute($rate);
    }
    
    echo "✅ Inserted " . count($rates) . " exchange rates\n";
    
    // Verify
    $countryCount = $db->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    
    echo "\n📊 Database Summary:\n";
    echo "   Countries: {$countryCount}\n";
    echo "   Users: {$userCount}\n";
    echo "   Database: {$dbPath}\n";
    echo "   Size: " . round(filesize($dbPath) / 1024, 2) . " KB\n";
    
    echo "\n🎉 Database fixed successfully!\n";
    echo "🚀 Start the app: php -S localhost:8000 -t public\n";
    echo "🌐 Visit: http://localhost:8000\n";
    echo "👤 Admin: <EMAIL> / password\n";
    echo "👤 Customer: <EMAIL> / customer123\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
