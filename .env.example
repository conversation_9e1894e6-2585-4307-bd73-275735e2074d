# Elite Transfer System - Environment Configuration
# Copy this file to .env and update with your actual credentials

# Application Settings
APP_NAME="Elite Transfer System"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://elitetransfer.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=elite_transfer
DB_USERNAME=elite_user
DB_PASSWORD=elite_password_2025
DB_CHARSET=utf8mb4

# SMS ******** Configuration
# Twilio SMS Service
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# AWS SNS Service
AWS_ACCESS_KEY=your_aws_access_key
AWS_SECRET_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Nexmo/Vonage SMS Service
NEXMO_API_KEY=your_nexmo_api_key
NEXMO_API_SECRET=your_nexmo_api_secret
NEXMO_FROM=EliteTransfer

# Email ******** Configuration
# SendGrid Email Service
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="Elite Transfer System"

# SMTP Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_ENCRYPTION=tls
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME="Elite Transfer System"

# Exchange Rate ******** Configuration
# Fixer.io Service
FIXER_API_KEY=your_fixer_api_key

# ExchangeRate-API Service
EXCHANGERATE_API_KEY=your_exchangerate_api_key

# CurrencyLayer Service
CURRENCYLAYER_API_KEY=your_currencylayer_api_key

# Payment Gateway Configuration
# Stripe
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# Security Configuration
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_32_character_encryption_key

# Cache Configuration
CACHE_DRIVER=file
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Logging Configuration
LOG_CHANNEL=stack
LOG_LEVEL=debug

# External API Configuration
# Bank Integration APIs
BANK_API_ENDPOINT=https://api.bank.com/v1
BANK_API_KEY=your_bank_api_key
BANK_API_SECRET=your_bank_api_secret

# KYC/AML Service
KYC_API_ENDPOINT=https://api.kycprovider.com/v1
KYC_API_KEY=your_kyc_api_key

# Fraud Detection Service
FRAUD_API_ENDPOINT=https://api.frauddetection.com/v1
FRAUD_API_KEY=your_fraud_api_key

# Monitoring and Analytics
# Sentry Error Tracking
SENTRY_DSN=your_sentry_dsn

# Google Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=your_new_relic_key
NEW_RELIC_APP_NAME="Elite Transfer System"

# File Storage Configuration
FILESYSTEM_DRIVER=local
AWS_S3_BUCKET=your_s3_bucket
AWS_S3_REGION=us-east-1

# Backup Configuration
BACKUP_DRIVER=s3
BACKUP_S3_BUCKET=your_backup_bucket

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_ATTEMPTS=60
RATE_LIMIT_DECAY_MINUTES=1

# Feature Flags
FEATURE_2FA_ENABLED=true
FEATURE_FRAUD_DETECTION_ENABLED=true
FEATURE_REAL_TIME_RATES_ENABLED=true
FEATURE_SMS_NOTIFICATIONS_ENABLED=true
FEATURE_EMAIL_NOTIFICATIONS_ENABLED=true

# Business Configuration
DEFAULT_TRANSFER_FEE_PERCENTAGE=2.5
DEFAULT_TRANSFER_FEE_FIXED=5.00
MAX_TRANSFER_AMOUNT=50000
MIN_TRANSFER_AMOUNT=1
DAILY_TRANSFER_LIMIT=100000

# Compliance Configuration
AML_THRESHOLD_AMOUNT=10000
KYC_REQUIRED_AMOUNT=3000
SUSPICIOUS_ACTIVITY_THRESHOLD=5

# Notification Settings
SMS_PROVIDER=twilio
EMAIL_PROVIDER=sendgrid
EXCHANGE_RATE_PROVIDER=fixer

# Development Settings (only for development environment)
APP_DEBUG=true
LOG_LEVEL=debug
MAIL_MAILTRAP_USERNAME=your_mailtrap_username
MAIL_MAILTRAP_PASSWORD=your_mailtrap_password
