version: '3.8'

services:
  # Application Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: elite-transfer-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./storage:/var/www/html/storage
      - ./uploads:/var/www/html/uploads
      - ./logs:/var/log/app
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_DATABASE=elite_transfer
      - DB_USERNAME=elite_user
      - DB_PASSWORD=elite_password_2025
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
    depends_on:
      - mysql
      - redis
    networks:
      - elite-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.elite-transfer.rule=Host(`elitetransfer.com`)"
      - "traefik.http.routers.elite-transfer.tls=true"
      - "traefik.http.routers.elite-transfer.tls.certresolver=letsencrypt"

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: elite-transfer-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: elite_transfer
      MYSQL_USER: elite_user
      MYSQL_PASSWORD: elite_password_2025
      MYSQL_ROOT_PASSWORD: elite_root_password_2025
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - elite-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: elite-transfer-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - elite-network
    command: redis-server /usr/local/etc/redis/redis.conf

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: elite-transfer-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - elite-network

  # Traefik Reverse Proxy
  traefik:
    image: traefik:v2.10
    container_name: elite-transfer-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./docker/traefik/traefik.yml:/traefik.yml:ro
      - ./docker/traefik/dynamic.yml:/dynamic.yml:ro
      - traefik_data:/data
    networks:
      - elite-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.elitetransfer.com`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.service=api@internal"

  # Elasticsearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: elite-transfer-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - elite-network

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: elite-transfer-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - elite-network

  # Logstash for Log Processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: elite-transfer-logstash
    restart: unless-stopped
    volumes:
      - ./docker/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/var/log/app:ro
    depends_on:
      - elasticsearch
    networks:
      - elite-network

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: elite-transfer-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - elite-network

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: elite-transfer-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=elite_admin_2025
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - elite-network

  # Worker for Background Jobs
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: elite-transfer-worker
    restart: unless-stopped
    command: php worker.php
    volumes:
      - ./storage:/var/www/html/storage
      - ./logs:/var/log/app
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - elite-network

  # Scheduler for Cron Jobs
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: elite-transfer-scheduler
    restart: unless-stopped
    command: php scheduler.php
    volumes:
      - ./storage:/var/www/html/storage
      - ./logs:/var/log/app
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - elite-network

# Networks
networks:
  elite-network:
    driver: bridge

# Volumes
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  traefik_data:
    driver: local
