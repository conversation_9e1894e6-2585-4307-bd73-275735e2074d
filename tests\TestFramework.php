<?php

class TestFramework {
    private $tests = [];
    private $results = [];
    private $db;
    private $config;
    
    public function __construct($database = null, $config = []) {
        $this->db = $database;
        $this->config = $config;
    }
    
    /**
     * Add a test case
     */
    public function addTest($name, $callback, $group = 'default') {
        $this->tests[] = [
            'name' => $name,
            'callback' => $callback,
            'group' => $group
        ];
    }
    
    /**
     * Run all tests
     */
    public function runTests($group = null) {
        $this->results = [];
        $startTime = microtime(true);
        
        echo "🚀 Starting Elite Transfer System Tests\n";
        echo "=====================================\n\n";
        
        $testsToRun = $group ?
            array_filter($this->tests, function($test) use ($group) { return $test['group'] === $group; }) :
            $this->tests;
        
        foreach ($testsToRun as $test) {
            $this->runSingleTest($test);
        }
        
        $endTime = microtime(true);
        $this->printSummary($endTime - $startTime);
        
        return $this->results;
    }
    
    private function runSingleTest($test) {
        $startTime = microtime(true);
        
        try {
            echo "🧪 Running: {$test['name']} ";
            
            $result = call_user_func($test['callback']);
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            if ($result === true) {
                echo "✅ PASSED ({$duration}ms)\n";
                $this->results[] = [
                    'name' => $test['name'],
                    'status' => 'passed',
                    'duration' => $duration,
                    'group' => $test['group']
                ];
            } else {
                echo "❌ FAILED ({$duration}ms)\n";
                if (is_string($result)) {
                    echo "   Error: {$result}\n";
                }
                $this->results[] = [
                    'name' => $test['name'],
                    'status' => 'failed',
                    'duration' => $duration,
                    'error' => is_string($result) ? $result : 'Test returned false',
                    'group' => $test['group']
                ];
            }
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            echo "💥 ERROR ({$duration}ms)\n";
            echo "   Exception: {$e->getMessage()}\n";
            
            $this->results[] = [
                'name' => $test['name'],
                'status' => 'error',
                'duration' => $duration,
                'error' => $e->getMessage(),
                'group' => $test['group']
            ];
        }
    }
    
    private function printSummary($totalTime) {
        $passed = count(array_filter($this->results, function($r) { return $r['status'] === 'passed'; }));
        $failed = count(array_filter($this->results, function($r) { return $r['status'] === 'failed'; }));
        $errors = count(array_filter($this->results, function($r) { return $r['status'] === 'error'; }));
        $total = count($this->results);
        
        echo "\n=====================================\n";
        echo "📊 Test Summary\n";
        echo "=====================================\n";
        echo "Total Tests: {$total}\n";
        echo "✅ Passed: {$passed}\n";
        echo "❌ Failed: {$failed}\n";
        echo "💥 Errors: {$errors}\n";
        echo "⏱️  Total Time: " . round($totalTime * 1000, 2) . "ms\n";
        
        if ($failed > 0 || $errors > 0) {
            echo "\n🔍 Failed/Error Tests:\n";
            foreach ($this->results as $result) {
                if ($result['status'] !== 'passed') {
                    echo "   - {$result['name']}: {$result['error']}\n";
                }
            }
        }
        
        $successRate = $total > 0 ? round(($passed / $total) * 100, 1) : 0;
        echo "\n🎯 Success Rate: {$successRate}%\n";
        
        if ($successRate >= 95) {
            echo "🎉 Excellent! System is ready for production.\n";
        } elseif ($successRate >= 80) {
            echo "⚠️  Good, but some issues need attention.\n";
        } else {
            echo "🚨 Critical issues found. System needs fixes.\n";
        }
    }
    
    /**
     * Assert functions for testing
     */
    public function assertTrue($condition, $message = '') {
        if (!$condition) {
            throw new Exception($message ?: 'Assertion failed: expected true');
        }
        return true;
    }
    
    public function assertFalse($condition, $message = '') {
        if ($condition) {
            throw new Exception($message ?: 'Assertion failed: expected false');
        }
        return true;
    }
    
    public function assertEquals($expected, $actual, $message = '') {
        if ($expected !== $actual) {
            $message = $message ?: "Assertion failed: expected '{$expected}', got '{$actual}'";
            throw new Exception($message);
        }
        return true;
    }
    
    public function assertNotEquals($expected, $actual, $message = '') {
        if ($expected === $actual) {
            $message = $message ?: "Assertion failed: expected not '{$expected}'";
            throw new Exception($message);
        }
        return true;
    }
    
    public function assertContains($needle, $haystack, $message = '') {
        if (is_array($haystack)) {
            if (!in_array($needle, $haystack)) {
                throw new Exception($message ?: "Assertion failed: array does not contain '{$needle}'");
            }
        } else {
            if (strpos($haystack, $needle) === false) {
                throw new Exception($message ?: "Assertion failed: string does not contain '{$needle}'");
            }
        }
        return true;
    }
    
    public function assertNotNull($value, $message = '') {
        if ($value === null) {
            throw new Exception($message ?: 'Assertion failed: expected not null');
        }
        return true;
    }
    
    public function assertNull($value, $message = '') {
        if ($value !== null) {
            throw new Exception($message ?: 'Assertion failed: expected null');
        }
        return true;
    }
    
    public function assertInstanceOf($expected, $actual, $message = '') {
        if (!($actual instanceof $expected)) {
            $actualType = is_object($actual) ? get_class($actual) : gettype($actual);
            throw new Exception($message ?: "Assertion failed: expected instance of '{$expected}', got '{$actualType}'");
        }
        return true;
    }
    
    /**
     * Mock HTTP request for testing
     */
    public function mockRequest($method, $path, $data = [], $headers = []) {
        $_SERVER['REQUEST_METHOD'] = $method;
        $_SERVER['REQUEST_URI'] = $path;
        $_SERVER['HTTP_HOST'] = 'localhost';
        
        foreach ($headers as $key => $value) {
            $_SERVER['HTTP_' . strtoupper(str_replace('-', '_', $key))] = $value;
        }
        
        if ($method === 'POST' || $method === 'PUT') {
            $_POST = $data;
        } else {
            $_GET = $data;
        }
        
        return true;
    }
    
    /**
     * Create test database
     */
    public function createTestDatabase() {
        if (!$this->db) {
            $this->db = new PDO('sqlite::memory:');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        }
        
        // Create test tables (simplified)
        $this->db->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                phone TEXT,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'customer',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        $this->db->exec("
            CREATE TABLE IF NOT EXISTS transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transfer_code TEXT UNIQUE NOT NULL,
                sender_id INTEGER NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        return $this->db;
    }
    
    /**
     * Clean test database
     */
    public function cleanTestDatabase() {
        if ($this->db) {
            $this->db->exec("DELETE FROM users");
            $this->db->exec("DELETE FROM transfers");
        }
    }
    
    /**
     * Generate test data
     */
    public function generateTestUser($data = []) {
        $defaultData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'role' => 'customer'
        ];
        
        $userData = array_merge($defaultData, $data);
        
        $stmt = $this->db->prepare("
            INSERT INTO users (name, email, phone, password, role) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userData['name'],
            $userData['email'],
            $userData['phone'],
            $userData['password'],
            $userData['role']
        ]);
        
        return $this->db->lastInsertId();
    }
    
    public function generateTestTransfer($data = []) {
        $defaultData = [
            'transfer_code' => 'TEST' . uniqid(),
            'sender_id' => 1,
            'amount' => 1000.00,
            'status' => 'pending'
        ];
        
        $transferData = array_merge($defaultData, $data);
        
        $stmt = $this->db->prepare("
            INSERT INTO transfers (transfer_code, sender_id, amount, status) 
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $transferData['transfer_code'],
            $transferData['sender_id'],
            $transferData['amount'],
            $transferData['status']
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Performance testing
     */
    public function benchmarkFunction($callback, $iterations = 1000) {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        for ($i = 0; $i < $iterations; $i++) {
            call_user_func($callback);
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        return [
            'iterations' => $iterations,
            'total_time' => $endTime - $startTime,
            'avg_time' => ($endTime - $startTime) / $iterations,
            'memory_used' => $endMemory - $startMemory,
            'peak_memory' => memory_get_peak_usage()
        ];
    }
    
    /**
     * Export test results
     */
    public function exportResults($format = 'json') {
        $data = [
            'timestamp' => date('Y-m-d H:i:s'),
            'total_tests' => count($this->results),
            'passed' => count(array_filter($this->results, fn($r) => $r['status'] === 'passed')),
            'failed' => count(array_filter($this->results, fn($r) => $r['status'] === 'failed')),
            'errors' => count(array_filter($this->results, fn($r) => $r['status'] === 'error')),
            'results' => $this->results
        ];
        
        switch ($format) {
            case 'json':
                return json_encode($data, JSON_PRETTY_PRINT);
            case 'xml':
                return $this->arrayToXml($data);
            case 'html':
                return $this->arrayToHtml($data);
            default:
                return $data;
        }
    }
    
    private function arrayToXml($data, $rootElement = 'testResults') {
        $xml = new SimpleXMLElement("<?xml version=\"1.0\"?><{$rootElement}></{$rootElement}>");
        $this->arrayToXmlRecursive($data, $xml);
        return $xml->asXML();
    }
    
    private function arrayToXmlRecursive($data, &$xml) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if (is_numeric($key)) {
                    $key = 'item';
                }
                $subnode = $xml->addChild($key);
                $this->arrayToXmlRecursive($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }
    
    private function arrayToHtml($data) {
        $html = "<html><head><title>Test Results</title></head><body>";
        $html .= "<h1>Elite Transfer System - Test Results</h1>";
        $html .= "<p>Generated: {$data['timestamp']}</p>";
        $html .= "<h2>Summary</h2>";
        $html .= "<ul>";
        $html .= "<li>Total Tests: {$data['total_tests']}</li>";
        $html .= "<li>Passed: {$data['passed']}</li>";
        $html .= "<li>Failed: {$data['failed']}</li>";
        $html .= "<li>Errors: {$data['errors']}</li>";
        $html .= "</ul>";
        $html .= "<h2>Detailed Results</h2>";
        $html .= "<table border='1'><tr><th>Test Name</th><th>Status</th><th>Duration</th><th>Error</th></tr>";
        
        foreach ($data['results'] as $result) {
            $status = $result['status'];
            $color = $status === 'passed' ? 'green' : ($status === 'failed' ? 'red' : 'orange');
            $html .= "<tr>";
            $html .= "<td>{$result['name']}</td>";
            $html .= "<td style='color: {$color}'>{$status}</td>";
            $html .= "<td>{$result['duration']}ms</td>";
            $html .= "<td>" . ($result['error'] ?? '') . "</td>";
            $html .= "</tr>";
        }
        
        $html .= "</table></body></html>";
        return $html;
    }
}
