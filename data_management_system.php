<?php

echo "🗄️ Data Management System - Elite Transfer\n\n";

class DataManager {
    private $db;
    
    public function __construct() {
        $this->db = new PDO('sqlite:database/elite_transfer_production.db');
        $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    // User Management
    public function createUser($data) {
        $userCode = 'USR' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        
        $stmt = $this->db->prepare("
            INSERT INTO users (user_code, name, email, phone, password_hash, role, status, kyc_status, kyc_level, country_id, address, date_of_birth, national_id, passport_number, occupation, monthly_income, source_of_funds, risk_score) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $userCode, $data['name'], $data['email'], $data['phone'],
            password_hash($data['password'], PASSWORD_DEFAULT),
            $data['role'] ?? 'customer',
            $data['status'] ?? 'active',
            $data['kyc_status'] ?? 'pending',
            $data['kyc_level'] ?? 1,
            $data['country_id'] ?? 1,
            $data['address'] ?? '',
            $data['date_of_birth'] ?? null,
            $data['national_id'] ?? '',
            $data['passport_number'] ?? '',
            $data['occupation'] ?? '',
            $data['monthly_income'] ?? 0.00,
            $data['source_of_funds'] ?? '',
            $data['risk_score'] ?? 0
        ]);
    }
    
    public function updateUser($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        
        $values[] = $id;
        $sql = "UPDATE users SET " . implode(', ', $fields) . ", updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    public function deleteUser($id, $softDelete = true) {
        if ($softDelete) {
            $stmt = $this->db->prepare("UPDATE users SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?");
        } else {
            $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
        }
        return $stmt->execute([$id]);
    }
    
    public function getUsers($filters = [], $limit = 100, $offset = 0) {
        $where = [];
        $params = [];
        
        if (!empty($filters['role'])) {
            $where[] = "role = ?";
            $params[] = $filters['role'];
        }
        
        if (!empty($filters['status'])) {
            $where[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['kyc_status'])) {
            $where[] = "kyc_status = ?";
            $params[] = $filters['kyc_status'];
        }
        
        if (!isset($filters['include_deleted']) || !$filters['include_deleted']) {
            $where[] = "deleted_at IS NULL";
        }
        
        $whereClause = empty($where) ? "" : "WHERE " . implode(' AND ', $where);
        $sql = "SELECT * FROM users $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Transfer Management
    public function createTransfer($data) {
        $transferCode = 'ET' . date('Ymd') . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        $pickupCode = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        
        $stmt = $this->db->prepare("
            INSERT INTO transfers (
                transfer_code, pickup_code, sender_id, receiver_id, sender_name, sender_phone, 
                sender_country_id, receiver_name, receiver_phone, receiver_country_id,
                amount, sender_currency, receiver_currency, exchange_rate, converted_amount,
                fee_amount, total_amount, purpose, payment_method, pickup_method, status,
                risk_score, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $transferCode, $pickupCode, $data['sender_id'], $data['receiver_id'] ?? null,
            $data['sender_name'], $data['sender_phone'], $data['sender_country_id'],
            $data['receiver_name'], $data['receiver_phone'], $data['receiver_country_id'],
            $data['amount'], $data['sender_currency'], $data['receiver_currency'],
            $data['exchange_rate'], $data['converted_amount'], $data['fee_amount'],
            $data['total_amount'], $data['purpose'] ?? '', $data['payment_method'] ?? 'cash',
            $data['pickup_method'] ?? 'cash', $data['status'] ?? 'pending',
            $data['risk_score'] ?? 0, $data['notes'] ?? ''
        ]);
    }
    
    public function updateTransfer($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        
        $values[] = $id;
        $sql = "UPDATE transfers SET " . implode(', ', $fields) . ", updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    public function deleteTransfer($id, $softDelete = true) {
        if ($softDelete) {
            $stmt = $this->db->prepare("UPDATE transfers SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?");
        } else {
            $stmt = $this->db->prepare("DELETE FROM transfers WHERE id = ?");
        }
        return $stmt->execute([$id]);
    }
    
    public function getTransfers($filters = [], $limit = 100, $offset = 0) {
        $where = [];
        $params = [];
        
        if (!empty($filters['status'])) {
            $where[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['sender_id'])) {
            $where[] = "sender_id = ?";
            $params[] = $filters['sender_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = "created_at >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = "created_at <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!isset($filters['include_deleted']) || !$filters['include_deleted']) {
            $where[] = "deleted_at IS NULL";
        }
        
        $whereClause = empty($where) ? "" : "WHERE " . implode(' AND ', $where);
        $sql = "SELECT * FROM transfers $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Country Management
    public function createCountry($data) {
        $stmt = $this->db->prepare("
            INSERT INTO countries (name, code, iso3_code, currency, currency_symbol, phone_prefix, region, is_active, risk_level, max_transaction_limit, daily_limit, monthly_limit) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $data['name'], $data['code'], $data['iso3_code'] ?? '',
            $data['currency'], $data['currency_symbol'] ?? '',
            $data['phone_prefix'] ?? '', $data['region'] ?? '',
            $data['is_active'] ?? 1, $data['risk_level'] ?? 1,
            $data['max_transaction_limit'] ?? 50000.00,
            $data['daily_limit'] ?? 10000.00,
            $data['monthly_limit'] ?? 100000.00
        ]);
    }
    
    public function updateCountry($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        
        $values[] = $id;
        $sql = "UPDATE countries SET " . implode(', ', $fields) . ", updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    public function deleteCountry($id) {
        $stmt = $this->db->prepare("DELETE FROM countries WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    // Exchange Rate Management
    public function createExchangeRate($data) {
        $stmt = $this->db->prepare("
            INSERT INTO exchange_rates (from_currency, to_currency, rate, buy_rate, sell_rate, provider, margin_percentage, valid_from, valid_until) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $data['from_currency'], $data['to_currency'], $data['rate'],
            $data['buy_rate'] ?? $data['rate'], $data['sell_rate'] ?? $data['rate'],
            $data['provider'] ?? 'manual', $data['margin_percentage'] ?? 0.0000,
            $data['valid_from'] ?? date('Y-m-d H:i:s'),
            $data['valid_until'] ?? date('Y-m-d H:i:s', strtotime('+1 day'))
        ]);
    }
    
    public function updateExchangeRate($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        
        $values[] = $id;
        $sql = "UPDATE exchange_rates SET " . implode(', ', $fields) . ", updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    // Bulk Operations
    public function bulkInsertUsers($users) {
        $this->db->beginTransaction();
        try {
            foreach ($users as $user) {
                $this->createUser($user);
            }
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function bulkInsertTransfers($transfers) {
        $this->db->beginTransaction();
        try {
            foreach ($transfers as $transfer) {
                $this->createTransfer($transfer);
            }
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    // Statistics
    public function getStatistics() {
        $stats = [];
        
        $stats['users'] = [
            'total' => $this->db->query("SELECT COUNT(*) FROM users WHERE deleted_at IS NULL")->fetchColumn(),
            'active' => $this->db->query("SELECT COUNT(*) FROM users WHERE status = 'active' AND deleted_at IS NULL")->fetchColumn(),
            'verified' => $this->db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'verified' AND deleted_at IS NULL")->fetchColumn()
        ];
        
        $stats['transfers'] = [
            'total' => $this->db->query("SELECT COUNT(*) FROM transfers WHERE deleted_at IS NULL")->fetchColumn(),
            'completed' => $this->db->query("SELECT COUNT(*) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn(),
            'pending' => $this->db->query("SELECT COUNT(*) FROM transfers WHERE status IN ('pending', 'pending_payment') AND deleted_at IS NULL")->fetchColumn(),
            'total_amount' => $this->db->query("SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE status = 'completed' AND deleted_at IS NULL")->fetchColumn()
        ];
        
        $stats['countries'] = [
            'total' => $this->db->query("SELECT COUNT(*) FROM countries")->fetchColumn(),
            'active' => $this->db->query("SELECT COUNT(*) FROM countries WHERE is_active = 1")->fetchColumn()
        ];
        
        return $stats;
    }
}

// Example usage
try {
    $dm = new DataManager();
    
    echo "📊 Current Statistics:\n";
    $stats = $dm->getStatistics();
    
    echo "Users: {$stats['users']['total']} total, {$stats['users']['active']} active, {$stats['users']['verified']} verified\n";
    echo "Transfers: {$stats['transfers']['total']} total, {$stats['transfers']['completed']} completed, {$stats['transfers']['pending']} pending\n";
    echo "Countries: {$stats['countries']['total']} total, {$stats['countries']['active']} active\n";
    echo "Total Transfer Amount: $" . number_format($stats['transfers']['total_amount'], 2) . "\n";
    
    echo "\n✅ Data Management System ready for use!\n";
    echo "🔧 All CRUD operations available for Users, Transfers, Countries, and Exchange Rates\n";
    echo "📈 Bulk operations supported for large data imports\n";
    echo "🗄️ Soft delete implemented for data integrity\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
