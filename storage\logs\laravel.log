[2025-07-25 08:08:51] local.ERROR: The C:\xampp\htdocs\WST_Transfir\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\xampp\\htdocs\\WST_Transfir\\bootstrap\\cache directory must be present and writable. at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-25 08:09:13] local.ERROR: The C:\xampp\htdocs\WST_Transfir\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\xampp\\htdocs\\WST_Transfir\\bootstrap\\cache directory must be present and writable. at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-25 08:09:49] local.ERROR: The C:\xampp\htdocs\WST_Transfir\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\xampp\\htdocs\\WST_Transfir\\bootstrap\\cache directory must be present and writable. at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-25 08:11:13] local.ERROR: The C:\xampp\htdocs\WST_Transfir\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\xampp\\htdocs\\WST_Transfir\\bootstrap\\cache directory must be present and writable. at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#8 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#9 C:\\xampp\\htdocs\\WST_Transfir\\public\\index.php(29): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#10 {main}
"} 
[2025-07-25 08:13:56] local.ERROR: Class "Laravel\Pail\PailServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Laravel\\Pail\\PailServiceProvider\" not found at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Laravel\\\\Pail\\\\Pa...')
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-07-25 08:14:00] local.ERROR: Class "Laravel\Pail\PailServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Laravel\\Pail\\PailServiceProvider\" not found at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Laravel\\\\Pail\\\\Pa...')
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-07-25 08:14:52] local.ERROR: Class "App\Providers\AppServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\AppServiceProvider\" not found at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\A...')
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-07-25 08:15:43] local.ERROR: Class "App\Providers\AppServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\AppServiceProvider\" not found at C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\A...')
#1 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\WST_Transfir\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\WST_Transfir\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
