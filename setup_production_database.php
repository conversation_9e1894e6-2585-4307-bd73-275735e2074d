<?php

echo "🗄️ Setting up Production Database - Elite Transfer System\n\n";

try {
    // Connect to database
    $db = new PDO('sqlite:database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Enable foreign keys
    $db->exec("PRAGMA foreign_keys = ON");
    
    // Performance optimizations for large data
    $db->exec("PRAGMA journal_mode = WAL");
    $db->exec("PRAGMA synchronous = NORMAL");
    $db->exec("PRAGMA cache_size = 10000");
    $db->exec("PRAGMA temp_store = MEMORY");
    
    echo "✅ Database connection established with performance optimizations\n\n";
    
    // Drop existing tables if they exist
    $tables = [
        'transfer_attachments',
        'transfer_status_history', 
        'notifications',
        'audit_logs',
        'payments',
        'transfers',
        'exchange_rates',
        'system_settings',
        'countries',
        'users'
    ];
    
    foreach ($tables as $table) {
        $db->exec("DROP TABLE IF EXISTS $table");
        echo "🗑️ Dropped table: $table\n";
    }
    
    echo "\n📋 Creating optimized tables for large data...\n\n";
    
    // Users table with enhanced fields
    $db->exec("
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            phone TEXT,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'customer' CHECK (role IN ('admin', 'agent', 'customer', 'compliance', 'manager')),
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
            kyc_status TEXT DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'verified', 'rejected', 'expired')),
            kyc_level INTEGER DEFAULT 1 CHECK (kyc_level BETWEEN 1 AND 5),
            country_id INTEGER,
            address TEXT,
            date_of_birth DATE,
            national_id TEXT,
            passport_number TEXT,
            occupation TEXT,
            monthly_income DECIMAL(15,2),
            source_of_funds TEXT,
            risk_score INTEGER DEFAULT 0 CHECK (risk_score BETWEEN 0 AND 100),
            last_login_at DATETIME,
            email_verified_at DATETIME,
            phone_verified_at DATETIME,
            two_factor_enabled BOOLEAN DEFAULT 0,
            two_factor_secret TEXT,
            login_attempts INTEGER DEFAULT 0,
            locked_until DATETIME,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            deleted_at DATETIME
        )
    ");
    
    // Countries table with enhanced data
    $db->exec("
        CREATE TABLE countries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            iso3_code TEXT,
            currency TEXT NOT NULL,
            currency_symbol TEXT,
            phone_prefix TEXT,
            flag_url TEXT,
            region TEXT,
            subregion TEXT,
            population INTEGER,
            area_km2 DECIMAL(15,2),
            gdp_usd DECIMAL(20,2),
            languages TEXT,
            timezone TEXT,
            is_active BOOLEAN DEFAULT 1,
            risk_level INTEGER DEFAULT 1 CHECK (risk_level BETWEEN 1 AND 5),
            compliance_requirements TEXT,
            max_transaction_limit DECIMAL(15,2),
            daily_limit DECIMAL(15,2),
            monthly_limit DECIMAL(15,2),
            requires_kyc BOOLEAN DEFAULT 1,
            processing_time_hours INTEGER DEFAULT 24,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // System settings with categories
    $db->exec("
        CREATE TABLE system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            data_type TEXT DEFAULT 'string' CHECK (data_type IN ('string', 'integer', 'decimal', 'boolean', 'json')),
            description TEXT,
            is_public BOOLEAN DEFAULT 0,
            is_encrypted BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
        )
    ");
    
    // Exchange rates with historical data
    $db->exec("
        CREATE TABLE exchange_rates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            from_currency TEXT NOT NULL,
            to_currency TEXT NOT NULL,
            rate DECIMAL(15,8) NOT NULL,
            buy_rate DECIMAL(15,8),
            sell_rate DECIMAL(15,8),
            mid_rate DECIMAL(15,8),
            provider TEXT DEFAULT 'manual',
            source_url TEXT,
            margin_percentage DECIMAL(5,4) DEFAULT 0.0000,
            is_active BOOLEAN DEFAULT 1,
            valid_from DATETIME DEFAULT CURRENT_TIMESTAMP,
            valid_until DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // Enhanced transfers table for large scale
    $db->exec("
        CREATE TABLE transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_code TEXT UNIQUE NOT NULL,
            pickup_code TEXT NOT NULL,
            reference_number TEXT,
            sender_id INTEGER NOT NULL,
            receiver_id INTEGER,
            agent_id INTEGER,
            branch_id INTEGER,
            
            -- Sender information
            sender_name TEXT NOT NULL,
            sender_phone TEXT NOT NULL,
            sender_email TEXT,
            sender_address TEXT,
            sender_country_id INTEGER NOT NULL,
            sender_city TEXT,
            sender_postal_code TEXT,
            sender_id_type TEXT,
            sender_id_number TEXT,
            
            -- Receiver information  
            receiver_name TEXT NOT NULL,
            receiver_phone TEXT NOT NULL,
            receiver_email TEXT,
            receiver_address TEXT,
            receiver_country_id INTEGER NOT NULL,
            receiver_city TEXT,
            receiver_postal_code TEXT,
            receiver_id_type TEXT,
            receiver_id_number TEXT,
            receiver_bank_name TEXT,
            receiver_bank_account TEXT,
            receiver_bank_code TEXT,
            
            -- Financial details
            amount DECIMAL(15,2) NOT NULL,
            sender_currency TEXT NOT NULL,
            receiver_currency TEXT NOT NULL,
            exchange_rate DECIMAL(15,8) NOT NULL,
            converted_amount DECIMAL(15,2) NOT NULL,
            fee_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
            tax_amount DECIMAL(15,2) DEFAULT 0.00,
            total_amount DECIMAL(15,2) NOT NULL,
            profit_amount DECIMAL(15,2) DEFAULT 0.00,
            
            -- Transaction details
            purpose TEXT,
            relationship TEXT,
            source_of_funds TEXT,
            payment_method TEXT DEFAULT 'cash' CHECK (payment_method IN ('cash', 'card', 'bank_transfer', 'mobile_money', 'crypto')),
            pickup_method TEXT DEFAULT 'cash' CHECK (pickup_method IN ('cash', 'bank_deposit', 'mobile_money', 'card')),
            
            -- Status and tracking
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'pending_payment', 'paid', 'processing', 'ready_for_pickup', 'completed', 'cancelled', 'refunded', 'failed', 'on_hold')),
            priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
            
            -- Compliance and risk
            risk_score INTEGER DEFAULT 0 CHECK (risk_score BETWEEN 0 AND 100),
            compliance_status TEXT DEFAULT 'pending' CHECK (compliance_status IN ('pending', 'approved', 'rejected', 'under_review')),
            aml_status TEXT DEFAULT 'pending' CHECK (aml_status IN ('pending', 'cleared', 'flagged', 'blocked')),
            kyc_required BOOLEAN DEFAULT 1,
            
            -- Timing
            estimated_delivery DATETIME,
            completed_at DATETIME,
            expires_at DATETIME,
            
            -- Additional data
            notes TEXT,
            internal_notes TEXT,
            metadata TEXT, -- JSON data
            
            -- Audit fields
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            deleted_at DATETIME,
            
            FOREIGN KEY (sender_id) REFERENCES users(id),
            FOREIGN KEY (receiver_id) REFERENCES users(id),
            FOREIGN KEY (agent_id) REFERENCES users(id),
            FOREIGN KEY (sender_country_id) REFERENCES countries(id),
            FOREIGN KEY (receiver_country_id) REFERENCES countries(id)
        )
    ");
    
    echo "✅ Created enhanced tables\n\n";
    
    // Create comprehensive indexes for performance
    echo "📊 Creating performance indexes...\n";
    
    $indexes = [
        // Users indexes
        "CREATE INDEX idx_users_email ON users(email)",
        "CREATE INDEX idx_users_phone ON users(phone)",
        "CREATE INDEX idx_users_role_status ON users(role, status)",
        "CREATE INDEX idx_users_kyc ON users(kyc_status, kyc_level)",
        "CREATE INDEX idx_users_created ON users(created_at)",
        "CREATE INDEX idx_users_deleted ON users(deleted_at)",
        
        // Countries indexes
        "CREATE INDEX idx_countries_code ON countries(code)",
        "CREATE INDEX idx_countries_currency ON countries(currency)",
        "CREATE INDEX idx_countries_active ON countries(is_active)",
        "CREATE INDEX idx_countries_region ON countries(region)",
        
        // Exchange rates indexes
        "CREATE INDEX idx_rates_currencies ON exchange_rates(from_currency, to_currency)",
        "CREATE INDEX idx_rates_active ON exchange_rates(is_active)",
        "CREATE INDEX idx_rates_valid ON exchange_rates(valid_from, valid_until)",
        "CREATE INDEX idx_rates_created ON exchange_rates(created_at)",
        
        // Transfers indexes - critical for performance
        "CREATE INDEX idx_transfers_code ON transfers(transfer_code)",
        "CREATE INDEX idx_transfers_pickup ON transfers(pickup_code)",
        "CREATE INDEX idx_transfers_sender ON transfers(sender_id)",
        "CREATE INDEX idx_transfers_receiver ON transfers(receiver_id)",
        "CREATE INDEX idx_transfers_agent ON transfers(agent_id)",
        "CREATE INDEX idx_transfers_status ON transfers(status)",
        "CREATE INDEX idx_transfers_created ON transfers(created_at)",
        "CREATE INDEX idx_transfers_updated ON transfers(updated_at)",
        "CREATE INDEX idx_transfers_deleted ON transfers(deleted_at)",
        "CREATE INDEX idx_transfers_countries ON transfers(sender_country_id, receiver_country_id)",
        "CREATE INDEX idx_transfers_amount ON transfers(amount)",
        "CREATE INDEX idx_transfers_currency ON transfers(sender_currency, receiver_currency)",
        "CREATE INDEX idx_transfers_status_created ON transfers(status, created_at)",
        "CREATE INDEX idx_transfers_compliance ON transfers(compliance_status, aml_status)",
        "CREATE INDEX idx_transfers_risk ON transfers(risk_score)",
        "CREATE INDEX idx_transfers_priority ON transfers(priority)",
        "CREATE INDEX idx_transfers_payment ON transfers(payment_method, pickup_method)",
        
        // System settings indexes
        "CREATE INDEX idx_settings_category ON system_settings(category)",
        "CREATE INDEX idx_settings_key ON system_settings(key)",
        "CREATE INDEX idx_settings_public ON system_settings(is_public)"
    ];
    
    foreach ($indexes as $index) {
        $db->exec($index);
    }
    
    echo "✅ Created " . count($indexes) . " performance indexes\n\n";
    
    echo "🎉 Production database setup completed successfully!\n";
    echo "📊 Database is now optimized for large-scale data\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
