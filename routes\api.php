<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\TransferController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\CountryController;
use App\Http\Controllers\Api\CurrencyController;
use App\Http\Controllers\Api\BranchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes
Route::prefix('v1')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/register', [AuthController::class, 'register']);
        Route::post('/verify-otp', [AuthController::class, 'verifyOtp']);
        Route::post('/resend-otp', [AuthController::class, 'resendOtp']);
        Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('/reset-password', [AuthController::class, 'resetPassword']);
    });
    
    // Public data routes
    Route::get('/countries', [CountryController::class, 'index']);
    Route::get('/currencies', [CurrencyController::class, 'index']);
    Route::get('/branches/{country}', [BranchController::class, 'getByCountry']);
    Route::get('/exchange-rate/{from}/{to}', [CurrencyController::class, 'getExchangeRate']);
    
    // Public transfer tracking
    Route::post('/track-transfer', [TransferController::class, 'track']);
    
    // Protected API routes
    Route::middleware('auth:sanctum')->group(function () {
        
        // Authentication
        Route::prefix('auth')->group(function () {
            Route::post('/logout', [AuthController::class, 'logout']);
            Route::get('/user', [AuthController::class, 'user']);
            Route::post('/refresh', [AuthController::class, 'refresh']);
        });
        
        // User management
        Route::prefix('user')->group(function () {
            Route::get('/profile', [UserController::class, 'profile']);
            Route::put('/profile', [UserController::class, 'updateProfile']);
            Route::post('/upload-photo', [UserController::class, 'uploadPhoto']);
            Route::post('/enable-2fa', [UserController::class, 'enable2FA']);
            Route::post('/disable-2fa', [UserController::class, 'disable2FA']);
            Route::post('/change-password', [UserController::class, 'changePassword']);
        });
        
        // Transfer management
        Route::prefix('transfers')->group(function () {
            Route::get('/', [TransferController::class, 'index']);
            Route::post('/', [TransferController::class, 'store']);
            Route::get('/{transfer}', [TransferController::class, 'show']);
            Route::post('/calculate-fees', [TransferController::class, 'calculateFees']);
            Route::post('/{transfer}/cancel', [TransferController::class, 'cancel']);
            Route::get('/user/sent', [TransferController::class, 'userSent']);
            Route::get('/user/received', [TransferController::class, 'userReceived']);
        });
        
        // Dashboard data
        Route::get('/dashboard/stats', [UserController::class, 'getDashboardStats']);
        Route::get('/dashboard/chart-data', [UserController::class, 'getChartData']);
        Route::get('/dashboard/recent-transfers', [UserController::class, 'getRecentTransfers']);
        
        // Admin routes
        Route::middleware('role:admin,super_admin')->prefix('admin')->group(function () {
            
            // User management
            Route::prefix('users')->group(function () {
                Route::get('/', [UserController::class, 'index']);
                Route::post('/', [UserController::class, 'store']);
                Route::get('/{user}', [UserController::class, 'show']);
                Route::put('/{user}', [UserController::class, 'update']);
                Route::delete('/{user}', [UserController::class, 'destroy']);
                Route::post('/{user}/toggle-status', [UserController::class, 'toggleStatus']);
                Route::post('/{user}/reset-password', [UserController::class, 'resetPassword']);
            });
            
            // Transfer management
            Route::prefix('transfers')->group(function () {
                Route::get('/', [TransferController::class, 'adminIndex']);
                Route::get('/{transfer}', [TransferController::class, 'adminShow']);
                Route::post('/{transfer}/update-status', [TransferController::class, 'updateStatus']);
                Route::post('/{transfer}/assign-agent', [TransferController::class, 'assignAgent']);
                Route::get('/suspicious/list', [TransferController::class, 'getSuspiciousTransfers']);
            });
            
            // Branch management
            Route::prefix('branches')->group(function () {
                Route::get('/', [BranchController::class, 'index']);
                Route::post('/', [BranchController::class, 'store']);
                Route::get('/{branch}', [BranchController::class, 'show']);
                Route::put('/{branch}', [BranchController::class, 'update']);
                Route::delete('/{branch}', [BranchController::class, 'destroy']);
                Route::get('/{branch}/stats', [BranchController::class, 'getStats']);
            });
            
            // Reports
            Route::prefix('reports')->group(function () {
                Route::get('/transfers', [TransferController::class, 'getTransferReports']);
                Route::get('/financial', [TransferController::class, 'getFinancialReports']);
                Route::get('/agents', [UserController::class, 'getAgentReports']);
                Route::get('/suspicious', [TransferController::class, 'getSuspiciousReports']);
                Route::post('/export', [TransferController::class, 'exportReports']);
            });
            
            // System settings
            Route::prefix('settings')->group(function () {
                Route::get('/', [UserController::class, 'getSettings']);
                Route::put('/', [UserController::class, 'updateSettings']);
                Route::get('/currencies', [CurrencyController::class, 'adminIndex']);
                Route::post('/currencies', [CurrencyController::class, 'store']);
                Route::put('/currencies/{currency}', [CurrencyController::class, 'update']);
                Route::get('/countries', [CountryController::class, 'adminIndex']);
                Route::post('/countries', [CountryController::class, 'store']);
                Route::put('/countries/{country}', [CountryController::class, 'update']);
            });
            
            // Audit logs
            Route::get('/audit-logs', [UserController::class, 'getAuditLogs']);
        });
        
        // Agent routes
        Route::middleware('role:agent')->prefix('agent')->group(function () {
            
            // Transfer management for agents
            Route::prefix('transfers')->group(function () {
                Route::get('/', [TransferController::class, 'agentIndex']);
                Route::get('/pending', [TransferController::class, 'agentPending']);
                Route::get('/ready-for-pickup', [TransferController::class, 'agentReadyForPickup']);
                Route::get('/{transfer}', [TransferController::class, 'agentShow']);
                Route::post('/{transfer}/process', [TransferController::class, 'agentProcess']);
                Route::post('/{transfer}/complete', [TransferController::class, 'agentComplete']);
                Route::post('/{transfer}/verify-pickup', [TransferController::class, 'agentVerifyPickup']);
            });
            
            // Customer management
            Route::prefix('customers')->group(function () {
                Route::get('/', [UserController::class, 'agentCustomers']);
                Route::post('/', [UserController::class, 'agentCreateCustomer']);
                Route::get('/{customer}', [UserController::class, 'agentShowCustomer']);
                Route::put('/{customer}', [UserController::class, 'agentUpdateCustomer']);
            });
            
            // Agent reports
            Route::prefix('reports')->group(function () {
                Route::get('/daily', [TransferController::class, 'agentDailyReport']);
                Route::get('/monthly', [TransferController::class, 'agentMonthlyReport']);
                Route::get('/commissions', [TransferController::class, 'agentCommissionReport']);
            });
            
            // Agent dashboard
            Route::get('/dashboard/stats', [UserController::class, 'getAgentDashboardStats']);
        });
    });
});

// Webhook routes (for external integrations)
Route::prefix('webhooks')->group(function () {
    Route::post('/exchange-rates', [CurrencyController::class, 'updateExchangeRates']);
    Route::post('/payment-notification', [TransferController::class, 'paymentNotification']);
    Route::post('/compliance-check', [TransferController::class, 'complianceCheck']);
});

// Health check route
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => '6.0.0',
        'environment' => app()->environment(),
    ]);
});

// API Documentation route
Route::get('/docs', function () {
    return response()->json([
        'name' => 'Elite Financial Transfer System API',
        'version' => '6.0.0',
        'description' => 'Advanced Money Transfer System API',
        'documentation' => url('/api/documentation'),
        'endpoints' => [
            'authentication' => '/api/v1/auth/*',
            'transfers' => '/api/v1/transfers/*',
            'users' => '/api/v1/user/*',
            'admin' => '/api/v1/admin/*',
            'agent' => '/api/v1/agent/*',
        ],
    ]);
});
