<?php

class SystemMonitor {
    private $db;
    private $config;
    private $alerts = [];
    
    public function __construct($database, $config = []) {
        $this->db = $database;
        $this->config = $config;
    }
    
    /**
     * Comprehensive system health check
     */
    public function performHealthCheck() {
        $healthStatus = [
            'timestamp' => date('Y-m-d H:i:s'),
            'overall_status' => 'healthy',
            'components' => [],
            'metrics' => [],
            'alerts' => []
        ];
        
        // Database health
        $healthStatus['components']['database'] = $this->checkDatabaseHealth();
        
        // Application health
        $healthStatus['components']['application'] = $this->checkApplicationHealth();
        
        // External services health
        $healthStatus['components']['external_services'] = $this->checkExternalServicesHealth();
        
        // System resources
        $healthStatus['components']['system_resources'] = $this->checkSystemResources();
        
        // Security status
        $healthStatus['components']['security'] = $this->checkSecurityStatus();
        
        // Performance metrics
        $healthStatus['metrics'] = $this->collectPerformanceMetrics();
        
        // Determine overall status
        $healthStatus['overall_status'] = $this->determineOverallStatus($healthStatus['components']);
        
        // Generate alerts if needed
        $healthStatus['alerts'] = $this->generateAlerts($healthStatus);
        
        // Store health check results
        $this->storeHealthCheckResults($healthStatus);
        
        return $healthStatus;
    }
    
    private function checkDatabaseHealth() {
        try {
            $startTime = microtime(true);
            
            // Test connection
            $this->db->query("SELECT 1");
            
            // Check response time
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            // Check database size
            $stmt = $this->db->query("
                SELECT 
                    COUNT(*) as total_transfers,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers
                FROM transfers
            ");
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'connection' => 'active',
                'statistics' => $stats,
                'last_checked' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'last_checked' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    private function checkApplicationHealth() {
        $status = [
            'status' => 'healthy',
            'php_version' => PHP_VERSION,
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'disk_space' => [
                'free' => disk_free_space('.'),
                'total' => disk_total_space('.')
            ],
            'extensions' => [
                'pdo' => extension_loaded('pdo'),
                'curl' => extension_loaded('curl'),
                'json' => extension_loaded('json'),
                'mbstring' => extension_loaded('mbstring')
            ]
        ];
        
        // Check critical extensions
        $requiredExtensions = ['pdo', 'curl', 'json', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $status['status'] = 'unhealthy';
                $status['missing_extensions'][] = $ext;
            }
        }
        
        // Check memory usage
        $memoryUsagePercent = (memory_get_usage(true) / $this->parseMemoryLimit()) * 100;
        if ($memoryUsagePercent > 80) {
            $status['status'] = 'warning';
            $status['warnings'][] = 'High memory usage: ' . round($memoryUsagePercent, 1) . '%';
        }
        
        // Check disk space
        $diskUsagePercent = (1 - (disk_free_space('.') / disk_total_space('.'))) * 100;
        if ($diskUsagePercent > 90) {
            $status['status'] = 'critical';
            $status['errors'][] = 'Low disk space: ' . round($diskUsagePercent, 1) . '% used';
        }
        
        return $status;
    }
    
    private function checkExternalServicesHealth() {
        $services = [
            'sms_service' => $this->checkSMSService(),
            'email_service' => $this->checkEmailService(),
            'exchange_rate_service' => $this->checkExchangeRateService(),
            'payment_gateway' => $this->checkPaymentGateway()
        ];
        
        $overallStatus = 'healthy';
        foreach ($services as $service) {
            if ($service['status'] === 'unhealthy') {
                $overallStatus = 'unhealthy';
                break;
            } elseif ($service['status'] === 'warning' && $overallStatus === 'healthy') {
                $overallStatus = 'warning';
            }
        }
        
        return [
            'status' => $overallStatus,
            'services' => $services,
            'last_checked' => date('Y-m-d H:i:s')
        ];
    }
    
    private function checkSMSService() {
        try {
            // Mock SMS service health check
            $startTime = microtime(true);
            
            // Simulate API call
            usleep(50000); // 50ms delay
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => 'healthy',
                'provider' => 'twilio',
                'response_time_ms' => round($responseTime, 2),
                'last_successful_send' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function checkEmailService() {
        try {
            // Mock email service health check
            $startTime = microtime(true);
            
            // Simulate API call
            usleep(30000); // 30ms delay
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => 'healthy',
                'provider' => 'sendgrid',
                'response_time_ms' => round($responseTime, 2),
                'last_successful_send' => date('Y-m-d H:i:s', strtotime('-2 minutes'))
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function checkExchangeRateService() {
        try {
            // Mock exchange rate service health check
            $startTime = microtime(true);
            
            // Simulate API call
            usleep(100000); // 100ms delay
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => 'healthy',
                'provider' => 'fixer',
                'response_time_ms' => round($responseTime, 2),
                'last_rate_update' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function checkPaymentGateway() {
        try {
            // Mock payment gateway health check
            $startTime = microtime(true);
            
            // Simulate API call
            usleep(200000); // 200ms delay
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => 'healthy',
                'provider' => 'stripe',
                'response_time_ms' => round($responseTime, 2),
                'last_successful_payment' => date('Y-m-d H:i:s', strtotime('-10 minutes'))
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function checkSystemResources() {
        $loadAverage = sys_getloadavg();
        
        return [
            'status' => 'healthy',
            'cpu' => [
                'load_average' => $loadAverage,
                'load_1min' => $loadAverage[0] ?? 0,
                'load_5min' => $loadAverage[1] ?? 0,
                'load_15min' => $loadAverage[2] ?? 0
            ],
            'memory' => [
                'used_bytes' => memory_get_usage(true),
                'peak_bytes' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'disk' => [
                'free_bytes' => disk_free_space('.'),
                'total_bytes' => disk_total_space('.'),
                'usage_percent' => round((1 - (disk_free_space('.') / disk_total_space('.'))) * 100, 2)
            ]
        ];
    }
    
    private function checkSecurityStatus() {
        return [
            'status' => 'healthy',
            'ssl_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'security_headers' => [
                'x_frame_options' => true,
                'x_content_type_options' => true,
                'x_xss_protection' => true
            ],
            'failed_login_attempts' => $this->getFailedLoginAttempts(),
            'suspicious_activities' => $this->getSuspiciousActivities(),
            'last_security_scan' => date('Y-m-d H:i:s', strtotime('-1 hour'))
        ];
    }
    
    private function collectPerformanceMetrics() {
        try {
            // Database performance
            $dbMetrics = $this->collectDatabaseMetrics();
            
            // API performance
            $apiMetrics = $this->collectAPIMetrics();
            
            // Business metrics
            $businessMetrics = $this->collectBusinessMetrics();
            
            return [
                'database' => $dbMetrics,
                'api' => $apiMetrics,
                'business' => $businessMetrics,
                'collected_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
                'collected_at' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    private function collectDatabaseMetrics() {
        $stmt = $this->db->query("
            SELECT 
                COUNT(*) as total_transfers,
                COUNT(CASE WHEN created_at >= DATE('now', '-1 hour') THEN 1 END) as transfers_last_hour,
                COUNT(CASE WHEN created_at >= DATE('now', '-1 day') THEN 1 END) as transfers_last_day,
                AVG(amount) as avg_transfer_amount,
                SUM(amount) as total_transfer_volume
            FROM transfers
        ");
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function collectAPIMetrics() {
        // Mock API metrics
        return [
            'requests_per_minute' => rand(50, 200),
            'avg_response_time_ms' => rand(50, 150),
            'error_rate_percent' => rand(0, 5),
            'active_sessions' => rand(100, 500)
        ];
    }
    
    private function collectBusinessMetrics() {
        $stmt = $this->db->query("
            SELECT 
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                COUNT(CASE WHEN created_at >= DATE('now', '-1 day') THEN 1 END) as daily_transfers
            FROM transfers
        ");
        
        $transferStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'transfer_statistics' => $transferStats,
            'success_rate_percent' => $transferStats['completed_transfers'] > 0 ? 
                round(($transferStats['completed_transfers'] / ($transferStats['completed_transfers'] + $transferStats['failed_transfers'])) * 100, 2) : 0,
            'daily_growth_rate' => rand(-5, 15) // Mock growth rate
        ];
    }
    
    private function determineOverallStatus($components) {
        foreach ($components as $component) {
            if (is_array($component) && isset($component['status'])) {
                if ($component['status'] === 'unhealthy' || $component['status'] === 'critical') {
                    return 'unhealthy';
                }
            }
        }
        
        foreach ($components as $component) {
            if (is_array($component) && isset($component['status'])) {
                if ($component['status'] === 'warning') {
                    return 'warning';
                }
            }
        }
        
        return 'healthy';
    }
    
    private function generateAlerts($healthStatus) {
        $alerts = [];
        
        // Check for critical issues
        if ($healthStatus['overall_status'] === 'unhealthy') {
            $alerts[] = [
                'level' => 'critical',
                'message' => 'System is unhealthy - immediate attention required',
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
        
        // Check database response time
        if (isset($healthStatus['components']['database']['response_time_ms']) && 
            $healthStatus['components']['database']['response_time_ms'] > 1000) {
            $alerts[] = [
                'level' => 'warning',
                'message' => 'Database response time is high: ' . $healthStatus['components']['database']['response_time_ms'] . 'ms',
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
        
        // Check memory usage
        if (isset($healthStatus['components']['application']['memory_usage'])) {
            $memoryUsage = $healthStatus['components']['application']['memory_usage']['current'];
            $memoryLimit = $this->parseMemoryLimit();
            $memoryPercent = ($memoryUsage / $memoryLimit) * 100;
            
            if ($memoryPercent > 90) {
                $alerts[] = [
                    'level' => 'critical',
                    'message' => 'Memory usage is critical: ' . round($memoryPercent, 1) . '%',
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            } elseif ($memoryPercent > 80) {
                $alerts[] = [
                    'level' => 'warning',
                    'message' => 'Memory usage is high: ' . round($memoryPercent, 1) . '%',
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            }
        }
        
        return $alerts;
    }
    
    private function storeHealthCheckResults($healthStatus) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO system_health_checks (
                    overall_status, components_data, metrics_data, alerts_data, created_at
                ) VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $healthStatus['overall_status'],
                json_encode($healthStatus['components']),
                json_encode($healthStatus['metrics']),
                json_encode($healthStatus['alerts']),
                date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            error_log("Failed to store health check results: " . $e->getMessage());
        }
    }
    
    private function getFailedLoginAttempts() {
        try {
            $stmt = $this->db->query("
                SELECT COUNT(*) as failed_attempts 
                FROM login_attempts 
                WHERE success = 0 AND created_at >= DATE('now', '-1 hour')
            ");
            
            return $stmt->fetchColumn() ?: 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    private function getSuspiciousActivities() {
        try {
            $stmt = $this->db->query("
                SELECT COUNT(*) as suspicious_count 
                FROM aml_checks 
                WHERE risk_score >= 50 AND created_at >= DATE('now', '-1 day')
            ");
            
            return $stmt->fetchColumn() ?: 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    private function parseMemoryLimit() {
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }
    
    /**
     * Get system uptime
     */
    public function getSystemUptime() {
        if (function_exists('sys_getloadavg')) {
            $uptime = shell_exec('uptime');
            return $uptime ?: 'Unknown';
        }
        
        return 'Not available on this system';
    }
    
    /**
     * Generate system report
     */
    public function generateSystemReport() {
        $healthCheck = $this->performHealthCheck();
        
        return [
            'report_id' => 'RPT_' . date('YmdHis') . '_' . uniqid(),
            'generated_at' => date('Y-m-d H:i:s'),
            'system_info' => [
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'operating_system' => PHP_OS,
                'uptime' => $this->getSystemUptime()
            ],
            'health_status' => $healthCheck,
            'recommendations' => $this->generateRecommendations($healthCheck)
        ];
    }
    
    private function generateRecommendations($healthCheck) {
        $recommendations = [];
        
        if ($healthCheck['overall_status'] !== 'healthy') {
            $recommendations[] = 'System requires immediate attention - check component statuses';
        }
        
        if (isset($healthCheck['components']['database']['response_time_ms']) && 
            $healthCheck['components']['database']['response_time_ms'] > 500) {
            $recommendations[] = 'Consider optimizing database queries or adding indexes';
        }
        
        if (count($healthCheck['alerts']) > 0) {
            $recommendations[] = 'Address system alerts to improve stability';
        }
        
        if (empty($recommendations)) {
            $recommendations[] = 'System is operating optimally';
        }
        
        return $recommendations;
    }
}
