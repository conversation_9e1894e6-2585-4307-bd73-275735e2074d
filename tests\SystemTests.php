<?php

require_once __DIR__ . '/TestFramework.php';
require_once __DIR__ . '/../bootstrap/simple-app.php';

class SystemTests {
    private $testFramework;
    private $app;
    private $db;
    
    public function __construct() {
        $this->testFramework = new TestFramework();
        $this->db = $this->testFramework->createTestDatabase();
        $this->app = new SimpleApp();
        
        $this->setupTests();
    }
    
    private function setupTests() {
        // Database Tests
        $this->testFramework->addTest('Database Connection', [$this, 'testDatabaseConnection'], 'database');
        $this->testFramework->addTest('User Creation', [$this, 'testUserCreation'], 'database');
        $this->testFramework->addTest('Transfer Creation', [$this, 'testTransferCreation'], 'database');
        
        // API Tests
        $this->testFramework->addTest('API Health Check', [$this, 'testAPIHealthCheck'], 'api');
        $this->testFramework->addTest('User Registration API', [$this, 'testUserRegistrationAPI'], 'api');
        $this->testFramework->addTest('Login API', [$this, 'testLoginAPI'], 'api');
        $this->testFramework->addTest('Transfer Creation API', [$this, 'testTransferCreationAPI'], 'api');
        
        // Security Tests
        $this->testFramework->addTest('SQL Injection Protection', [$this, 'testSQLInjectionProtection'], 'security');
        $this->testFramework->addTest('XSS Protection', [$this, 'testXSSProtection'], 'security');
        $this->testFramework->addTest('CSRF Protection', [$this, 'testCSRFProtection'], 'security');
        $this->testFramework->addTest('Rate Limiting', [$this, 'testRateLimiting'], 'security');
        
        // Business Logic Tests
        $this->testFramework->addTest('Exchange Rate Calculation', [$this, 'testExchangeRateCalculation'], 'business');
        $this->testFramework->addTest('Fee Calculation', [$this, 'testFeeCalculation'], 'business');
        $this->testFramework->addTest('Transfer Limits', [$this, 'testTransferLimits'], 'business');
        
        // Integration Tests
        $this->testFramework->addTest('SMS Service Integration', [$this, 'testSMSServiceIntegration'], 'integration');
        $this->testFramework->addTest('Email Service Integration', [$this, 'testEmailServiceIntegration'], 'integration');
        $this->testFramework->addTest('Payment Gateway Integration', [$this, 'testPaymentGatewayIntegration'], 'integration');
        
        // Performance Tests
        $this->testFramework->addTest('Database Query Performance', [$this, 'testDatabaseQueryPerformance'], 'performance');
        $this->testFramework->addTest('API Response Time', [$this, 'testAPIResponseTime'], 'performance');
        $this->testFramework->addTest('Concurrent Users', [$this, 'testConcurrentUsers'], 'performance');
        
        // Compliance Tests
        $this->testFramework->addTest('AML Check Functionality', [$this, 'testAMLCheckFunctionality'], 'compliance');
        $this->testFramework->addTest('KYC Verification', [$this, 'testKYCVerification'], 'compliance');
        $this->testFramework->addTest('Transaction Reporting', [$this, 'testTransactionReporting'], 'compliance');
    }
    
    // Database Tests
    public function testDatabaseConnection() {
        try {
            $this->db->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            return "Database connection failed: " . $e->getMessage();
        }
    }
    
    public function testUserCreation() {
        try {
            $userId = $this->testFramework->generateTestUser([
                'email' => 'test_user_' . uniqid() . '@example.com'
            ]);
            
            $this->testFramework->assertNotNull($userId);
            $this->testFramework->assertTrue($userId > 0);
            
            return true;
        } catch (Exception $e) {
            return "User creation failed: " . $e->getMessage();
        }
    }
    
    public function testTransferCreation() {
        try {
            // Create test user first
            $userId = $this->testFramework->generateTestUser([
                'email' => 'transfer_test_' . uniqid() . '@example.com'
            ]);
            
            $transferId = $this->testFramework->generateTestTransfer([
                'sender_id' => $userId,
                'transfer_code' => 'TEST_' . uniqid()
            ]);
            
            $this->testFramework->assertNotNull($transferId);
            $this->testFramework->assertTrue($transferId > 0);
            
            return true;
        } catch (Exception $e) {
            return "Transfer creation failed: " . $e->getMessage();
        }
    }
    
    // API Tests
    public function testAPIHealthCheck() {
        try {
            $this->testFramework->mockRequest('GET', '/health');
            
            ob_start();
            // Simulate health check endpoint
            echo "healthy";
            $response = ob_get_clean();
            
            $this->testFramework->assertEquals('healthy', $response);
            
            return true;
        } catch (Exception $e) {
            return "API health check failed: " . $e->getMessage();
        }
    }
    
    public function testUserRegistrationAPI() {
        try {
            $userData = [
                'name' => 'Test User API',
                'email' => 'api_test_' . uniqid() . '@example.com',
                'phone' => '+966501234567',
                'password' => 'password123',
                'confirm_password' => 'password123'
            ];
            
            $this->testFramework->mockRequest('POST', '/register', $userData);
            
            // Simulate registration logic
            $this->testFramework->assertTrue(strlen($userData['email']) > 0);
            $this->testFramework->assertTrue(strlen($userData['password']) >= 8);
            
            return true;
        } catch (Exception $e) {
            return "User registration API failed: " . $e->getMessage();
        }
    }
    
    public function testLoginAPI() {
        try {
            // Create test user first
            $email = 'login_test_' . uniqid() . '@example.com';
            $password = 'password123';
            
            $this->testFramework->generateTestUser([
                'email' => $email,
                'password' => password_hash($password, PASSWORD_DEFAULT)
            ]);
            
            $loginData = [
                'email' => $email,
                'password' => $password
            ];
            
            $this->testFramework->mockRequest('POST', '/login', $loginData);
            
            // Simulate login validation
            $this->testFramework->assertTrue(filter_var($email, FILTER_VALIDATE_EMAIL) !== false);
            $this->testFramework->assertTrue(strlen($password) > 0);
            
            return true;
        } catch (Exception $e) {
            return "Login API failed: " . $e->getMessage();
        }
    }
    
    public function testTransferCreationAPI() {
        try {
            $transferData = [
                'receiver_name' => 'Test Receiver',
                'receiver_phone' => '+966501234568',
                'amount' => 1000,
                'sender_country' => '1',
                'receiver_country' => '2'
            ];
            
            $this->testFramework->mockRequest('POST', '/api/create-transfer', $transferData);
            
            // Validate transfer data
            $this->testFramework->assertTrue($transferData['amount'] > 0);
            $this->testFramework->assertTrue(strlen($transferData['receiver_name']) > 0);
            $this->testFramework->assertTrue(strlen($transferData['receiver_phone']) > 0);
            
            return true;
        } catch (Exception $e) {
            return "Transfer creation API failed: " . $e->getMessage();
        }
    }
    
    // Security Tests
    public function testSQLInjectionProtection() {
        try {
            $maliciousInput = "'; DROP TABLE users; --";
            
            // Test that malicious input doesn't break the system
            $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$maliciousInput]);
            
            // If we reach here, prepared statements are working
            return true;
        } catch (Exception $e) {
            return "SQL injection protection failed: " . $e->getMessage();
        }
    }
    
    public function testXSSProtection() {
        try {
            $maliciousScript = "<script>alert('XSS')</script>";
            $sanitized = htmlspecialchars($maliciousScript, ENT_QUOTES, 'UTF-8');
            
            $this->testFramework->assertNotEquals($maliciousScript, $sanitized);
            $this->testFramework->assertContains('&lt;script&gt;', $sanitized);
            
            return true;
        } catch (Exception $e) {
            return "XSS protection failed: " . $e->getMessage();
        }
    }
    
    public function testCSRFProtection() {
        try {
            // Simulate CSRF token generation
            $token = bin2hex(random_bytes(32));
            
            $this->testFramework->assertTrue(strlen($token) === 64);
            $this->testFramework->assertTrue(ctype_xdigit($token));
            
            return true;
        } catch (Exception $e) {
            return "CSRF protection failed: " . $e->getMessage();
        }
    }
    
    public function testRateLimiting() {
        try {
            // Simulate rate limiting logic
            $requests = [];
            $currentTime = time();
            
            // Add 10 requests in the last minute
            for ($i = 0; $i < 10; $i++) {
                $requests[] = $currentTime - $i;
            }
            
            // Count requests in last minute
            $recentRequests = array_filter($requests, function($timestamp) use ($currentTime) {
                return ($currentTime - $timestamp) < 60;
            });
            
            $this->testFramework->assertTrue(count($recentRequests) <= 60); // Max 60 per minute
            
            return true;
        } catch (Exception $e) {
            return "Rate limiting failed: " . $e->getMessage();
        }
    }
    
    // Business Logic Tests
    public function testExchangeRateCalculation() {
        try {
            $amount = 1000;
            $rate = 3.75; // USD to SAR
            $converted = $amount * $rate;
            
            $this->testFramework->assertEquals(3750, $converted);
            
            return true;
        } catch (Exception $e) {
            return "Exchange rate calculation failed: " . $e->getMessage();
        }
    }
    
    public function testFeeCalculation() {
        try {
            $amount = 1000;
            $percentageFee = 0.025; // 2.5%
            $fixedFee = 5.00;
            
            $totalFee = ($amount * $percentageFee) + $fixedFee;
            $expectedFee = 30.00; // (1000 * 0.025) + 5
            
            $this->testFramework->assertEquals($expectedFee, $totalFee);
            
            return true;
        } catch (Exception $e) {
            return "Fee calculation failed: " . $e->getMessage();
        }
    }
    
    public function testTransferLimits() {
        try {
            $minAmount = 1;
            $maxAmount = 50000;
            $testAmount = 25000;
            
            $this->testFramework->assertTrue($testAmount >= $minAmount);
            $this->testFramework->assertTrue($testAmount <= $maxAmount);
            
            return true;
        } catch (Exception $e) {
            return "Transfer limits test failed: " . $e->getMessage();
        }
    }
    
    // Integration Tests
    public function testSMSServiceIntegration() {
        try {
            // Mock SMS service response
            $smsResponse = [
                'success' => true,
                'message_id' => 'SMS_' . uniqid(),
                'provider' => 'test'
            ];
            
            $this->testFramework->assertTrue($smsResponse['success']);
            $this->testFramework->assertNotNull($smsResponse['message_id']);
            
            return true;
        } catch (Exception $e) {
            return "SMS service integration failed: " . $e->getMessage();
        }
    }
    
    public function testEmailServiceIntegration() {
        try {
            // Mock email service response
            $emailResponse = [
                'success' => true,
                'message_id' => 'EMAIL_' . uniqid(),
                'provider' => 'test'
            ];
            
            $this->testFramework->assertTrue($emailResponse['success']);
            $this->testFramework->assertNotNull($emailResponse['message_id']);
            
            return true;
        } catch (Exception $e) {
            return "Email service integration failed: " . $e->getMessage();
        }
    }
    
    public function testPaymentGatewayIntegration() {
        try {
            // Mock payment gateway response
            $paymentResponse = [
                'success' => true,
                'payment_id' => 'PAY_' . uniqid(),
                'status' => 'completed'
            ];
            
            $this->testFramework->assertTrue($paymentResponse['success']);
            $this->testFramework->assertEquals('completed', $paymentResponse['status']);
            
            return true;
        } catch (Exception $e) {
            return "Payment gateway integration failed: " . $e->getMessage();
        }
    }
    
    // Performance Tests
    public function testDatabaseQueryPerformance() {
        try {
            $benchmark = $this->testFramework->benchmarkFunction(function() {
                $this->db->query("SELECT COUNT(*) FROM users");
            }, 100);
            
            // Query should complete in less than 10ms on average
            $this->testFramework->assertTrue($benchmark['avg_time'] < 0.01);
            
            return true;
        } catch (Exception $e) {
            return "Database query performance failed: " . $e->getMessage();
        }
    }
    
    public function testAPIResponseTime() {
        try {
            $startTime = microtime(true);
            
            // Simulate API call
            $this->testFramework->mockRequest('GET', '/api/health');
            usleep(1000); // 1ms delay
            
            $endTime = microtime(true);
            $responseTime = $endTime - $startTime;
            
            // API should respond in less than 100ms
            $this->testFramework->assertTrue($responseTime < 0.1);
            
            return true;
        } catch (Exception $e) {
            return "API response time test failed: " . $e->getMessage();
        }
    }
    
    public function testConcurrentUsers() {
        try {
            // Simulate concurrent user handling
            $maxConcurrentUsers = 1000;
            $currentUsers = 500;
            
            $this->testFramework->assertTrue($currentUsers <= $maxConcurrentUsers);
            
            return true;
        } catch (Exception $e) {
            return "Concurrent users test failed: " . $e->getMessage();
        }
    }
    
    // Compliance Tests
    public function testAMLCheckFunctionality() {
        try {
            $transferAmount = 15000; // Above AML threshold
            $amlThreshold = 10000;
            
            $requiresAMLCheck = $transferAmount >= $amlThreshold;
            
            $this->testFramework->assertTrue($requiresAMLCheck);
            
            return true;
        } catch (Exception $e) {
            return "AML check functionality failed: " . $e->getMessage();
        }
    }
    
    public function testKYCVerification() {
        try {
            $kycLevels = ['none', 'basic', 'enhanced', 'full'];
            $userKYCLevel = 'basic';
            $requiredLevel = 'basic';
            
            $kycSufficient = array_search($userKYCLevel, $kycLevels) >= array_search($requiredLevel, $kycLevels);
            
            $this->testFramework->assertTrue($kycSufficient);
            
            return true;
        } catch (Exception $e) {
            return "KYC verification failed: " . $e->getMessage();
        }
    }
    
    public function testTransactionReporting() {
        try {
            $reportId = 'CTR' . date('Ymd') . uniqid();
            
            $this->testFramework->assertTrue(strlen($reportId) > 10);
            $this->testFramework->assertContains('CTR', $reportId);
            
            return true;
        } catch (Exception $e) {
            return "Transaction reporting failed: " . $e->getMessage();
        }
    }
    
    public function runAllTests() {
        return $this->testFramework->runTests();
    }
    
    public function runTestGroup($group) {
        return $this->testFramework->runTests($group);
    }
    
    public function exportResults($format = 'json') {
        return $this->testFramework->exportResults($format);
    }
}
