<?php

class ExchangeRateService {
    private $providers = [];
    private $defaultProvider = 'fixer';
    private $config;
    private $cacheService;
    private $db;
    
    public function __construct($config = [], $cacheService = null, $db = null) {
        $this->config = $config;
        $this->cacheService = $cacheService;
        $this->db = $db;
        $this->initializeProviders();
    }
    
    private function initializeProviders() {
        // Fixer.io Provider
        $this->providers['fixer'] = [
            'class' => 'FixerExchangeProvider',
            'config' => [
                'api_key' => $this->config['fixer']['api_key'] ?? '',
                'base_url' => 'http://data.fixer.io/api/'
            ]
        ];
        
        // ExchangeRate-API Provider
        $this->providers['exchangerate_api'] = [
            'class' => 'ExchangeRateAPIProvider',
            'config' => [
                'api_key' => $this->config['exchangerate_api']['api_key'] ?? '',
                'base_url' => 'https://v6.exchangerate-api.com/v6/'
            ]
        ];
        
        // CurrencyLayer Provider
        $this->providers['currencylayer'] = [
            'class' => 'CurrencyLayerProvider',
            'config' => [
                'api_key' => $this->config['currencylayer']['api_key'] ?? '',
                'base_url' => 'http://api.currencylayer.com/'
            ]
        ];
        
        // Central Bank Provider (for local rates)
        $this->providers['central_bank'] = [
            'class' => 'CentralBankProvider',
            'config' => [
                'sama_url' => 'https://www.sama.gov.sa/en-us/EconomicReports/Pages/ExchangeRate.aspx'
            ]
        ];
        
        // Fallback Provider (static rates)
        $this->providers['fallback'] = [
            'class' => 'FallbackExchangeProvider',
            'config' => []
        ];
    }
    
    /**
     * Get exchange rate between two currencies
     */
    public function getExchangeRate($fromCurrency, $toCurrency, $amount = 1, $provider = null) {
        $provider = $provider ?: $this->defaultProvider;
        
        // Check cache first
        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}_{$provider}";
        if ($this->cacheService) {
            $cachedRate = $this->cacheService->get($cacheKey);
            if ($cachedRate !== null) {
                return [
                    'success' => true,
                    'from_currency' => $fromCurrency,
                    'to_currency' => $toCurrency,
                    'rate' => $cachedRate['rate'],
                    'amount' => $amount,
                    'converted_amount' => $amount * $cachedRate['rate'],
                    'provider' => $provider,
                    'cached' => true,
                    'last_updated' => $cachedRate['last_updated']
                ];
            }
        }
        
        try {
            $providerInstance = $this->createProvider($provider);
            $result = $providerInstance->getRate($fromCurrency, $toCurrency);
            
            if ($result['success']) {
                // Cache the result for 5 minutes
                if ($this->cacheService) {
                    $this->cacheService->put($cacheKey, [
                        'rate' => $result['rate'],
                        'last_updated' => date('Y-m-d H:i:s')
                    ], 300);
                }
                
                // Store in database for historical tracking
                $this->storeExchangeRate($fromCurrency, $toCurrency, $result['rate'], $provider);
                
                return [
                    'success' => true,
                    'from_currency' => $fromCurrency,
                    'to_currency' => $toCurrency,
                    'rate' => $result['rate'],
                    'amount' => $amount,
                    'converted_amount' => $amount * $result['rate'],
                    'provider' => $provider,
                    'cached' => false,
                    'last_updated' => date('Y-m-d H:i:s')
                ];
            }
            
            throw new Exception($result['error'] ?? 'Failed to get exchange rate');
            
        } catch (Exception $e) {
            // Try fallback providers
            return $this->getExchangeRateWithFallback($fromCurrency, $toCurrency, $amount, $provider);
        }
    }
    
    private function getExchangeRateWithFallback($fromCurrency, $toCurrency, $amount, $failedProvider) {
        $availableProviders = array_keys($this->providers);
        $remainingProviders = array_diff($availableProviders, [$failedProvider]);
        
        foreach ($remainingProviders as $provider) {
            try {
                $providerInstance = $this->createProvider($provider);
                $result = $providerInstance->getRate($fromCurrency, $toCurrency);
                
                if ($result['success']) {
                    return [
                        'success' => true,
                        'from_currency' => $fromCurrency,
                        'to_currency' => $toCurrency,
                        'rate' => $result['rate'],
                        'amount' => $amount,
                        'converted_amount' => $amount * $result['rate'],
                        'provider' => $provider,
                        'fallback_used' => true,
                        'original_provider' => $failedProvider,
                        'last_updated' => date('Y-m-d H:i:s')
                    ];
                }
            } catch (Exception $e) {
                continue;
            }
        }
        
        // If all providers fail, use historical data
        return $this->getHistoricalRate($fromCurrency, $toCurrency, $amount);
    }
    
    private function createProvider($provider) {
        if (!isset($this->providers[$provider])) {
            throw new Exception("Exchange rate provider '{$provider}' not found");
        }
        
        $providerConfig = $this->providers[$provider];
        $className = $providerConfig['class'];
        
        switch ($className) {
            case 'FixerExchangeProvider':
                return new FixerExchangeProvider($providerConfig['config']);
            case 'ExchangeRateAPIProvider':
                return new ExchangeRateAPIProvider($providerConfig['config']);
            case 'CurrencyLayerProvider':
                return new CurrencyLayerProvider($providerConfig['config']);
            case 'CentralBankProvider':
                return new CentralBankProvider($providerConfig['config']);
            case 'FallbackExchangeProvider':
            default:
                return new FallbackExchangeProvider($providerConfig['config']);
        }
    }
    
    private function storeExchangeRate($fromCurrency, $toCurrency, $rate, $provider) {
        if (!$this->db) return;
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO exchange_rates (from_currency, to_currency, rate, provider, created_at) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$fromCurrency, $toCurrency, $rate, $provider, date('Y-m-d H:i:s')]);
        } catch (Exception $e) {
            error_log("Failed to store exchange rate: " . $e->getMessage());
        }
    }
    
    private function getHistoricalRate($fromCurrency, $toCurrency, $amount) {
        if (!$this->db) {
            throw new Exception("No exchange rate available and no database connection");
        }
        
        try {
            $stmt = $this->db->prepare("
                SELECT rate, created_at FROM exchange_rates 
                WHERE from_currency = ? AND to_currency = ? 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$fromCurrency, $toCurrency]);
            $historical = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($historical) {
                return [
                    'success' => true,
                    'from_currency' => $fromCurrency,
                    'to_currency' => $toCurrency,
                    'rate' => $historical['rate'],
                    'amount' => $amount,
                    'converted_amount' => $amount * $historical['rate'],
                    'provider' => 'historical',
                    'historical' => true,
                    'last_updated' => $historical['created_at'],
                    'warning' => 'Using historical exchange rate due to API unavailability'
                ];
            }
            
            throw new Exception("No historical exchange rate found");
            
        } catch (Exception $e) {
            throw new Exception("Failed to get exchange rate: " . $e->getMessage());
        }
    }
    
    /**
     * Get multiple exchange rates at once
     */
    public function getMultipleRates($baseCurrency, $targetCurrencies, $provider = null) {
        $rates = [];
        
        foreach ($targetCurrencies as $targetCurrency) {
            try {
                $result = $this->getExchangeRate($baseCurrency, $targetCurrency, 1, $provider);
                $rates[$targetCurrency] = $result;
            } catch (Exception $e) {
                $rates[$targetCurrency] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $rates;
    }
    
    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies($provider = null) {
        $provider = $provider ?: $this->defaultProvider;
        
        try {
            $providerInstance = $this->createProvider($provider);
            return $providerInstance->getSupportedCurrencies();
        } catch (Exception $e) {
            // Return default supported currencies
            return [
                'USD', 'EUR', 'GBP', 'SAR', 'AED', 'EGP', 'KWD', 'JOD', 'QAR', 'BHD'
            ];
        }
    }
    
    /**
     * Calculate transfer fees based on amount and corridor
     */
    public function calculateTransferFees($amount, $fromCountry, $toCountry, $fromCurrency, $toCurrency) {
        // Get base fee structure
        $baseFee = $this->getBaseFee($fromCountry, $toCountry);
        $percentageFee = $this->getPercentageFee($fromCountry, $toCountry);
        
        // Calculate fees
        $fixedFee = $baseFee;
        $variableFee = $amount * ($percentageFee / 100);
        $totalFee = $fixedFee + $variableFee;
        
        // Apply currency conversion if needed
        if ($fromCurrency !== 'USD') {
            $conversionResult = $this->getExchangeRate($fromCurrency, 'USD', $totalFee);
            $totalFeeUSD = $conversionResult['converted_amount'];
        } else {
            $totalFeeUSD = $totalFee;
        }
        
        return [
            'fixed_fee' => $fixedFee,
            'variable_fee' => $variableFee,
            'total_fee' => $totalFee,
            'total_fee_usd' => $totalFeeUSD,
            'percentage' => $percentageFee,
            'currency' => $fromCurrency
        ];
    }
    
    private function getBaseFee($fromCountry, $toCountry) {
        // Fee structure based on corridor
        $feeMatrix = [
            'US-SA' => 5.00,
            'US-AE' => 5.00,
            'US-EG' => 3.00,
            'SA-EG' => 2.00,
            'AE-EG' => 2.00,
            'default' => 4.00
        ];
        
        $corridor = $fromCountry . '-' . $toCountry;
        return $feeMatrix[$corridor] ?? $feeMatrix['default'];
    }
    
    private function getPercentageFee($fromCountry, $toCountry) {
        // Percentage fee structure
        $percentageMatrix = [
            'US-SA' => 1.5,
            'US-AE' => 1.5,
            'US-EG' => 2.0,
            'SA-EG' => 1.0,
            'AE-EG' => 1.0,
            'default' => 1.75
        ];
        
        $corridor = $fromCountry . '-' . $toCountry;
        return $percentageMatrix[$corridor] ?? $percentageMatrix['default'];
    }
}

// Fixer.io Exchange Rate Provider
class FixerExchangeProvider {
    private $apiKey;
    private $baseUrl;
    
    public function __construct($config) {
        $this->apiKey = $config['api_key'];
        $this->baseUrl = $config['base_url'];
    }
    
    public function getRate($fromCurrency, $toCurrency) {
        if (empty($this->apiKey)) {
            throw new Exception("Fixer.io API key not configured");
        }
        
        $url = $this->baseUrl . "latest?access_key={$this->apiKey}&base={$fromCurrency}&symbols={$toCurrency}";
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if (!$data['success']) {
            throw new Exception($data['error']['info'] ?? 'Fixer.io API error');
        }
        
        return [
            'success' => true,
            'rate' => $data['rates'][$toCurrency],
            'provider' => 'fixer'
        ];
    }
    
    public function getSupportedCurrencies() {
        if (empty($this->apiKey)) {
            throw new Exception("Fixer.io API key not configured");
        }
        
        $url = $this->baseUrl . "symbols?access_key={$this->apiKey}";
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if (!$data['success']) {
            throw new Exception($data['error']['info'] ?? 'Fixer.io API error');
        }
        
        return array_keys($data['symbols']);
    }
}

// ExchangeRate-API Provider
class ExchangeRateAPIProvider {
    private $apiKey;
    private $baseUrl;
    
    public function __construct($config) {
        $this->apiKey = $config['api_key'];
        $this->baseUrl = $config['base_url'];
    }
    
    public function getRate($fromCurrency, $toCurrency) {
        if (empty($this->apiKey)) {
            throw new Exception("ExchangeRate-API key not configured");
        }
        
        $url = $this->baseUrl . "{$this->apiKey}/pair/{$fromCurrency}/{$toCurrency}";
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if ($data['result'] !== 'success') {
            throw new Exception($data['error-type'] ?? 'ExchangeRate-API error');
        }
        
        return [
            'success' => true,
            'rate' => $data['conversion_rate'],
            'provider' => 'exchangerate_api'
        ];
    }
    
    public function getSupportedCurrencies() {
        if (empty($this->apiKey)) {
            throw new Exception("ExchangeRate-API key not configured");
        }
        
        $url = $this->baseUrl . "{$this->apiKey}/codes";
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if ($data['result'] !== 'success') {
            throw new Exception($data['error-type'] ?? 'ExchangeRate-API error');
        }
        
        return array_column($data['supported_codes'], 0);
    }
}

// CurrencyLayer Provider
class CurrencyLayerProvider {
    private $apiKey;
    private $baseUrl;
    
    public function __construct($config) {
        $this->apiKey = $config['api_key'];
        $this->baseUrl = $config['base_url'];
    }
    
    public function getRate($fromCurrency, $toCurrency) {
        if (empty($this->apiKey)) {
            throw new Exception("CurrencyLayer API key not configured");
        }
        
        $url = $this->baseUrl . "live?access_key={$this->apiKey}&currencies={$toCurrency}&source={$fromCurrency}";
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if (!$data['success']) {
            throw new Exception($data['error']['info'] ?? 'CurrencyLayer API error');
        }
        
        $rateKey = $fromCurrency . $toCurrency;
        
        return [
            'success' => true,
            'rate' => $data['quotes'][$rateKey],
            'provider' => 'currencylayer'
        ];
    }
    
    public function getSupportedCurrencies() {
        return ['USD', 'EUR', 'GBP', 'SAR', 'AED', 'EGP', 'KWD', 'JOD'];
    }
}

// Central Bank Provider (for local rates)
class CentralBankProvider {
    private $config;
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    public function getRate($fromCurrency, $toCurrency) {
        // This would scrape central bank websites for official rates
        // For now, return mock data
        
        $rates = [
            'USD-SAR' => 3.75,
            'EUR-SAR' => 4.10,
            'GBP-SAR' => 4.65,
            'SAR-USD' => 0.2667,
            'SAR-EUR' => 0.2439,
            'SAR-GBP' => 0.2151
        ];
        
        $rateKey = $fromCurrency . '-' . $toCurrency;
        
        if (isset($rates[$rateKey])) {
            return [
                'success' => true,
                'rate' => $rates[$rateKey],
                'provider' => 'central_bank'
            ];
        }
        
        throw new Exception("Rate not available from central bank");
    }
    
    public function getSupportedCurrencies() {
        return ['USD', 'EUR', 'GBP', 'SAR'];
    }
}

// Fallback Exchange Provider (static rates)
class FallbackExchangeProvider {
    public function __construct($config) {
        // No configuration needed
    }
    
    public function getRate($fromCurrency, $toCurrency) {
        // Static fallback rates
        $rates = [
            'USD-SAR' => 3.75,
            'USD-AED' => 3.67,
            'USD-EGP' => 30.90,
            'USD-KWD' => 0.30,
            'USD-JOD' => 0.71,
            'SAR-AED' => 0.98,
            'SAR-EGP' => 8.24,
            'AED-EGP' => 8.42,
            'EUR-USD' => 1.08,
            'GBP-USD' => 1.24
        ];
        
        $rateKey = $fromCurrency . '-' . $toCurrency;
        
        if (isset($rates[$rateKey])) {
            return [
                'success' => true,
                'rate' => $rates[$rateKey],
                'provider' => 'fallback'
            ];
        }
        
        // Try reverse rate
        $reverseKey = $toCurrency . '-' . $fromCurrency;
        if (isset($rates[$reverseKey])) {
            return [
                'success' => true,
                'rate' => 1 / $rates[$reverseKey],
                'provider' => 'fallback'
            ];
        }
        
        throw new Exception("Exchange rate not available");
    }
    
    public function getSupportedCurrencies() {
        return ['USD', 'EUR', 'GBP', 'SAR', 'AED', 'EGP', 'KWD', 'JOD'];
    }
}
