@echo off
echo ========================================
echo Elite Financial Transfer System v6.0
echo ========================================
echo.

echo [1/7] Checking PHP version...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo [2/7] Checking Composer...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo [3/7] Installing PHP dependencies...
composer install --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ERROR: Failed to install PHP dependencies
    pause
    exit /b 1
)

echo.
echo [4/7] Setting up environment...
if not exist .env (
    copy .env.example .env
    echo Environment file created
) else (
    echo Environment file already exists
)

echo.
echo [5/7] Generating application key...
php artisan key:generate --force

echo.
echo [6/7] Setting up database...
if not exist database\database.sqlite (
    echo. > database\database.sqlite
    echo SQLite database file created
)

php artisan migrate --force
if %errorlevel% neq 0 (
    echo ERROR: Database migration failed
    pause
    exit /b 1
)

echo.
echo [7/7] Seeding database with sample data...
php artisan db:seed --force
if %errorlevel% neq 0 (
    echo WARNING: Database seeding failed, but system can still work
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Default login credentials:
echo.
echo Super Admin:
echo   Email: <EMAIL>
echo   Password: password123
echo.
echo Customer:
echo   Email: <EMAIL>
echo   Password: password123
echo.
echo Agent:
echo   Email: <EMAIL>
echo   Password: password123
echo.
echo ========================================
echo To start the application, run:
echo   php artisan serve
echo.
echo Then open your browser and go to:
echo   http://localhost:8000
echo ========================================
echo.
pause
