<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مستخدم';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال أموال - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .step {
            display: none;
        }
        
        .step.active {
            display: block;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step-indicator .step-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .step-indicator .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 10px;
        }
        
        .step-indicator .step-item.active .step-number {
            background: #2563eb;
            color: white;
        }
        
        .step-indicator .step-item.completed .step-number {
            background: #059669;
            color: white;
        }
        
        .calculation-card {
            position: sticky;
            top: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <span class="navbar-text text-white">
                    <i class="bi bi-person-circle me-1"></i>
                    <?= $userName ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-send me-2"></i>
                            إرسال أموال
                        </h2>
                        <p class="text-white-50 mb-0">أرسل الأموال عبر العالم بسرعة وأمان</p>
                    </div>
                    <a href="/dashboard" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Transfer Form -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step-item active" id="indicator-1">
                                <div class="step-number">1</div>
                                <span>تفاصيل التحويل</span>
                            </div>
                            <div class="step-item" id="indicator-2">
                                <div class="step-number">2</div>
                                <span>معلومات المستلم</span>
                            </div>
                            <div class="step-item" id="indicator-3">
                                <div class="step-number">3</div>
                                <span>مراجعة وتأكيد</span>
                            </div>
                        </div>

                        <form id="transferForm">
                            <!-- Step 1: Transfer Details -->
                            <div class="step active" id="step-1">
                                <h4 class="mb-4">تفاصيل التحويل</h4>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sender_country" class="form-label">
                                                <i class="bi bi-geo-alt me-1"></i>
                                                الإرسال من
                                            </label>
                                            <select class="form-select" id="sender_country" name="sender_country" required>
                                                <option value="">اختر الدولة</option>
                                                <option value="1">السعودية</option>
                                                <option value="2">الإمارات</option>
                                                <option value="3">مصر</option>
                                                <option value="4">الكويت</option>
                                                <option value="6">الولايات المتحدة</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="receiver_country" class="form-label">
                                                <i class="bi bi-geo-alt-fill me-1"></i>
                                                الإرسال إلى
                                            </label>
                                            <select class="form-select" id="receiver_country" name="receiver_country" required>
                                                <option value="">اختر الدولة</option>
                                                <option value="1">السعودية</option>
                                                <option value="2">الإمارات</option>
                                                <option value="3">مصر</option>
                                                <option value="4">الكويت</option>
                                                <option value="5">الأردن</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">
                                                <i class="bi bi-cash me-1"></i>
                                                المبلغ المرسل
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="amount" 
                                                   name="amount" 
                                                   min="1" 
                                                   step="0.01" 
                                                   required
                                                   placeholder="0.00">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="purpose" class="form-label">
                                                <i class="bi bi-info-circle me-1"></i>
                                                الغرض من التحويل
                                            </label>
                                            <select class="form-select" id="purpose" name="purpose">
                                                <option value="">اختر الغرض</option>
                                                <option value="family_support">دعم العائلة</option>
                                                <option value="education">تعليم</option>
                                                <option value="medical">طبي</option>
                                                <option value="business">تجاري</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                                        التالي: معلومات المستلم
                                        <i class="bi bi-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Step 2: Receiver Information -->
                            <div class="step" id="step-2">
                                <h4 class="mb-4">معلومات المستلم</h4>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="receiver_name" class="form-label">
                                                <i class="bi bi-person me-1"></i>
                                                الاسم الكامل
                                            </label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="receiver_name" 
                                                   name="receiver_name" 
                                                   required
                                                   placeholder="أدخل الاسم الكامل للمستلم">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="receiver_phone" class="form-label">
                                                <i class="bi bi-phone me-1"></i>
                                                رقم الهاتف
                                            </label>
                                            <input type="tel" 
                                                   class="form-control" 
                                                   id="receiver_phone" 
                                                   name="receiver_phone" 
                                                   required
                                                   placeholder="أدخل رقم الهاتف">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="receiver_address" class="form-label">
                                        <i class="bi bi-geo-alt me-1"></i>
                                        العنوان
                                    </label>
                                    <textarea class="form-control" 
                                              id="receiver_address" 
                                              name="receiver_address" 
                                              rows="3"
                                              placeholder="أدخل عنوان المستلم"></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="bi bi-chat-text me-1"></i>
                                        ملاحظات (اختياري)
                                    </label>
                                    <textarea class="form-control" 
                                              id="notes" 
                                              name="notes" 
                                              rows="3"
                                              placeholder="أي ملاحظات أو تعليمات إضافية"></textarea>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        السابق
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                                        التالي: مراجعة وتأكيد
                                        <i class="bi bi-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Step 3: Review & Confirm -->
                            <div class="step" id="step-3">
                                <h4 class="mb-4">مراجعة وتأكيد</h4>
                                
                                <div id="transferSummary">
                                    <!-- Summary will be populated by JavaScript -->
                                </div>
                                
                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        أوافق على 
                                        <a href="#" class="text-decoration-none">الشروط والأحكام</a>
                                        و
                                        <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                                    </label>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        السابق
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-send me-2"></i>
                                        إرسال الأموال
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Calculation Summary -->
            <div class="col-lg-4">
                <div class="card calculation-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-calculator me-2"></i>
                            ملخص التحويل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="calculationResult" class="d-none">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المبلغ المرسل:</span>
                                <strong id="sendAmount">-</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>رسوم التحويل:</span>
                                <span id="feeAmount">-</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>سعر الصرف:</span>
                                <span id="exchangeRate">-</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <span>المبلغ الإجمالي:</span>
                                <strong class="text-primary" id="totalAmount">-</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>المستلم يحصل على:</span>
                                <strong class="text-success" id="receiveAmount">-</strong>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="estimatedDelivery">-</small>
                            </div>
                        </div>
                        
                        <div id="calculationPlaceholder">
                            <div class="text-center text-muted">
                                <i class="bi bi-calculator fs-1 mb-3"></i>
                                <p>املأ تفاصيل التحويل لرؤية الحساب</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStep = 1;
        
        // Step navigation
        function nextStep(step) {
            if (validateCurrentStep()) {
                // Hide current step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`indicator-${currentStep}`).classList.add('completed');
                document.getElementById(`indicator-${currentStep}`).classList.remove('active');
                
                // Show next step
                document.getElementById(`step-${step}`).classList.add('active');
                document.getElementById(`indicator-${step}`).classList.add('active');
                
                currentStep = step;
                
                if (step === 3) {
                    generateSummary();
                }
                
                calculateFees();
            }
        }
        
        function prevStep(step) {
            // Hide current step
            document.getElementById(`step-${currentStep}`).classList.remove('active');
            document.getElementById(`indicator-${currentStep}`).classList.remove('active');
            document.getElementById(`indicator-${currentStep}`).classList.remove('completed');
            
            // Show previous step
            document.getElementById(`step-${step}`).classList.add('active');
            document.getElementById(`indicator-${step}`).classList.add('active');
            document.getElementById(`indicator-${step}`).classList.remove('completed');
            
            currentStep = step;
        }
        
        function validateCurrentStep() {
            const currentStepElement = document.getElementById(`step-${currentStep}`);
            const requiredFields = currentStepElement.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Auto-calculate fees when form data changes
        function setupAutoCalculation() {
            const fields = ['sender_country', 'receiver_country', 'amount'];
            
            fields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field) {
                    field.addEventListener('change', calculateFees);
                    field.addEventListener('input', debounce(calculateFees, 500));
                }
            });
        }
        
        function calculateFees() {
            const senderCountry = document.getElementById('sender_country').value;
            const receiverCountry = document.getElementById('receiver_country').value;
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            
            if (!senderCountry || !receiverCountry || amount <= 0) {
                document.getElementById('calculationResult').classList.add('d-none');
                document.getElementById('calculationPlaceholder').classList.remove('d-none');
                return;
            }
            
            // Simple calculation (in real system, this would be an API call)
            const feePercentage = 0.025; // 2.5%
            const fixedFee = 5.00;
            const exchangeRate = getExchangeRate(senderCountry, receiverCountry);
            
            const feeAmount = (amount * feePercentage) + fixedFee;
            const totalAmount = amount + feeAmount;
            const convertedAmount = amount * exchangeRate;
            
            updateCalculationDisplay({
                amount: amount.toFixed(2),
                fee_amount: feeAmount.toFixed(2),
                total_amount: totalAmount.toFixed(2),
                converted_amount: convertedAmount.toFixed(2),
                exchange_rate: exchangeRate.toFixed(4),
                sender_currency: getCurrency(senderCountry),
                receiver_currency: getCurrency(receiverCountry),
                estimated_delivery: '24-48 ساعة'
            });
        }
        
        function getExchangeRate(fromCountry, toCountry) {
            // Simplified exchange rates
            const rates = {
                '1-2': 1.02, // SAR to AED
                '1-3': 8.25, // SAR to EGP
                '2-3': 8.08, // AED to EGP
                '6-1': 3.75, // USD to SAR
                '6-2': 3.67, // USD to AED
            };
            
            return rates[`${fromCountry}-${toCountry}`] || 1.0;
        }
        
        function getCurrency(countryId) {
            const currencies = {
                '1': 'SAR',
                '2': 'AED', 
                '3': 'EGP',
                '4': 'KWD',
                '5': 'JOD',
                '6': 'USD'
            };
            
            return currencies[countryId] || 'USD';
        }
        
        function updateCalculationDisplay(data) {
            document.getElementById('sendAmount').textContent = `${data.amount} ${data.sender_currency}`;
            document.getElementById('feeAmount').textContent = `${data.fee_amount} ${data.sender_currency}`;
            document.getElementById('exchangeRate').textContent = `1 ${data.sender_currency} = ${data.exchange_rate} ${data.receiver_currency}`;
            document.getElementById('totalAmount').textContent = `${data.total_amount} ${data.sender_currency}`;
            document.getElementById('receiveAmount').textContent = `${data.converted_amount} ${data.receiver_currency}`;
            document.getElementById('estimatedDelivery').textContent = `التسليم المتوقع: ${data.estimated_delivery}`;
            
            document.getElementById('calculationResult').classList.remove('d-none');
            document.getElementById('calculationPlaceholder').classList.add('d-none');
        }
        
        function generateSummary() {
            const formData = new FormData(document.getElementById('transferForm'));
            
            const summary = `
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">تفاصيل التحويل</h6>
                        <p><strong>من:</strong> ${getCountryName(formData.get('sender_country'))}</p>
                        <p><strong>إلى:</strong> ${getCountryName(formData.get('receiver_country'))}</p>
                        <p><strong>المبلغ:</strong> ${formData.get('amount')} ${getCurrency(formData.get('sender_country'))}</p>
                        <p><strong>الغرض:</strong> ${getPurposeText(formData.get('purpose'))}</p>
                    </div>
                </div>
                
                <div class="card bg-light mt-3">
                    <div class="card-body">
                        <h6 class="card-title">معلومات المستلم</h6>
                        <p><strong>الاسم:</strong> ${formData.get('receiver_name')}</p>
                        <p><strong>الهاتف:</strong> ${formData.get('receiver_phone')}</p>
                        ${formData.get('receiver_address') ? `<p><strong>العنوان:</strong> ${formData.get('receiver_address')}</p>` : ''}
                        ${formData.get('notes') ? `<p><strong>ملاحظات:</strong> ${formData.get('notes')}</p>` : ''}
                    </div>
                </div>
            `;
            
            document.getElementById('transferSummary').innerHTML = summary;
        }
        
        function getCountryName(countryId) {
            const countries = {
                '1': 'السعودية',
                '2': 'الإمارات',
                '3': 'مصر',
                '4': 'الكويت',
                '5': 'الأردن',
                '6': 'الولايات المتحدة'
            };
            
            return countries[countryId] || 'غير محدد';
        }
        
        function getPurposeText(purpose) {
            const purposes = {
                'family_support': 'دعم العائلة',
                'education': 'تعليم',
                'medical': 'طبي',
                'business': 'تجاري',
                'other': 'أخرى'
            };
            
            return purposes[purpose] || 'غير محدد';
        }
        
        // Form submission
        document.getElementById('transferForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!validateCurrentStep()) {
                return;
            }

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإرسال...';

            fetch('/transfers/create', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTransferSuccess(data.data);
                } else {
                    alert(data.message || 'حدث خطأ أثناء إنشاء التحويل');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-send me-2"></i>إرسال الأموال';
            });
        });

        function showTransferSuccess(data) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-check-circle me-2"></i>
                                تم إنشاء التحويل بنجاح
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <div class="alert alert-success">
                                    <h4>رمز التحويل: <strong>${data.transfer_code}</strong></h4>
                                    <p class="mb-0">احفظ هذا الرمز لتتبع التحويل</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>تفاصيل التحويل</h6>
                                    <p><strong>المبلغ:</strong> ${data.amount}</p>
                                    <p><strong>الرسوم:</strong> ${data.fee_amount}</p>
                                    <p><strong>المجموع:</strong> ${data.total_amount}</p>
                                    <p><strong>الحالة:</strong> <span class="badge bg-warning">${data.status}</span></p>
                                </div>
                                <div class="col-md-6">
                                    <h6>معلومات الاستلام</h6>
                                    <p><strong>رمز الاستلام:</strong> ${data.pickup_code}</p>
                                    <p><strong>المستلم يحصل على:</strong> ${data.converted_amount}</p>
                                    <p><strong>التسليم المتوقع:</strong> 24-48 ساعة</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="window.location.href='/track'">
                                تتبع التحويل
                            </button>
                            <button type="button" class="btn btn-success" onclick="window.location.href='/transfers/create'">
                                إرسال تحويل آخر
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }
        
        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupAutoCalculation();
        });
    </script>
</body>
</html>
