#!/usr/bin/env php
<?php

/**
 * Elite Transfer System - Simple Table Creation Script
 */

echo "🚀 Creating Elite Transfer System Tables...\n\n";

// Load environment variables
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$config = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'elite_transfer',
    'username' => $_ENV['DB_USERNAME'] ?? 'elite_user',
    'password' => $_ENV['DB_PASSWORD'] ?? 'elite_password_2025',
    'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4'
];

try {
    // Connect to MySQL
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Connected to database: {$config['database']}\n\n";
    
    // Create tables one by one
    $tables = [
        'countries' => "
            CREATE TABLE IF NOT EXISTS countries (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                code VARCHAR(3) NOT NULL UNIQUE,
                currency VARCHAR(3) NOT NULL,
                flag_url VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                phone VARCHAR(20),
                password VARCHAR(255) NOT NULL,
                role ENUM('customer', 'agent', 'admin') DEFAULT 'customer',
                kyc_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
                kyc_level ENUM('none', 'basic', 'enhanced', 'full') DEFAULT 'none',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'transfers' => "
            CREATE TABLE IF NOT EXISTS transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transfer_code VARCHAR(20) UNIQUE NOT NULL,
                pickup_code VARCHAR(10) NOT NULL,
                sender_id INT NOT NULL,
                sender_name VARCHAR(255) NOT NULL,
                sender_phone VARCHAR(20) NOT NULL,
                sender_country_id INT NOT NULL,
                receiver_name VARCHAR(255) NOT NULL,
                receiver_phone VARCHAR(20) NOT NULL,
                receiver_country_id INT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                converted_amount DECIMAL(15,2) NOT NULL,
                exchange_rate DECIMAL(10,6) NOT NULL,
                fee_amount DECIMAL(15,2) NOT NULL,
                total_amount DECIMAL(15,2) NOT NULL,
                sender_currency VARCHAR(3) NOT NULL,
                receiver_currency VARCHAR(3) NOT NULL,
                status ENUM('pending', 'pending_payment', 'paid', 'processing', 'ready_for_pickup', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
                payment_method ENUM('cash', 'card', 'bank_transfer', 'mobile_wallet') DEFAULT 'cash',
                pickup_method ENUM('cash', 'bank_deposit', 'mobile_wallet') DEFAULT 'cash',
                agent_id INT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (sender_country_id) REFERENCES countries(id),
                FOREIGN KEY (receiver_country_id) REFERENCES countries(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'payments' => "
            CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transfer_id INT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'USD',
                payment_method VARCHAR(50) NOT NULL,
                payment_provider VARCHAR(50),
                transaction_id VARCHAR(255),
                status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
                response_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'exchange_rates' => "
            CREATE TABLE IF NOT EXISTS exchange_rates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                from_currency VARCHAR(3) NOT NULL,
                to_currency VARCHAR(3) NOT NULL,
                rate DECIMAL(15,6) NOT NULL,
                provider VARCHAR(50) DEFAULT 'fallback',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'notifications' => "
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                transfer_id INT NULL,
                type ENUM('sms', 'email', 'push') NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
                sent_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'system_settings' => "
            CREATE TABLE IF NOT EXISTS system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'aml_checks' => "
            CREATE TABLE IF NOT EXISTS aml_checks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transfer_id INT NULL,
                user_id INT NOT NULL,
                risk_score INT DEFAULT 0,
                action ENUM('approve', 'review', 'block', 'monitor') NOT NULL,
                checks_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'kyc_documents' => "
            CREATE TABLE IF NOT EXISTS kyc_documents (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                document_type ENUM('id_document', 'proof_of_address', 'proof_of_income', 'bank_statement') NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    // Create each table
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            echo "✅ Created table: {$tableName}\n";
        } catch (PDOException $e) {
            echo "⚠️  Table {$tableName}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📊 Inserting initial data...\n";
    
    // Insert countries
    $countries = [
        ['Saudi Arabia', 'SA', 'SAR'],
        ['United Arab Emirates', 'AE', 'AED'],
        ['Egypt', 'EG', 'EGP'],
        ['Kuwait', 'KW', 'KWD'],
        ['Jordan', 'JO', 'JOD'],
        ['United States', 'US', 'USD']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO countries (name, code, currency) VALUES (?, ?, ?)");
    foreach ($countries as $country) {
        $stmt->execute($country);
    }
    echo "✅ Inserted countries data\n";
    
    // Insert admin user
    $adminPassword = password_hash('password', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (name, email, password, role, kyc_status, kyc_level) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['System Administrator', '<EMAIL>', $adminPassword, 'admin', 'verified', 'full']);
    echo "✅ Created admin user: <EMAIL> / password\n";
    
    // Insert system settings
    $settings = [
        ['app_name', 'Elite Transfer System', 'Application name'],
        ['max_transfer_amount', '50000', 'Maximum transfer amount'],
        ['min_transfer_amount', '1', 'Minimum transfer amount'],
        ['default_fee_percentage', '2.5', 'Default fee percentage'],
        ['default_fee_fixed', '5.00', 'Default fixed fee']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "✅ Inserted system settings\n";
    
    // Insert exchange rates
    $rates = [
        ['USD', 'SAR', 3.7500],
        ['USD', 'AED', 3.6725],
        ['USD', 'EGP', 30.9000],
        ['USD', 'KWD', 0.3000],
        ['USD', 'JOD', 0.7090]
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO exchange_rates (from_currency, to_currency, rate) VALUES (?, ?, ?)");
    foreach ($rates as $rate) {
        $stmt->execute($rate);
    }
    echo "✅ Inserted exchange rates\n";
    
    // Verify setup
    echo "\n📋 Verifying database setup...\n";
    
    $tableCount = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{$config['database']}'")->fetchColumn();
    echo "✅ Total tables created: {$tableCount}\n";
    
    $countryCount = $pdo->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    echo "✅ Countries: {$countryCount}\n";
    
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "✅ Users: {$userCount}\n";
    
    $settingsCount = $pdo->query("SELECT COUNT(*) FROM system_settings")->fetchColumn();
    echo "✅ Settings: {$settingsCount}\n";
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "🌐 phpMyAdmin: http://localhost/phpmyadmin\n";
    echo "👤 Username: {$config['username']}\n";
    echo "🔑 Password: {$config['password']}\n";
    echo "🗄️  Database: {$config['database']}\n";
    echo "\n🚀 Start the application: php -S localhost:8000 -t public\n";
    echo "🌐 Visit: http://localhost:8000\n";
    echo "👤 Admin login: <EMAIL> / password\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure MySQL/XAMPP is running and accessible\n";
    exit(1);
}

?>
