<?php

class SMSService {
    private $providers = [];
    private $defaultProvider = 'twilio';
    private $config;
    
    public function __construct($config = []) {
        $this->config = $config;
        $this->initializeProviders();
    }
    
    private function initializeProviders() {
        // Twilio SMS Provider
        $this->providers['twilio'] = [
            'class' => 'TwilioSMSProvider',
            'config' => [
                'account_sid' => $this->config['twilio']['account_sid'] ?? '',
                'auth_token' => $this->config['twilio']['auth_token'] ?? '',
                'from_number' => $this->config['twilio']['from_number'] ?? ''
            ]
        ];
        
        // AWS SNS Provider
        $this->providers['aws_sns'] = [
            'class' => 'AWSSNSProvider',
            'config' => [
                'access_key' => $this->config['aws']['access_key'] ?? '',
                'secret_key' => $this->config['aws']['secret_key'] ?? '',
                'region' => $this->config['aws']['region'] ?? 'us-east-1'
            ]
        ];
        
        // Nexmo/Vonage Provider
        $this->providers['nexmo'] = [
            'class' => 'NexmoSMSProvider',
            'config' => [
                'api_key' => $this->config['nexmo']['api_key'] ?? '',
                'api_secret' => $this->config['nexmo']['api_secret'] ?? '',
                'from' => $this->config['nexmo']['from'] ?? 'EliteTransfer'
            ]
        ];
        
        // Local SMS Provider (for testing)
        $this->providers['local'] = [
            'class' => 'LocalSMSProvider',
            'config' => []
        ];
    }
    
    /**
     * Send SMS using configured provider
     */
    public function sendSMS($phone, $message, $provider = null) {
        $provider = $provider ?: $this->defaultProvider;
        
        if (!isset($this->providers[$provider])) {
            throw new Exception("SMS Provider '{$provider}' not found");
        }
        
        $providerConfig = $this->providers[$provider];
        $providerClass = $providerConfig['class'];
        
        try {
            $providerInstance = $this->createProvider($providerClass, $providerConfig['config']);
            return $providerInstance->send($phone, $message);
        } catch (Exception $e) {
            // Fallback to next available provider
            return $this->sendWithFallback($phone, $message, $provider);
        }
    }
    
    private function sendWithFallback($phone, $message, $failedProvider) {
        $availableProviders = array_keys($this->providers);
        $remainingProviders = array_diff($availableProviders, [$failedProvider]);
        
        foreach ($remainingProviders as $provider) {
            try {
                $providerConfig = $this->providers[$provider];
                $providerInstance = $this->createProvider($providerConfig['class'], $providerConfig['config']);
                
                $result = $providerInstance->send($phone, $message);
                $result['fallback_used'] = true;
                $result['fallback_provider'] = $provider;
                
                return $result;
            } catch (Exception $e) {
                continue;
            }
        }
        
        throw new Exception("All SMS providers failed");
    }
    
    private function createProvider($className, $config) {
        switch ($className) {
            case 'TwilioSMSProvider':
                return new TwilioSMSProvider($config);
            case 'AWSSNSProvider':
                return new AWSSNSProvider($config);
            case 'NexmoSMSProvider':
                return new NexmoSMSProvider($config);
            case 'LocalSMSProvider':
            default:
                return new LocalSMSProvider($config);
        }
    }
    
    /**
     * Get SMS delivery status
     */
    public function getDeliveryStatus($messageId, $provider = null) {
        $provider = $provider ?: $this->defaultProvider;
        
        if (!isset($this->providers[$provider])) {
            return ['status' => 'unknown', 'error' => 'Provider not found'];
        }
        
        $providerConfig = $this->providers[$provider];
        $providerInstance = $this->createProvider($providerConfig['class'], $providerConfig['config']);
        
        return $providerInstance->getStatus($messageId);
    }
    
    /**
     * Validate phone number format
     */
    public function validatePhoneNumber($phone) {
        // Remove all non-numeric characters
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);
        
        // Check if it starts with + and has country code
        if (strpos($cleanPhone, '+') === 0) {
            return strlen($cleanPhone) >= 10 && strlen($cleanPhone) <= 15;
        }
        
        // Add default country code if missing
        if (strlen($cleanPhone) === 9 && strpos($cleanPhone, '5') === 0) {
            // Saudi Arabia mobile number
            return '+966' . $cleanPhone;
        }
        
        return false;
    }
    
    /**
     * Format phone number for international use
     */
    public function formatPhoneNumber($phone, $defaultCountryCode = '+966') {
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);
        
        if (strpos($cleanPhone, '+') === 0) {
            return $cleanPhone;
        }
        
        // Remove leading zero if present
        if (strpos($cleanPhone, '0') === 0) {
            $cleanPhone = substr($cleanPhone, 1);
        }
        
        return $defaultCountryCode . $cleanPhone;
    }
}

// Twilio SMS Provider
class TwilioSMSProvider {
    private $accountSid;
    private $authToken;
    private $fromNumber;
    
    public function __construct($config) {
        $this->accountSid = $config['account_sid'];
        $this->authToken = $config['auth_token'];
        $this->fromNumber = $config['from_number'];
    }
    
    public function send($phone, $message) {
        if (empty($this->accountSid) || empty($this->authToken)) {
            throw new Exception("Twilio credentials not configured");
        }
        
        $url = "https://api.twilio.com/2010-04-01/Accounts/{$this->accountSid}/Messages.json";
        
        $data = [
            'From' => $this->fromNumber,
            'To' => $phone,
            'Body' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $this->accountSid . ':' . $this->authToken);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 201) {
            throw new Exception("Twilio SMS failed: " . $response);
        }
        
        $result = json_decode($response, true);
        
        return [
            'success' => true,
            'message_id' => $result['sid'],
            'provider' => 'twilio',
            'status' => $result['status'],
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
    
    public function getStatus($messageId) {
        $url = "https://api.twilio.com/2010-04-01/Accounts/{$this->accountSid}/Messages/{$messageId}.json";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $this->accountSid . ':' . $this->authToken);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ['status' => 'unknown', 'error' => 'Failed to get status'];
        }
        
        $result = json_decode($response, true);
        
        return [
            'status' => $result['status'],
            'error_code' => $result['error_code'] ?? null,
            'error_message' => $result['error_message'] ?? null,
            'date_sent' => $result['date_sent'],
            'date_updated' => $result['date_updated']
        ];
    }
}

// AWS SNS Provider
class AWSSNSProvider {
    private $accessKey;
    private $secretKey;
    private $region;
    
    public function __construct($config) {
        $this->accessKey = $config['access_key'];
        $this->secretKey = $config['secret_key'];
        $this->region = $config['region'];
    }
    
    public function send($phone, $message) {
        if (empty($this->accessKey) || empty($this->secretKey)) {
            throw new Exception("AWS credentials not configured");
        }
        
        // AWS SNS implementation would go here
        // For now, simulate the call
        
        return [
            'success' => true,
            'message_id' => 'aws_' . uniqid(),
            'provider' => 'aws_sns',
            'status' => 'sent',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
    
    public function getStatus($messageId) {
        return [
            'status' => 'delivered',
            'provider' => 'aws_sns'
        ];
    }
}

// Nexmo/Vonage Provider
class NexmoSMSProvider {
    private $apiKey;
    private $apiSecret;
    private $from;
    
    public function __construct($config) {
        $this->apiKey = $config['api_key'];
        $this->apiSecret = $config['api_secret'];
        $this->from = $config['from'];
    }
    
    public function send($phone, $message) {
        if (empty($this->apiKey) || empty($this->apiSecret)) {
            throw new Exception("Nexmo credentials not configured");
        }
        
        $url = 'https://rest.nexmo.com/sms/json';
        
        $data = [
            'api_key' => $this->apiKey,
            'api_secret' => $this->apiSecret,
            'from' => $this->from,
            'to' => $phone,
            'text' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLOPT_HTTP_CODE);
        curl_close($ch);
        
        $result = json_decode($response, true);
        
        if ($result['messages'][0]['status'] !== '0') {
            throw new Exception("Nexmo SMS failed: " . $result['messages'][0]['error-text']);
        }
        
        return [
            'success' => true,
            'message_id' => $result['messages'][0]['message-id'],
            'provider' => 'nexmo',
            'status' => 'sent',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
    
    public function getStatus($messageId) {
        return [
            'status' => 'delivered',
            'provider' => 'nexmo'
        ];
    }
}

// Local SMS Provider (for testing)
class LocalSMSProvider {
    public function __construct($config) {
        // No configuration needed for local provider
    }
    
    public function send($phone, $message) {
        // Log SMS to file for testing
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'phone' => $phone,
            'message' => $message,
            'provider' => 'local'
        ];
        
        $logFile = __DIR__ . '/../../../storage/logs/sms.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND);
        
        return [
            'success' => true,
            'message_id' => 'local_' . uniqid(),
            'provider' => 'local',
            'status' => 'sent',
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }
    
    public function getStatus($messageId) {
        return [
            'status' => 'delivered',
            'provider' => 'local'
        ];
    }
}
