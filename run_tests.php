#!/usr/bin/env php
<?php

/**
 * Elite Transfer System - Test Runner
 * 
 * This script runs comprehensive tests for the Elite Transfer System
 * Usage: php run_tests.php [group] [format]
 * 
 * Groups: all, database, api, security, business, integration, performance, compliance
 * Formats: console, json, xml, html
 */

require_once __DIR__ . '/tests/SystemTests.php';

class TestRunner {
    private $systemTests;
    private $availableGroups = [
        'all' => 'Run all tests',
        'database' => 'Database connectivity and operations',
        'api' => 'API endpoints and responses',
        'security' => 'Security measures and protections',
        'business' => 'Business logic and calculations',
        'integration' => 'External service integrations',
        'performance' => 'Performance and load testing',
        'compliance' => 'AML/KYC compliance checks'
    ];
    
    private $availableFormats = [
        'console' => 'Console output (default)',
        'json' => 'JSON format',
        'xml' => 'XML format',
        'html' => 'HTML report'
    ];
    
    public function __construct() {
        $this->systemTests = new SystemTests();
    }
    
    public function run($args = []) {
        $this->printHeader();
        
        // Parse command line arguments
        $group = $args[1] ?? 'all';
        $format = $args[2] ?? 'console';
        
        // Validate arguments
        if (!array_key_exists($group, $this->availableGroups)) {
            $this->printUsage();
            return 1;
        }
        
        if (!array_key_exists($format, $this->availableFormats)) {
            $this->printUsage();
            return 1;
        }
        
        // Run tests
        echo "🚀 Running tests for group: {$group}\n";
        echo "📄 Output format: {$format}\n";
        echo "⏰ Started at: " . date('Y-m-d H:i:s') . "\n\n";
        
        $startTime = microtime(true);
        
        if ($group === 'all') {
            $results = $this->systemTests->runAllTests();
        } else {
            $results = $this->systemTests->runTestGroup($group);
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        // Output results in requested format
        if ($format !== 'console') {
            $this->outputResults($results, $format, $totalTime);
        }
        
        // Return appropriate exit code
        $failed = count(array_filter($results, function($r) { return $r['status'] !== 'passed'; }));
        return $failed > 0 ? 1 : 0;
    }
    
    private function printHeader() {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                Elite Transfer System - Test Suite            ║\n";
        echo "║                     Comprehensive Testing                    ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }
    
    private function printUsage() {
        echo "Usage: php run_tests.php [group] [format]\n\n";
        
        echo "Available test groups:\n";
        foreach ($this->availableGroups as $group => $description) {
            echo "  " . str_pad($group, 12) . " - {$description}\n";
        }

        echo "\nAvailable output formats:\n";
        foreach ($this->availableFormats as $format => $description) {
            echo "  " . str_pad($format, 8) . " - {$description}\n";
        }
        
        echo "\nExamples:\n";
        echo "  php run_tests.php all console\n";
        echo "  php run_tests.php security json\n";
        echo "  php run_tests.php performance html\n";
        echo "\n";
    }
    
    private function outputResults($results, $format, $totalTime) {
        $output = $this->systemTests->exportResults($format);
        
        switch ($format) {
            case 'json':
                $filename = 'test_results_' . date('Y-m-d_H-i-s') . '.json';
                file_put_contents($filename, $output);
                echo "\n📄 Results exported to: {$filename}\n";
                break;
                
            case 'xml':
                $filename = 'test_results_' . date('Y-m-d_H-i-s') . '.xml';
                file_put_contents($filename, $output);
                echo "\n📄 Results exported to: {$filename}\n";
                break;
                
            case 'html':
                $filename = 'test_results_' . date('Y-m-d_H-i-s') . '.html';
                file_put_contents($filename, $output);
                echo "\n📄 Results exported to: {$filename}\n";
                echo "🌐 Open in browser: file://" . realpath($filename) . "\n";
                break;
        }
    }
}

// Performance monitoring
class PerformanceMonitor {
    private $startTime;
    private $startMemory;
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage();
    }
    
    public function getStats() {
        return [
            'execution_time' => microtime(true) - $this->startTime,
            'memory_used' => memory_get_usage() - $this->startMemory,
            'peak_memory' => memory_get_peak_usage(),
            'memory_limit' => ini_get('memory_limit')
        ];
    }
    
    public function printStats() {
        $stats = $this->getStats();
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 Performance Statistics\n";
        echo str_repeat("=", 60) . "\n";
        echo "⏱️  Execution Time: " . round($stats['execution_time'], 3) . " seconds\n";
        echo "💾 Memory Used: " . $this->formatBytes($stats['memory_used']) . "\n";
        echo "📈 Peak Memory: " . $this->formatBytes($stats['peak_memory']) . "\n";
        echo "🔒 Memory Limit: " . $stats['memory_limit'] . "\n";
        echo str_repeat("=", 60) . "\n";
    }
    
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// Continuous Integration Helper
class CIHelper {
    public static function isCI() {
        return !empty($_ENV['CI']) || !empty($_ENV['CONTINUOUS_INTEGRATION']);
    }
    
    public static function getCIInfo() {
        $ciSystems = [
            'GITHUB_ACTIONS' => 'GitHub Actions',
            'GITLAB_CI' => 'GitLab CI',
            'JENKINS_URL' => 'Jenkins',
            'TRAVIS' => 'Travis CI',
            'CIRCLECI' => 'CircleCI'
        ];
        
        foreach ($ciSystems as $env => $name) {
            if (!empty($_ENV[$env])) {
                return $name;
            }
        }
        
        return 'Unknown CI System';
    }
    
    public static function exportForCI($results) {
        if (self::isCI()) {
            // Export JUnit XML for CI systems
            $junit = self::generateJUnitXML($results);
            file_put_contents('test-results.xml', $junit);
            
            // Export coverage report (if available)
            if (function_exists('xdebug_get_code_coverage')) {
                $coverage = xdebug_get_code_coverage();
                file_put_contents('coverage.json', json_encode($coverage));
            }
        }
    }
    
    private static function generateJUnitXML($results) {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><testsuites></testsuites>');
        
        $testsuite = $xml->addChild('testsuite');
        $testsuite->addAttribute('name', 'Elite Transfer System Tests');
        $testsuite->addAttribute('tests', count($results));
        $testsuite->addAttribute('failures', count(array_filter($results, function($r) { return $r['status'] === 'failed'; })));
        $testsuite->addAttribute('errors', count(array_filter($results, function($r) { return $r['status'] === 'error'; })));
        
        foreach ($results as $result) {
            $testcase = $testsuite->addChild('testcase');
            $testcase->addAttribute('name', $result['name']);
            $testcase->addAttribute('classname', $result['group']);
            $testcase->addAttribute('time', $result['duration'] / 1000);
            
            if ($result['status'] === 'failed') {
                $failure = $testcase->addChild('failure');
                $failure->addAttribute('message', $result['error'] ?? 'Test failed');
            } elseif ($result['status'] === 'error') {
                $error = $testcase->addChild('error');
                $error->addAttribute('message', $result['error'] ?? 'Test error');
            }
        }
        
        return $xml->asXML();
    }
}

// Main execution
if (php_sapi_name() === 'cli') {
    $monitor = new PerformanceMonitor();
    $runner = new TestRunner();
    
    // Check if running in CI
    if (CIHelper::isCI()) {
        echo "🤖 Running in CI environment: " . CIHelper::getCIInfo() . "\n\n";
    }
    
    // Run tests
    $exitCode = $runner->run($argv);
    
    // Export results for CI if needed
    if (CIHelper::isCI()) {
        // Results would be available here for CI export
        echo "📤 Exporting results for CI system...\n";
    }
    
    // Print performance statistics
    $monitor->printStats();
    
    // Exit with appropriate code
    exit($exitCode);
} else {
    echo "This script must be run from the command line.\n";
    exit(1);
}
