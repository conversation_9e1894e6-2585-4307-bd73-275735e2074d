<?php

/**
 * Database Connection Manager
 * Elite Transfer System - Production Database
 */

class Database {
    private static $instance = null;
    private $connection;
    
    // Database configuration
    private $config = [
        'production' => 'sqlite:' . __DIR__ . '/../../database/elite_transfer_production.db',
        'development' => 'sqlite:' . __DIR__ . '/../../database/elite_transfer.db',
        'test' => 'sqlite:' . __DIR__ . '/../../database/elite_transfer_test.db'
    ];
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            // Use production database by default
            $environment = $_ENV['APP_ENV'] ?? 'production';
            $dsn = $this->config[$environment] ?? $this->config['production'];
            
            $this->connection = new PDO($dsn);
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            
            // Enable foreign keys for SQLite
            $this->connection->exec("PRAGMA foreign_keys = ON");
            
            // Performance optimizations
            $this->connection->exec("PRAGMA journal_mode = WAL");
            $this->connection->exec("PRAGMA synchronous = NORMAL");
            $this->connection->exec("PRAGMA cache_size = 10000");
            $this->connection->exec("PRAGMA temp_store = MEMORY");
            
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }
    
    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }
    
    public function fetchColumn($sql, $params = []) {
        return $this->query($sql, $params)->fetchColumn();
    }
    
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->connection->prepare($sql);
        
        return $stmt->execute($params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute($params);
    }
    
    public function softDelete($table, $id) {
        return $this->update($table, ['deleted_at' => date('Y-m-d H:i:s')], 'id = :id', ['id' => $id]);
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function getLastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    // Utility methods for common operations
    public function tableExists($tableName) {
        $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name = ?";
        return $this->fetchColumn($sql, [$tableName]) !== false;
    }
    
    public function getTableColumns($tableName) {
        $sql = "PRAGMA table_info({$tableName})";
        return $this->fetchAll($sql);
    }
    
    public function getTableCount($tableName) {
        $sql = "SELECT COUNT(*) FROM {$tableName}";
        return $this->fetchColumn($sql);
    }
    
    public function vacuum() {
        return $this->connection->exec("VACUUM");
    }
    
    public function analyze() {
        return $this->connection->exec("ANALYZE");
    }
}

// Helper function for quick database access
function getDB() {
    return Database::getInstance()->getConnection();
}

// Helper function for quick queries
function dbQuery($sql, $params = []) {
    return Database::getInstance()->query($sql, $params);
}

function dbFetchAll($sql, $params = []) {
    return Database::getInstance()->fetchAll($sql, $params);
}

function dbFetchOne($sql, $params = []) {
    return Database::getInstance()->fetchOne($sql, $params);
}

function dbFetchColumn($sql, $params = []) {
    return Database::getInstance()->fetchColumn($sql, $params);
}

function dbInsert($table, $data) {
    return Database::getInstance()->insert($table, $data);
}

function dbUpdate($table, $data, $where, $whereParams = []) {
    return Database::getInstance()->update($table, $data, $where, $whereParams);
}

function dbDelete($table, $where, $params = []) {
    return Database::getInstance()->delete($table, $where, $params);
}

function dbSoftDelete($table, $id) {
    return Database::getInstance()->softDelete($table, $id);
}

?>
