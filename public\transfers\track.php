<?php
// Load session helper
require_once __DIR__ . '/../includes/session_helper.php';

// Get transfer code from URL or form
$transferCode = $_GET['code'] ?? $_POST['transfer_code'] ?? '';
$transfer = null;
$error = '';

if ($transferCode) {
    try {
        // Connect to database
        $db = new PDO('sqlite:' . __DIR__ . '/../../database/elite_transfer.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Search for transfer
        $stmt = $db->prepare("
            SELECT t.*, 
                   sc.name as sender_country_name, sc.currency as sender_currency_name,
                   rc.name as receiver_country_name, rc.currency as receiver_currency_name,
                   u.name as sender_user_name
            FROM transfers t
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            LEFT JOIN users u ON t.sender_id = u.id
            WHERE t.transfer_code = ? OR t.pickup_code = ?
        ");
        $stmt->execute([$transferCode, $transferCode]);
        $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$transfer) {
            $error = 'لم يتم العثور على التحويل. يرجى التحقق من رمز التحويل.';
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ في البحث عن التحويل: ' . $e->getMessage();
    }
}

// Status translations
$statusTranslations = [
    'pending' => 'في الانتظار',
    'pending_payment' => 'في انتظار الدفع',
    'paid' => 'تم الدفع',
    'processing' => 'قيد المعالجة',
    'ready_for_pickup' => 'جاهز للاستلام',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي',
    'refunded' => 'مسترد'
];

$statusColors = [
    'pending' => 'warning',
    'pending_payment' => 'info',
    'paid' => 'primary',
    'processing' => 'info',
    'ready_for_pickup' => 'success',
    'completed' => 'success',
    'cancelled' => 'danger',
    'refunded' => 'secondary'
];

// Get flash message
$flashMessage = get_flash_message();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع التحويل - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .search-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px 0;
        }
        
        .result-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px 0;
        }
        
        .search-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .search-header h2 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .search-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .status-badge {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            display: inline-block;
        }
        
        .transfer-timeline {
            position: relative;
            padding: 30px 0;
        }
        
        .timeline-item {
            position: relative;
            padding: 25px 0 25px 80px;
            border-left: 4px solid #e9ecef;
        }
        
        .timeline-item:last-child {
            border-left: none;
        }
        
        .timeline-icon {
            position: absolute;
            left: -20px;
            top: 30px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .timeline-item.active {
            border-left-color: #28a745;
        }
        
        .timeline-item.active .timeline-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .timeline-item.pending .timeline-icon {
            background: #6c757d;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            font-weight: 500;
            color: #212529;
        }
        
        .navbar-dark {
            background: rgba(0, 0, 0, 0.1) !important;
        }
        
        .alert {
            border-radius: 15px;
            padding: 15px 20px;
        }
        
        .transfer-code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank me-2"></i>
                Elite Transfer
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/">الرئيسية</a>
                <a class="nav-link text-white" href="/transfers/create">تحويل جديد</a>
                <a class="nav-link text-white active" href="/transfers/track">تتبع التحويل</a>
                <?php if (is_logged_in()): ?>
                    <a class="nav-link text-white" href="/dashboard">لوحة التحكم</a>
                    <a class="nav-link text-white" href="/logout">تسجيل الخروج</a>
                <?php else: ?>
                    <a class="nav-link text-white" href="/login">تسجيل الدخول</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php if ($flashMessage): ?>
            <div class="alert alert-<?= $flashMessage['type'] === 'success' ? 'success' : ($flashMessage['type'] === 'error' ? 'danger' : 'info') ?> alert-dismissible fade show" role="alert">
                <i class="bi bi-<?= $flashMessage['type'] === 'success' ? 'check-circle' : ($flashMessage['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                <?= htmlspecialchars($flashMessage['message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Search Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="search-container">
                    <div class="search-header">
                        <h2>
                            <i class="bi bi-search me-3"></i>
                            تتبع التحويل
                        </h2>
                        <p>أدخل رمز التحويل أو رمز الاستلام لتتبع حالة تحويلك</p>
                    </div>
                    
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <input type="text" 
                                       class="form-control" 
                                       name="transfer_code" 
                                       placeholder="أدخل رمز التحويل أو رمز الاستلام"
                                       value="<?= htmlspecialchars($transferCode) ?>"
                                       required>
                                <div class="form-text mt-2">
                                    <i class="bi bi-info-circle me-1"></i>
                                    يمكنك استخدام رمز التحويل (مثل: ET20250725001) أو رمز الاستلام (مثل: 1234)
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-2"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <?php if ($error): ?>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Transfer Results -->
        <?php if ($transfer): ?>
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="result-container">
                        <!-- Transfer Status -->
                        <div class="text-center mb-4">
                            <h3>حالة التحويل</h3>
                            <span class="status-badge bg-<?= $statusColors[$transfer['status']] ?> text-white">
                                <i class="bi bi-<?= $transfer['status'] === 'completed' ? 'check-circle' : ($transfer['status'] === 'cancelled' ? 'x-circle' : 'clock') ?> me-2"></i>
                                <?= $statusTranslations[$transfer['status']] ?? $transfer['status'] ?>
                            </span>
                        </div>

                        <!-- Transfer Codes -->
                        <div class="info-card">
                            <h5 class="text-primary mb-3">
                                <i class="bi bi-qr-code me-2"></i>
                                أرقام التحويل
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">رمز التحويل:</span>
                                        <span class="transfer-code"><?= htmlspecialchars($transfer['transfer_code']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">رمز الاستلام:</span>
                                        <span class="transfer-code text-warning"><?= htmlspecialchars($transfer['pickup_code']) ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">تاريخ الإنشاء:</span>
                                <span class="info-value"><?= date('Y-m-d H:i', strtotime($transfer['created_at'])) ?></span>
                            </div>
                        </div>

                        <!-- Transfer Details -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h5 class="text-info mb-3">
                                        <i class="bi bi-person me-2"></i>
                                        معلومات المرسل
                                    </h5>
                                    
                                    <div class="info-row">
                                        <span class="info-label">الاسم:</span>
                                        <span class="info-value"><?= htmlspecialchars($transfer['sender_name']) ?></span>
                                    </div>
                                    
                                    <div class="info-row">
                                        <span class="info-label">الهاتف:</span>
                                        <span class="info-value"><?= htmlspecialchars($transfer['sender_phone']) ?></span>
                                    </div>
                                    
                                    <div class="info-row">
                                        <span class="info-label">البلد:</span>
                                        <span class="info-value"><?= htmlspecialchars($transfer['sender_country_name']) ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h5 class="text-success mb-3">
                                        <i class="bi bi-person-check me-2"></i>
                                        معلومات المستلم
                                    </h5>
                                    
                                    <div class="info-row">
                                        <span class="info-label">الاسم:</span>
                                        <span class="info-value"><?= htmlspecialchars($transfer['receiver_name']) ?></span>
                                    </div>
                                    
                                    <div class="info-row">
                                        <span class="info-label">الهاتف:</span>
                                        <span class="info-value"><?= htmlspecialchars($transfer['receiver_phone']) ?></span>
                                    </div>
                                    
                                    <div class="info-row">
                                        <span class="info-label">البلد:</span>
                                        <span class="info-value"><?= htmlspecialchars($transfer['receiver_country_name']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Details -->
                        <div class="info-card">
                            <h5 class="text-warning mb-3">
                                <i class="bi bi-currency-exchange me-2"></i>
                                التفاصيل المالية
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-row">
                                        <span class="info-label">المبلغ المرسل:</span>
                                        <span class="info-value"><?= number_format($transfer['amount'], 2) ?> <?= $transfer['sender_currency'] ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="info-row">
                                        <span class="info-label">سعر الصرف:</span>
                                        <span class="info-value"><?= number_format($transfer['exchange_rate'], 4) ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="info-row">
                                        <span class="info-label">الرسوم:</span>
                                        <span class="info-value"><?= number_format($transfer['fee_amount'], 2) ?> <?= $transfer['sender_currency'] ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="info-row">
                                        <span class="info-label">المبلغ المستلم:</span>
                                        <span class="info-value text-success fw-bold"><?= number_format($transfer['converted_amount'], 2) ?> <?= $transfer['receiver_currency'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Transfer Timeline -->
                        <div class="info-card">
                            <h5 class="text-secondary mb-3">
                                <i class="bi bi-clock-history me-2"></i>
                                مراحل التحويل
                            </h5>
                            
                            <div class="transfer-timeline">
                                <div class="timeline-item <?= in_array($transfer['status'], ['pending', 'pending_payment', 'paid', 'processing', 'ready_for_pickup', 'completed']) ? 'active' : 'pending' ?>">
                                    <div class="timeline-icon">
                                        <i class="bi bi-plus-circle"></i>
                                    </div>
                                    <h6>تم إنشاء التحويل</h6>
                                    <p class="text-muted mb-0">تم إنشاء طلب التحويل بنجاح</p>
                                </div>
                                
                                <div class="timeline-item <?= in_array($transfer['status'], ['paid', 'processing', 'ready_for_pickup', 'completed']) ? 'active' : 'pending' ?>">
                                    <div class="timeline-icon">
                                        <i class="bi bi-credit-card"></i>
                                    </div>
                                    <h6>تم الدفع</h6>
                                    <p class="text-muted mb-0">تم استلام المبلغ من المرسل</p>
                                </div>
                                
                                <div class="timeline-item <?= in_array($transfer['status'], ['processing', 'ready_for_pickup', 'completed']) ? 'active' : 'pending' ?>">
                                    <div class="timeline-icon">
                                        <i class="bi bi-gear"></i>
                                    </div>
                                    <h6>قيد المعالجة</h6>
                                    <p class="text-muted mb-0">جاري معالجة التحويل</p>
                                </div>
                                
                                <div class="timeline-item <?= in_array($transfer['status'], ['ready_for_pickup', 'completed']) ? 'active' : 'pending' ?>">
                                    <div class="timeline-icon">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <h6>جاهز للاستلام</h6>
                                    <p class="text-muted mb-0">المبلغ جاهز للاستلام</p>
                                </div>
                                
                                <div class="timeline-item <?= $transfer['status'] === 'completed' ? 'active' : 'pending' ?>">
                                    <div class="timeline-icon">
                                        <i class="bi bi-check-all"></i>
                                    </div>
                                    <h6>مكتمل</h6>
                                    <p class="text-muted mb-0">تم استلام المبلغ بنجاح</p>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="text-center mt-4">
                            <button class="btn btn-outline-primary me-2" onclick="window.print()">
                                <i class="bi bi-printer me-2"></i>
                                طباعة الإيصال
                            </button>
                            
                            <a href="/transfers/create" class="btn btn-success me-2">
                                <i class="bi bi-plus-circle me-2"></i>
                                تحويل جديد
                            </a>

                            <a href="/transfers/view?id=<?= $transfer['id'] ?>" class="btn btn-info">
                                <i class="bi bi-eye me-2"></i>
                                عرض تفصيلي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Help Section -->
        <div class="row justify-content-center mt-4">
            <div class="col-lg-8">
                <div class="search-container">
                    <div class="text-center">
                        <h5 class="text-primary">
                            <i class="bi bi-question-circle me-2"></i>
                            هل تحتاج مساعدة؟
                        </h5>
                        <p class="text-muted">
                            إذا كنت تواجه أي مشكلة في تتبع تحويلك، يرجى التواصل معنا
                        </p>
                        <div class="row">
                            <div class="col-md-4">
                                <i class="bi bi-telephone text-primary fs-3"></i>
                                <h6 class="mt-2">اتصل بنا</h6>
                                <p class="text-muted">+966-11-234-5678</p>
                            </div>
                            <div class="col-md-4">
                                <i class="bi bi-envelope text-primary fs-3"></i>
                                <h6 class="mt-2">راسلنا</h6>
                                <p class="text-muted"><EMAIL></p>
                            </div>
                            <div class="col-md-4">
                                <i class="bi bi-chat-dots text-primary fs-3"></i>
                                <h6 class="mt-2">دردشة مباشرة</h6>
                                <p class="text-muted">متاح 24/7</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus on search input
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="transfer_code"]');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });
    </script>
</body>
</html>
