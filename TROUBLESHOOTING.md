# 🔧 دليل استكشاف الأخطاء - Elite Transfer System

## 🚨 المشاكل الشائعة وحلولها

### 1. مشاكل قاعدة البيانات

#### ❌ خطأ: "Database connection failed"

**الأس<PERSON>اب المحتملة:**
- خادم MySQL غير مشغل
- بيانات اعتماد خاطئة
- قاعدة البيانات غير موجودة

**الحلول:**

```bash
# 1. تشغيل الإصلاح السريع
php quick_fix.php

# 2. إذا فشل، جرب إعادة إنشاء قاعدة البيانات
rm -rf database/
php quick_fix.php

# 3. تحقق من حالة MySQL (إذا كان مثبت)
# Windows (XAMPP): تشغيل XAMPP Control Panel
# Linux: sudo systemctl status mysql
# Mac: brew services list | grep mysql
```

#### ❌ خطأ: "SQLSTATE[42000]: Syntax error"

**السبب:** خطأ في SQL syntax أو إصدار قاعدة البيانات غير متوافق

**الحل:**
```bash
# استخدم SQLite كبديل آمن
php quick_fix.php
```

#### ❌ خطأ: "file is not a database"

**السبب:** ملف قاعدة البيانات تالف

**الحل:**
```bash
# حذف الملفات التالفة وإعادة الإنشاء
rm database/*.db
php quick_fix.php
```

### 2. مشاكل الخادم

#### ❌ خطأ: "Address already in use"

**السبب:** المنفذ 8000 مستخدم بالفعل

**الحل:**
```bash
# استخدم منفذ مختلف
php -S localhost:8001 -t public
# أو
php -S localhost:8080 -t public
```

#### ❌ خطأ: "Permission denied"

**السبب:** صلاحيات الملفات

**الحل:**
```bash
# Windows
icacls . /grant Everyone:F /T

# Linux/Mac
chmod -R 755 .
chmod -R 777 database/
chmod -R 777 storage/
```

### 3. مشاكل PHP

#### ❌ خطأ: "Class 'PDO' not found"

**السبب:** امتداد PDO غير مثبت

**الحل:**
```bash
# تحقق من الامتدادات المثبتة
php -m | grep -i pdo

# إذا لم تكن مثبتة، قم بتثبيتها:
# Ubuntu/Debian: sudo apt-get install php-pdo php-sqlite3
# CentOS/RHEL: sudo yum install php-pdo php-sqlite3
# Windows: تفعيل extension=pdo_sqlite في php.ini
```

#### ❌ خطأ: "Fatal error: Maximum execution time"

**السبب:** وقت التنفيذ محدود

**الحل:**
```bash
# زيادة وقت التنفيذ
php -d max_execution_time=300 -S localhost:8000 -t public
```

### 4. مشاكل الواجهة

#### ❌ الصفحة لا تظهر بشكل صحيح

**الأسباب:**
- ملفات CSS/JS مفقودة
- مشاكل في المسارات

**الحل:**
```bash
# تحقق من وجود الملفات
ls -la public/
ls -la public/assets/

# إذا كانت مفقودة، تأكد من أن الخادم يعمل من المجلد الصحيح
php -S localhost:8000 -t public
```

#### ❌ خطأ: "404 Not Found"

**السبب:** مسارات خاطئة أو ملفات مفقودة

**الحل:**
```bash
# تحقق من وجود index.php
ls -la public/index.php

# تأكد من تشغيل الخادم من المجلد الصحيح
pwd
php -S localhost:8000 -t public
```

### 5. مشاكل تسجيل الدخول

#### ❌ لا يمكن تسجيل الدخول

**الحل:**
```bash
# تأكد من وجود المستخدمين في قاعدة البيانات
php -r "
\$db = new PDO('sqlite:database/elite_transfer.db');
\$users = \$db->query('SELECT email, role FROM users')->fetchAll();
print_r(\$users);
"

# إذا لم يكن هناك مستخدمين، أعد تشغيل الإصلاح
php quick_fix.php
```

**بيانات الدخول الافتراضية:**
- **المدير:** <EMAIL> / password
- **العميل:** <EMAIL> / customer123

### 6. مشاكل الأداء

#### ❌ النظام بطيء

**الحلول:**
```bash
# 1. تحسين قاعدة البيانات
php -r "
\$db = new PDO('sqlite:database/elite_transfer.db');
\$db->exec('VACUUM;');
\$db->exec('ANALYZE;');
echo 'Database optimized';
"

# 2. مسح الملفات المؤقتة
rm -rf storage/cache/*
rm -rf storage/logs/*

# 3. زيادة ذاكرة PHP
php -d memory_limit=512M -S localhost:8000 -t public
```

## 🛠️ أدوات التشخيص

### فحص النظام السريع

```bash
# تشغيل فحص شامل
php simple_test.php
```

### فحص قاعدة البيانات

```bash
# فحص قاعدة البيانات
php -r "
try {
    \$db = new PDO('sqlite:database/elite_transfer.db');
    echo 'Database: OK\n';
    
    \$tables = \$db->query('SELECT name FROM sqlite_master WHERE type=\"table\"')->fetchAll();
    echo 'Tables: ' . count(\$tables) . '\n';
    
    \$users = \$db->query('SELECT COUNT(*) FROM users')->fetchColumn();
    echo 'Users: ' . \$users . '\n';
    
    \$countries = \$db->query('SELECT COUNT(*) FROM countries')->fetchColumn();
    echo 'Countries: ' . \$countries . '\n';
    
} catch (Exception \$e) {
    echo 'Database Error: ' . \$e->getMessage() . '\n';
}
"
```

### فحص الخادم

```bash
# فحص حالة الخادم
curl -I http://localhost:8000

# فحص استجابة API
curl http://localhost:8000/api/health
```

## 🔄 إعادة التثبيت الكاملة

إذا فشلت جميع الحلول، قم بإعادة التثبيت:

```bash
# 1. نسخ احتياطي للبيانات المهمة (إن وجدت)
cp database/elite_transfer.db backup_$(date +%Y%m%d).db

# 2. حذف قاعدة البيانات
rm -rf database/

# 3. إعادة الإنشاء
php quick_fix.php

# 4. تشغيل النظام
php -S localhost:8000 -t public
```

## 📞 طلب المساعدة

إذا استمرت المشاكل:

1. **جمع معلومات النظام:**
```bash
php -v
php -m
uname -a  # Linux/Mac
systeminfo  # Windows
```

2. **فحص سجلات الأخطاء:**
```bash
# سجلات PHP
tail -f /var/log/php_errors.log

# سجلات النظام
ls -la storage/logs/
```

3. **تشغيل التشخيص الشامل:**
```bash
php simple_test.php > diagnosis.txt
```

## ✅ قائمة التحقق السريعة

قبل طلب المساعدة، تأكد من:

- [ ] PHP 7.4+ مثبت
- [ ] امتداد PDO مفعل
- [ ] صلاحيات الكتابة على مجلد database/
- [ ] المنفذ 8000 متاح
- [ ] تم تشغيل `php quick_fix.php`
- [ ] تم تشغيل الخادم من المجلد الصحيح
- [ ] لا توجد رسائل خطأ في المتصفح

## 🎯 نصائح الوقاية

1. **نسخ احتياطية منتظمة:**
```bash
# إنشاء نسخة احتياطية يومية
cp database/elite_transfer.db backups/backup_$(date +%Y%m%d).db
```

2. **مراقبة المساحة:**
```bash
# فحص مساحة القرص
df -h
du -sh database/
```

3. **تحديث منتظم:**
```bash
# تحسين قاعدة البيانات أسبوعياً
php -r "
\$db = new PDO('sqlite:database/elite_transfer.db');
\$db->exec('VACUUM;');
echo 'Database optimized';
"
```

---

**💡 تذكر:** معظم المشاكل يمكن حلها بتشغيل `php quick_fix.php` 🚀
