<?php
// Load session helper
require_once __DIR__ . '/../includes/session_helper.php';

// Require login
require_login();

$userData = get_user_data();
$error = '';
$success = '';

// Connect to database
try {
    $db = new PDO('sqlite:' . __DIR__ . '/../../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get countries for dropdown
$countries = $db->query("SELECT * FROM countries WHERE is_active = 1 ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

// Get exchange rates
$exchangeRates = [];
$rates = $db->query("SELECT * FROM exchange_rates ORDER BY from_currency, to_currency")->fetchAll(PDO::FETCH_ASSOC);
foreach ($rates as $rate) {
    $exchangeRates[$rate['from_currency']][$rate['to_currency']] = $rate['rate'];
}

// Handle form submission
if ($_POST) {
    try {
        // Validate input
        $senderName = trim($_POST['sender_name'] ?? '');
        $senderPhone = trim($_POST['sender_phone'] ?? '');
        $senderCountryId = (int)($_POST['sender_country_id'] ?? 0);
        $receiverName = trim($_POST['receiver_name'] ?? '');
        $receiverPhone = trim($_POST['receiver_phone'] ?? '');
        $receiverCountryId = (int)($_POST['receiver_country_id'] ?? 0);
        $amount = (float)($_POST['amount'] ?? 0);
        $paymentMethod = $_POST['payment_method'] ?? 'cash';
        $pickupMethod = $_POST['pickup_method'] ?? 'cash';
        
        if (empty($senderName) || empty($receiverName) || $amount <= 0) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        if ($senderCountryId === $receiverCountryId) {
            throw new Exception('لا يمكن أن تكون دولة المرسل ودولة المستلم نفسها');
        }
        
        // Get country details
        $senderCountry = $db->query("SELECT * FROM countries WHERE id = $senderCountryId")->fetch(PDO::FETCH_ASSOC);
        $receiverCountry = $db->query("SELECT * FROM countries WHERE id = $receiverCountryId")->fetch(PDO::FETCH_ASSOC);
        
        if (!$senderCountry || !$receiverCountry) {
            throw new Exception('دولة غير صحيحة');
        }
        
        // Calculate exchange rate and fees
        $exchangeRate = $exchangeRates[$senderCountry['currency']][$receiverCountry['currency']] ?? 1.0;
        $convertedAmount = $amount * $exchangeRate;
        
        // Calculate fees (2.5% + fixed fee)
        $feePercentage = 2.5;
        $fixedFee = 5.0;
        $feeAmount = ($amount * $feePercentage / 100) + $fixedFee;
        $totalAmount = $amount + $feeAmount;
        
        // Generate transfer and pickup codes
        $transferCode = 'ET' . date('Ymd') . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        $pickupCode = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        
        // Insert transfer
        $stmt = $db->prepare("
            INSERT INTO transfers (
                transfer_code, pickup_code, sender_id, sender_name, sender_phone, sender_country_id,
                receiver_name, receiver_phone, receiver_country_id, amount, converted_amount,
                exchange_rate, fee_amount, total_amount, sender_currency, receiver_currency,
                status, payment_method, pickup_method, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ");

        $stmt->execute([
            $transferCode, $pickupCode, $userData['id'], $senderName, $senderPhone, $senderCountryId,
            $receiverName, $receiverPhone, $receiverCountryId, $amount, $convertedAmount,
            $exchangeRate, $feeAmount, $totalAmount, $senderCountry['currency'], $receiverCountry['currency'],
            'pending_payment', $paymentMethod, $pickupMethod
        ]);
        
        $success = "تم إنشاء التحويل بنجاح! رمز التحويل: $transferCode";
        
        // Clear form data
        $_POST = [];
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get flash message
$flashMessage = get_flash_message();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء تحويل جديد - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px 0;
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-header h2 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .section-title {
            color: #495057;
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-left: 10px;
            color: #667eea;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .calculation-box {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .calculation-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        
        .calculation-row.total {
            border-top: 2px solid rgba(255, 255, 255, 0.3);
            padding-top: 15px;
            margin-top: 15px;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .navbar-dark {
            background: rgba(0, 0, 0, 0.1) !important;
        }
        
        .alert {
            border-radius: 15px;
            padding: 15px 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank me-2"></i>
                Elite Transfer
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/">الرئيسية</a>
                <a class="nav-link text-white active" href="/transfers/create">تحويل جديد</a>
                <a class="nav-link text-white" href="/track-transfer">تتبع التحويل</a>
                <a class="nav-link text-white" href="/dashboard">لوحة التحكم</a>
                <a class="nav-link text-white" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php if ($flashMessage): ?>
            <div class="alert alert-<?= $flashMessage['type'] === 'success' ? 'success' : ($flashMessage['type'] === 'error' ? 'danger' : 'info') ?> alert-dismissible fade show" role="alert">
                <i class="bi bi-<?= $flashMessage['type'] === 'success' ? 'check-circle' : ($flashMessage['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                <?= htmlspecialchars($flashMessage['message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Error Messages -->
        <?php if ($error): ?>
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <!-- Success Messages -->
        <?php if ($success): ?>
            <div class="alert alert-success" role="alert">
                <i class="bi bi-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-container">
                    <div class="form-header">
                        <h2>
                            <i class="bi bi-plus-circle me-3"></i>
                            إنشاء تحويل جديد
                        </h2>
                        <p>املأ البيانات أدناه لإنشاء تحويل مالي جديد</p>
                    </div>

                    <form method="POST" id="transferForm">
                        <!-- Sender Information -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-person"></i>
                                معلومات المرسل
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المرسل *</label>
                                    <input type="text" class="form-control" name="sender_name" 
                                           value="<?= htmlspecialchars($_POST['sender_name'] ?? '') ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم هاتف المرسل *</label>
                                    <input type="tel" class="form-control" name="sender_phone" 
                                           value="<?= htmlspecialchars($_POST['sender_phone'] ?? '') ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">دولة المرسل *</label>
                                    <select class="form-select" name="sender_country_id" id="senderCountry" required>
                                        <option value="">اختر الدولة</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    data-currency="<?= $country['currency'] ?>"
                                                    <?= (($_POST['sender_country_id'] ?? '') == $country['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($country['name']) ?> (<?= $country['currency'] ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Receiver Information -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-person-check"></i>
                                معلومات المستلم
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المستلم *</label>
                                    <input type="text" class="form-control" name="receiver_name" 
                                           value="<?= htmlspecialchars($_POST['receiver_name'] ?? '') ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم هاتف المستلم *</label>
                                    <input type="tel" class="form-control" name="receiver_phone" 
                                           value="<?= htmlspecialchars($_POST['receiver_phone'] ?? '') ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">دولة المستلم *</label>
                                    <select class="form-select" name="receiver_country_id" id="receiverCountry" required>
                                        <option value="">اختر الدولة</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    data-currency="<?= $country['currency'] ?>"
                                                    <?= (($_POST['receiver_country_id'] ?? '') == $country['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($country['name']) ?> (<?= $country['currency'] ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Transfer Details -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-currency-exchange"></i>
                                تفاصيل التحويل
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المبلغ المراد تحويله *</label>
                                    <input type="number" class="form-control" name="amount" 
                                           value="<?= htmlspecialchars($_POST['amount'] ?? '') ?>" 
                                           step="0.01" min="1" id="amount" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-select" name="payment_method">
                                        <option value="cash">نقداً</option>
                                        <option value="card">بطاقة ائتمان</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">طريقة الاستلام</label>
                                    <select class="form-select" name="pickup_method">
                                        <option value="cash">نقداً</option>
                                        <option value="bank_deposit">إيداع بنكي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Calculation Box -->
                        <div class="calculation-box" id="calculationBox" style="display: none;">
                            <h5><i class="bi bi-calculator me-2"></i>ملخص التحويل</h5>
                            <div class="calculation-row">
                                <span>المبلغ:</span>
                                <span id="displayAmount">0.00</span>
                            </div>
                            <div class="calculation-row">
                                <span>سعر الصرف:</span>
                                <span id="displayRate">1.0000</span>
                            </div>
                            <div class="calculation-row">
                                <span>الرسوم:</span>
                                <span id="displayFees">0.00</span>
                            </div>
                            <div class="calculation-row">
                                <span>المبلغ المستلم:</span>
                                <span id="displayConverted">0.00</span>
                            </div>
                            <div class="calculation-row total">
                                <span>إجمالي المبلغ المطلوب:</span>
                                <span id="displayTotal">0.00</span>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check-circle me-2"></i>
                                إنشاء التحويل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Exchange rates from PHP
        const exchangeRates = <?= json_encode($exchangeRates) ?>;
        
        // Calculate transfer details
        function calculateTransfer() {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const senderCountry = document.getElementById('senderCountry');
            const receiverCountry = document.getElementById('receiverCountry');
            
            if (amount > 0 && senderCountry.value && receiverCountry.value) {
                const senderCurrency = senderCountry.options[senderCountry.selectedIndex].dataset.currency;
                const receiverCurrency = receiverCountry.options[receiverCountry.selectedIndex].dataset.currency;
                
                // Get exchange rate
                const rate = exchangeRates[senderCurrency] && exchangeRates[senderCurrency][receiverCurrency] 
                    ? exchangeRates[senderCurrency][receiverCurrency] 
                    : 1.0;
                
                // Calculate fees (2.5% + 5 fixed)
                const feeAmount = (amount * 0.025) + 5;
                const totalAmount = amount + feeAmount;
                const convertedAmount = amount * rate;
                
                // Update display
                document.getElementById('displayAmount').textContent = amount.toFixed(2) + ' ' + senderCurrency;
                document.getElementById('displayRate').textContent = rate.toFixed(4);
                document.getElementById('displayFees').textContent = feeAmount.toFixed(2) + ' ' + senderCurrency;
                document.getElementById('displayConverted').textContent = convertedAmount.toFixed(2) + ' ' + receiverCurrency;
                document.getElementById('displayTotal').textContent = totalAmount.toFixed(2) + ' ' + senderCurrency;
                
                document.getElementById('calculationBox').style.display = 'block';
            } else {
                document.getElementById('calculationBox').style.display = 'none';
            }
        }
        
        // Add event listeners
        document.getElementById('amount').addEventListener('input', calculateTransfer);
        document.getElementById('senderCountry').addEventListener('change', calculateTransfer);
        document.getElementById('receiverCountry').addEventListener('change', calculateTransfer);
        
        // Prevent same country selection
        document.getElementById('senderCountry').addEventListener('change', function() {
            const receiverSelect = document.getElementById('receiverCountry');
            if (this.value === receiverSelect.value && this.value !== '') {
                receiverSelect.value = '';
                alert('لا يمكن أن تكون دولة المرسل ودولة المستلم نفسها');
            }
        });
        
        document.getElementById('receiverCountry').addEventListener('change', function() {
            const senderSelect = document.getElementById('senderCountry');
            if (this.value === senderSelect.value && this.value !== '') {
                this.value = '';
                alert('لا يمكن أن تكون دولة المرسل ودولة المستلم نفسها');
            }
        });
        
        // Initial calculation
        calculateTransfer();
    </script>
</body>
</html>
