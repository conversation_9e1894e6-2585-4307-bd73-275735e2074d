<?php

echo "🔍 Router Debug - Elite Transfer System\n\n";

// Simulate different paths
$testPaths = [
    '',
    'api/health',
    'admin/users',
    'admin/transfers',
    'track-transfer',
    'login'
];

// Define routes (same as in index.php)
$routes = [
    '' => 'home.php',
    'home' => 'home.php',
    'login' => 'login.php',
    'logout' => 'logout.php',
    'register' => 'register.php',
    'dashboard' => 'dashboard.php',
    'create-transfer' => 'create-transfer.php',
    'track-transfer' => 'track-transfer.php',
    'admin/users' => 'admin/users.php',
    'admin/transfers' => 'admin/transfers.php',
    'admin/reports' => 'admin/reports.php',
    'admin/settings' => 'admin/settings.php',
    'admin/monitoring' => 'admin/monitoring.php',
    'compliance/dashboard' => 'compliance/dashboard.php',
    'api/health' => 'api/health.php'
];

foreach ($testPaths as $testPath) {
    echo "Testing path: '$testPath'\n";
    
    if (array_key_exists($testPath, $routes)) {
        $file = __DIR__ . '/public/' . $routes[$testPath];
        echo "  Route found: {$routes[$testPath]}\n";
        echo "  Full path: $file\n";
        echo "  File exists: " . (file_exists($file) ? "YES" : "NO") . "\n";
    } else {
        echo "  Route NOT found in routes array\n";
    }
    echo "\n";
}

// Test API health specifically
echo "🔍 Testing API Health specifically:\n";
$apiPath = __DIR__ . '/public/api/health.php';
echo "API file path: $apiPath\n";
echo "API file exists: " . (file_exists($apiPath) ? "YES" : "NO") . "\n";

if (file_exists($apiPath)) {
    echo "API file content preview:\n";
    echo "------------------------\n";
    $content = file_get_contents($apiPath);
    echo substr($content, 0, 200) . "...\n";
}

// Test database connection
echo "\n🗄️ Testing database connection:\n";
try {
    $dbPath = __DIR__ . '/database/elite_transfer.db';
    echo "Database path: $dbPath\n";
    echo "Database exists: " . (file_exists($dbPath) ? "YES" : "NO") . "\n";
    
    if (file_exists($dbPath)) {
        $db = new PDO('sqlite:' . $dbPath);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $result = $db->query("SELECT COUNT(*) as count FROM users")->fetch();
        echo "Database connection: SUCCESS\n";
        echo "Users count: {$result['count']}\n";
    }
} catch (Exception $e) {
    echo "Database connection: FAILED\n";
    echo "Error: " . $e->getMessage() . "\n";
}

// Test server info
echo "\n🖥️ Server info:\n";
echo "PHP version: " . PHP_VERSION . "\n";
echo "Current directory: " . __DIR__ . "\n";
echo "Document root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "\n";
echo "Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "\n";

?>
