<?php

class NotificationService {
    private $db;
    private $smsService;
    private $emailService;

    public function __construct($database, $smsService = null, $emailService = null) {
        $this->db = $database;
        $this->smsService = $smsService;
        $this->emailService = $emailService;
    }
    
    /**
     * Send transfer creation notification
     */
    public function sendTransferCreatedNotification($transferData) {
        // Send SMS to sender
        $this->sendSMS(
            $transferData['sender_phone'],
            "تم إنشاء التحويل بنجاح. رمز التحويل: {$transferData['transfer_code']}. رمز الاستلام: {$transferData['pickup_code']}"
        );
        
        // Send SMS to receiver
        $this->sendSMS(
            $transferData['receiver_phone'],
            "تم إرسال تحويل مالي لك من {$transferData['sender_name']}. رمز الاستلام: {$transferData['pickup_code']}. يمكنك استلام المبلغ من أقرب فرع."
        );
        
        // Send Email to sender
        $this->sendEmail(
            $transferData['sender_email'],
            'تأكيد إنشاء التحويل',
            $this->getTransferCreatedEmailTemplate($transferData)
        );
        
        // Log notification
        $this->logNotification($transferData['transfer_id'], 'transfer_created', 'sent');
    }
    
    /**
     * Send transfer status update notification
     */
    public function sendStatusUpdateNotification($transferData, $newStatus) {
        $statusMessages = [
            'processing' => 'تحويلك قيد المعالجة',
            'ready_for_pickup' => 'تحويلك جاهز للاستلام',
            'completed' => 'تم إكمال التحويل بنجاح',
            'cancelled' => 'تم إلغاء التحويل'
        ];
        
        $message = $statusMessages[$newStatus] ?? 'تم تحديث حالة التحويل';
        
        // Send SMS to both parties
        $this->sendSMS(
            $transferData['sender_phone'],
            "{$message}. رمز التحويل: {$transferData['transfer_code']}"
        );
        
        if ($newStatus === 'ready_for_pickup' || $newStatus === 'completed') {
            $this->sendSMS(
                $transferData['receiver_phone'],
                "{$message}. رمز الاستلام: {$transferData['pickup_code']}"
            );
        }
        
        // Log notification
        $this->logNotification($transferData['transfer_id'], 'status_update', 'sent');
    }
    
    /**
     * Send OTP for verification
     */
    public function sendOTP($phone, $otp) {
        $message = "رمز التحقق الخاص بك في Elite Transfer: {$otp}. لا تشارك هذا الرمز مع أحد.";
        return $this->sendSMS($phone, $message);
    }
    
    /**
     * Send SMS using external service
     */
    private function sendSMS($phone, $message) {
        try {
            if ($this->smsService) {
                $result = $this->smsService->sendSMS($phone, $message);

                // Log the API call
                $this->logExternalAPICall('sms', $result['provider'] ?? 'unknown', 'send', [
                    'phone' => $phone,
                    'message' => substr($message, 0, 100) // Log first 100 chars only
                ], $result, $result['success'] ? 200 : 400);

                return $result;
            } else {
                // Fallback to logging
                error_log("SMS to {$phone}: {$message}");

                return [
                    'success' => true,
                    'message_id' => 'SMS_' . uniqid(),
                    'phone' => $phone,
                    'message' => $message,
                    'sent_at' => date('Y-m-d H:i:s'),
                    'provider' => 'local'
                ];
            }
        } catch (Exception $e) {
            error_log("SMS sending failed: " . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'phone' => $phone,
                'provider' => 'error'
            ];
        }
    }
    
    /**
     * Send Email using external service
     */
    private function sendEmail($email, $subject, $body) {
        try {
            if ($this->emailService) {
                $result = $this->emailService->sendEmail($email, $subject, $body, true);

                // Log the API call
                $this->logExternalAPICall('email', $result['provider'] ?? 'unknown', 'send', [
                    'email' => $email,
                    'subject' => $subject
                ], $result, $result['success'] ? 200 : 400);

                return $result;
            } else {
                // Fallback to logging
                error_log("Email to {$email}: {$subject}");

                return [
                    'success' => true,
                    'message_id' => 'EMAIL_' . uniqid(),
                    'email' => $email,
                    'subject' => $subject,
                    'sent_at' => date('Y-m-d H:i:s'),
                    'provider' => 'local'
                ];
            }
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'email' => $email,
                'provider' => 'error'
            ];
        }
    }
    
    /**
     * Get transfer created email template
     */
    private function getTransferCreatedEmailTemplate($data) {
        return "
        <html>
        <head>
            <style>
                body { font-family: 'Arial', sans-serif; direction: rtl; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f8f9fa; }
                .footer { padding: 20px; text-align: center; color: #666; }
                .highlight { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Elite Transfer System</h1>
                    <h2>تأكيد إنشاء التحويل</h2>
                </div>
                <div class='content'>
                    <p>عزيزي {$data['sender_name']},</p>
                    <p>تم إنشاء تحويلك بنجاح. إليك تفاصيل التحويل:</p>
                    
                    <div class='highlight'>
                        <h3>معلومات التحويل</h3>
                        <p><strong>رمز التحويل:</strong> {$data['transfer_code']}</p>
                        <p><strong>رمز الاستلام:</strong> {$data['pickup_code']}</p>
                        <p><strong>المبلغ المرسل:</strong> {$data['amount']}</p>
                        <p><strong>المستلم:</strong> {$data['receiver_name']}</p>
                        <p><strong>الحالة:</strong> قيد المعالجة</p>
                    </div>
                    
                    <p>يمكنك تتبع التحويل في أي وقت باستخدام رمز التحويل.</p>
                    <p>شكراً لاختيارك Elite Transfer System.</p>
                </div>
                <div class='footer'>
                    <p>&copy; 2025 Elite Transfer System. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Log notification in database
     */
    private function logNotification($transferId, $type, $status) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO notifications (transfer_id, type, status, created_at) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$transferId, $type, $status, date('Y-m-d H:i:s')]);
        } catch (Exception $e) {
            error_log("Failed to log notification: " . $e->getMessage());
        }
    }
    
    /**
     * Get user notifications
     */
    public function getUserNotifications($userId, $limit = 10) {
        try {
            $stmt = $this->db->prepare("
                SELECT n.*, t.transfer_code, t.amount, t.status as transfer_status
                FROM notifications n
                LEFT JOIN transfers t ON n.transfer_id = t.id
                WHERE t.sender_id = ? OR t.receiver_id = ?
                ORDER BY n.created_at DESC
                LIMIT ?
            ");
            $stmt->execute([$userId, $userId, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Failed to get notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE notifications n
                LEFT JOIN transfers t ON n.transfer_id = t.id
                SET n.read_at = ?
                WHERE n.id = ? AND (t.sender_id = ? OR t.receiver_id = ?)
            ");
            $stmt->execute([date('Y-m-d H:i:s'), $notificationId, $userId, $userId]);
            return true;
        } catch (Exception $e) {
            error_log("Failed to mark notification as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send daily summary to agents
     */
    public function sendDailySummaryToAgents() {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE role = 'agent' AND is_active = 1");
        $stmt->execute();
        $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($agents as $agent) {
            $summary = $this->getAgentDailySummary($agent['id']);
            
            $this->sendSMS(
                $agent['phone'],
                "ملخص يومك: {$summary['total_transfers']} تحويل، {$summary['total_amount']} مبلغ إجمالي، {$summary['commission']} عمولة."
            );
        }
    }
    
    private function getAgentDailySummary($agentId) {
        $stmt = $this->db->prepare("
            SELECT
                COUNT(*) as total_transfers,
                SUM(amount) as total_amount,
                SUM(fee_amount * 0.3) as commission
            FROM transfers
            WHERE agent_id = ? AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute([$agentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Log external API calls for monitoring
     */
    private function logExternalAPICall($serviceName, $provider, $endpoint, $requestData, $responseData, $statusCode) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO external_api_logs (
                    service_name, provider, endpoint, request_data, response_data,
                    status_code, success, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $serviceName,
                $provider,
                $endpoint,
                json_encode($requestData),
                json_encode($responseData),
                $statusCode,
                $statusCode >= 200 && $statusCode < 300 ? 1 : 0,
                date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log external API call: " . $e->getMessage());
        }
    }
}
