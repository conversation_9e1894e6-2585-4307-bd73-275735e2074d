<?php

class ReportService {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get transfer statistics for dashboard
     */
    public function getTransferStats($userId = null, $role = 'customer', $period = '30days') {
        $dateFilter = $this->getDateFilter($period);
        
        if ($role === 'admin') {
            return $this->getAdminStats($dateFilter);
        } elseif ($role === 'agent') {
            return $this->getAgentStats($userId, $dateFilter);
        } else {
            return $this->getCustomerStats($userId, $dateFilter);
        }
    }
    
    private function getAdminStats($dateFilter) {
        $stats = [];
        
        // Total transfers
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM transfers");
        $stats['total_transfers'] = $stmt->fetchColumn();
        
        // Today's transfers
        $stmt = $this->db->query("SELECT COUNT(*) as today FROM transfers WHERE DATE(created_at) = DATE('now')");
        $stats['today_transfers'] = $stmt->fetchColumn();
        
        // Total amount
        $stmt = $this->db->query("SELECT SUM(amount) as total FROM transfers");
        $stats['total_amount'] = $stmt->fetchColumn() ?: 0;
        
        // Today's amount
        $stmt = $this->db->query("SELECT SUM(amount) as today FROM transfers WHERE DATE(created_at) = DATE('now')");
        $stats['today_amount'] = $stmt->fetchColumn() ?: 0;
        
        // Status counts
        $stmt = $this->db->query("SELECT status, COUNT(*) as count FROM transfers GROUP BY status");
        $statusCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $stats['pending_transfers'] = $statusCounts['pending'] ?? 0;
        $stats['completed_transfers'] = $statusCounts['completed'] ?? 0;
        $stats['processing_transfers'] = $statusCounts['processing'] ?? 0;
        
        // Revenue
        $stmt = $this->db->query("SELECT SUM(fee_amount) as revenue FROM transfers WHERE DATE(created_at) >= DATE('now', '-30 days')");
        $stats['monthly_revenue'] = $stmt->fetchColumn() ?: 0;
        
        $stmt = $this->db->query("SELECT SUM(fee_amount) as revenue FROM transfers WHERE DATE(created_at) >= DATE('now', 'start of year')");
        $stats['yearly_revenue'] = $stmt->fetchColumn() ?: 0;
        
        // User counts
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM users WHERE role = 'customer'");
        $stats['total_customers'] = $stmt->fetchColumn();
        
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM users WHERE role = 'agent'");
        $stats['total_agents'] = $stmt->fetchColumn();
        
        return $stats;
    }
    
    private function getAgentStats($userId, $dateFilter) {
        $stats = [];
        
        // Agent's transfers
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM transfers WHERE agent_id = ?");
        $stmt->execute([$userId]);
        $stats['total_transfers'] = $stmt->fetchColumn();
        
        // Today's transfers
        $stmt = $this->db->prepare("SELECT COUNT(*) as today FROM transfers WHERE agent_id = ? AND DATE(created_at) = DATE('now')");
        $stmt->execute([$userId]);
        $stats['today_transfers'] = $stmt->fetchColumn();
        
        // Status counts
        $stmt = $this->db->prepare("SELECT status, COUNT(*) as count FROM transfers WHERE agent_id = ? GROUP BY status");
        $stmt->execute([$userId]);
        $statusCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $stats['pending_transfers'] = $statusCounts['pending'] ?? 0;
        $stats['completed_transfers'] = $statusCounts['completed'] ?? 0;
        $stats['ready_for_pickup'] = $statusCounts['ready_for_pickup'] ?? 0;
        
        // Commission
        $stmt = $this->db->prepare("SELECT SUM(fee_amount * 0.3) as commission FROM transfers WHERE agent_id = ? AND DATE(created_at) >= DATE('now', '-30 days')");
        $stmt->execute([$userId]);
        $stats['monthly_commission'] = $stmt->fetchColumn() ?: 0;
        
        $stmt = $this->db->prepare("SELECT SUM(fee_amount * 0.3) as commission FROM transfers WHERE agent_id = ?");
        $stmt->execute([$userId]);
        $stats['total_commission'] = $stmt->fetchColumn() ?: 0;
        
        return $stats;
    }
    
    private function getCustomerStats($userId, $dateFilter) {
        $stats = [];
        
        // Sent transfers
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM transfers WHERE sender_id = ?");
        $stmt->execute([$userId]);
        $stats['total_sent'] = $stmt->fetchColumn();
        
        // Received transfers
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM transfers WHERE receiver_phone IN (SELECT phone FROM users WHERE id = ?)");
        $stmt->execute([$userId]);
        $stats['total_received'] = $stmt->fetchColumn();
        
        // Amount sent
        $stmt = $this->db->prepare("SELECT SUM(amount) as total FROM transfers WHERE sender_id = ?");
        $stmt->execute([$userId]);
        $stats['total_amount_sent'] = $stmt->fetchColumn() ?: 0;
        
        // Amount received
        $stmt = $this->db->prepare("SELECT SUM(converted_amount) as total FROM transfers WHERE receiver_phone IN (SELECT phone FROM users WHERE id = ?)");
        $stmt->execute([$userId]);
        $stats['total_amount_received'] = $stmt->fetchColumn() ?: 0;
        
        // Status counts
        $stmt = $this->db->prepare("SELECT status, COUNT(*) as count FROM transfers WHERE sender_id = ? GROUP BY status");
        $stmt->execute([$userId]);
        $statusCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $stats['pending_transfers'] = $statusCounts['pending'] ?? 0;
        $stats['completed_transfers'] = $statusCounts['completed'] ?? 0;
        
        // Monthly sent
        $stmt = $this->db->prepare("SELECT COUNT(*) as monthly FROM transfers WHERE sender_id = ? AND DATE(created_at) >= DATE('now', '-30 days')");
        $stmt->execute([$userId]);
        $stats['monthly_sent'] = $stmt->fetchColumn();
        
        // Available limit (simplified)
        $stmt = $this->db->prepare("SELECT daily_limit FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $dailyLimit = $stmt->fetchColumn() ?: 5000;
        
        $stmt = $this->db->prepare("SELECT SUM(amount) as used FROM transfers WHERE sender_id = ? AND DATE(created_at) = DATE('now')");
        $stmt->execute([$userId]);
        $usedToday = $stmt->fetchColumn() ?: 0;
        
        $stats['available_limit'] = max(0, $dailyLimit - $usedToday);
        
        return $stats;
    }
    
    /**
     * Get chart data for dashboard
     */
    public function getChartData($userId = null, $role = 'customer', $period = '7days') {
        $data = [];
        
        if ($period === '7days') {
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $data[] = [
                    'date' => $date,
                    'label' => date('M d', strtotime($date)),
                    'count' => $this->getTransferCountForDate($date, $userId, $role),
                    'amount' => $this->getTransferAmountForDate($date, $userId, $role)
                ];
            }
        } elseif ($period === '30days') {
            for ($i = 29; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $data[] = [
                    'date' => $date,
                    'label' => date('M d', strtotime($date)),
                    'count' => $this->getTransferCountForDate($date, $userId, $role),
                    'amount' => $this->getTransferAmountForDate($date, $userId, $role)
                ];
            }
        }
        
        return $data;
    }
    
    private function getTransferCountForDate($date, $userId, $role) {
        if ($role === 'admin') {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM transfers WHERE DATE(created_at) = ?");
            $stmt->execute([$date]);
        } elseif ($role === 'agent') {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM transfers WHERE agent_id = ? AND DATE(created_at) = ?");
            $stmt->execute([$userId, $date]);
        } else {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM transfers WHERE sender_id = ? AND DATE(created_at) = ?");
            $stmt->execute([$userId, $date]);
        }
        
        return $stmt->fetchColumn() ?: 0;
    }
    
    private function getTransferAmountForDate($date, $userId, $role) {
        if ($role === 'admin') {
            $stmt = $this->db->prepare("SELECT SUM(amount) FROM transfers WHERE DATE(created_at) = ?");
            $stmt->execute([$date]);
        } elseif ($role === 'agent') {
            $stmt = $this->db->prepare("SELECT SUM(amount) FROM transfers WHERE agent_id = ? AND DATE(created_at) = ?");
            $stmt->execute([$userId, $date]);
        } else {
            $stmt = $this->db->prepare("SELECT SUM(amount) FROM transfers WHERE sender_id = ? AND DATE(created_at) = ?");
            $stmt->execute([$userId, $date]);
        }
        
        return $stmt->fetchColumn() ?: 0;
    }
    
    /**
     * Get detailed transfer report
     */
    public function getTransferReport($filters = []) {
        $sql = "
            SELECT t.*, 
                   u1.name as sender_name, u1.email as sender_email,
                   sc.name_ar as sender_country, rc.name_ar as receiver_country,
                   sc.currency_code as sender_currency, rc.currency_code as receiver_currency
            FROM transfers t
            LEFT JOIN users u1 ON t.sender_id = u1.id
            LEFT JOIN countries sc ON t.sender_country_id = sc.id
            LEFT JOIN countries rc ON t.receiver_country_id = rc.id
            WHERE 1=1
        ";
        
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND DATE(t.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND DATE(t.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND t.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['sender_country'])) {
            $sql .= " AND t.sender_country_id = ?";
            $params[] = $filters['sender_country'];
        }
        
        if (!empty($filters['receiver_country'])) {
            $sql .= " AND t.receiver_country_id = ?";
            $params[] = $filters['receiver_country'];
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . intval($filters['limit']);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get financial summary
     */
    public function getFinancialSummary($period = 'month') {
        $dateFilter = $this->getDateFilter($period);
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_transfers,
                SUM(amount) as total_amount,
                SUM(fee_amount) as total_fees,
                SUM(converted_amount) as total_converted,
                AVG(amount) as avg_amount,
                MIN(amount) as min_amount,
                MAX(amount) as max_amount
            FROM transfers 
            WHERE created_at >= ?
        ");
        $stmt->execute([$dateFilter]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get country-wise statistics
     */
    public function getCountryStats() {
        $stmt = $this->db->query("
            SELECT 
                c.name_ar as country_name,
                COUNT(CASE WHEN t.sender_country_id = c.id THEN 1 END) as sent_count,
                COUNT(CASE WHEN t.receiver_country_id = c.id THEN 1 END) as received_count,
                SUM(CASE WHEN t.sender_country_id = c.id THEN t.amount ELSE 0 END) as sent_amount,
                SUM(CASE WHEN t.receiver_country_id = c.id THEN t.converted_amount ELSE 0 END) as received_amount
            FROM countries c
            LEFT JOIN transfers t ON (c.id = t.sender_country_id OR c.id = t.receiver_country_id)
            GROUP BY c.id, c.name_ar
            ORDER BY sent_count DESC
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getDateFilter($period) {
        switch ($period) {
            case 'today':
                return date('Y-m-d 00:00:00');
            case 'week':
                return date('Y-m-d 00:00:00', strtotime('-7 days'));
            case 'month':
                return date('Y-m-d 00:00:00', strtotime('-30 days'));
            case 'year':
                return date('Y-m-d 00:00:00', strtotime('-365 days'));
            default:
                return date('Y-m-d 00:00:00', strtotime('-30 days'));
        }
    }
}
