<?php

class ComplianceService {
    private $db;
    private $config;
    private $kycProvider;
    
    // AML thresholds
    private $amlThresholds = [
        'single_transaction' => 10000,
        'daily_cumulative' => 15000,
        'monthly_cumulative' => 50000,
        'suspicious_pattern' => 5000
    ];
    
    // KYC requirements
    private $kycThresholds = [
        'basic_kyc' => 1000,
        'enhanced_kyc' => 5000,
        'full_kyc' => 15000
    ];
    
    public function __construct($database, $config = []) {
        $this->db = $database;
        $this->config = $config;
        $this->loadThresholds();
    }
    
    private function loadThresholds() {
        // Load thresholds from configuration
        $this->amlThresholds['single_transaction'] = $this->config['aml_single_transaction'] ?? 10000;
        $this->amlThresholds['daily_cumulative'] = $this->config['aml_daily_cumulative'] ?? 15000;
        $this->amlThresholds['monthly_cumulative'] = $this->config['aml_monthly_cumulative'] ?? 50000;
        
        $this->kycThresholds['basic_kyc'] = $this->config['kyc_basic_threshold'] ?? 1000;
        $this->kycThresholds['enhanced_kyc'] = $this->config['kyc_enhanced_threshold'] ?? 5000;
        $this->kycThresholds['full_kyc'] = $this->config['kyc_full_threshold'] ?? 15000;
    }
    
    /**
     * Perform AML check for transfer
     */
    public function performAMLCheck($transferData) {
        $checks = [];
        $riskScore = 0;
        $requiresReview = false;
        $shouldBlock = false;
        
        // 1. Single transaction amount check
        $singleTransactionCheck = $this->checkSingleTransactionAmount($transferData['amount']);
        $checks['single_transaction'] = $singleTransactionCheck;
        $riskScore += $singleTransactionCheck['risk_score'];
        
        // 2. Daily cumulative amount check
        $dailyCumulativeCheck = $this->checkDailyCumulative($transferData['sender_id'], $transferData['amount']);
        $checks['daily_cumulative'] = $dailyCumulativeCheck;
        $riskScore += $dailyCumulativeCheck['risk_score'];
        
        // 3. Monthly cumulative amount check
        $monthlyCumulativeCheck = $this->checkMonthlyCumulative($transferData['sender_id'], $transferData['amount']);
        $checks['monthly_cumulative'] = $monthlyCumulativeCheck;
        $riskScore += $monthlyCumulativeCheck['risk_score'];
        
        // 4. Suspicious pattern detection
        $patternCheck = $this->checkSuspiciousPatterns($transferData);
        $checks['suspicious_patterns'] = $patternCheck;
        $riskScore += $patternCheck['risk_score'];
        
        // 5. Sanctions list check
        $sanctionsCheck = $this->checkSanctionsList($transferData);
        $checks['sanctions'] = $sanctionsCheck;
        $riskScore += $sanctionsCheck['risk_score'];
        
        // 6. PEP (Politically Exposed Person) check
        $pepCheck = $this->checkPEP($transferData);
        $checks['pep'] = $pepCheck;
        $riskScore += $pepCheck['risk_score'];
        
        // Determine action based on risk score
        if ($riskScore >= 80) {
            $shouldBlock = true;
            $action = 'block';
        } elseif ($riskScore >= 50) {
            $requiresReview = true;
            $action = 'review';
        } elseif ($riskScore >= 25) {
            $action = 'monitor';
        } else {
            $action = 'approve';
        }
        
        // Log AML check
        $this->logAMLCheck($transferData, $riskScore, $action, $checks);
        
        // Generate CTR if required
        if ($transferData['amount'] >= $this->amlThresholds['single_transaction']) {
            $this->generateCTR($transferData, 'large_transaction');
        }
        
        return [
            'risk_score' => $riskScore,
            'action' => $action,
            'requires_review' => $requiresReview,
            'should_block' => $shouldBlock,
            'checks' => $checks,
            'recommendations' => $this->getAMLRecommendations($checks, $riskScore)
        ];
    }
    
    /**
     * Perform KYC verification
     */
    public function performKYCCheck($userId, $amount) {
        $user = $this->getUserKYCStatus($userId);
        $requiredLevel = $this->getRequiredKYCLevel($amount);
        
        $result = [
            'current_level' => $user['kyc_level'] ?? 'none',
            'required_level' => $requiredLevel,
            'is_sufficient' => false,
            'missing_documents' => [],
            'next_steps' => []
        ];
        
        // Check if current KYC level is sufficient
        $kycLevels = ['none' => 0, 'basic' => 1, 'enhanced' => 2, 'full' => 3];
        $currentLevelNum = $kycLevels[$result['current_level']];
        $requiredLevelNum = $kycLevels[$requiredLevel];
        
        if ($currentLevelNum >= $requiredLevelNum) {
            $result['is_sufficient'] = true;
        } else {
            $result['missing_documents'] = $this->getMissingDocuments($userId, $requiredLevel);
            $result['next_steps'] = $this->getKYCNextSteps($requiredLevel);
        }
        
        return $result;
    }
    
    /**
     * Submit KYC documents
     */
    public function submitKYCDocuments($userId, $documents, $personalInfo = []) {
        try {
            // Store documents
            foreach ($documents as $docType => $docData) {
                $this->storeKYCDocument($userId, $docType, $docData);
            }
            
            // Update personal information
            if (!empty($personalInfo)) {
                $this->updateUserPersonalInfo($userId, $personalInfo);
            }
            
            // Trigger KYC verification process
            $verificationResult = $this->triggerKYCVerification($userId);
            
            return [
                'success' => true,
                'verification_id' => $verificationResult['verification_id'],
                'status' => 'pending_review',
                'estimated_completion' => date('Y-m-d H:i:s', strtotime('+2 business days'))
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate Suspicious Activity Report (SAR)
     */
    public function generateSAR($transferData, $reason, $description) {
        try {
            $sarId = 'SAR' . date('Ymd') . strtoupper(substr(uniqid(), -6));
            
            $stmt = $this->db->prepare("
                INSERT INTO suspicious_activity_reports (
                    sar_id, transfer_id, user_id, reason, description, 
                    amount, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?)
            ");
            
            $stmt->execute([
                $sarId,
                $transferData['transfer_id'] ?? null,
                $transferData['sender_id'],
                $reason,
                $description,
                $transferData['amount'],
                date('Y-m-d H:i:s')
            ]);
            
            // Notify compliance team
            $this->notifyComplianceTeam('SAR Generated', [
                'sar_id' => $sarId,
                'user_id' => $transferData['sender_id'],
                'amount' => $transferData['amount'],
                'reason' => $reason
            ]);
            
            return [
                'success' => true,
                'sar_id' => $sarId,
                'status' => 'submitted'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate Currency Transaction Report (CTR)
     */
    public function generateCTR($transferData, $reason) {
        try {
            $ctrId = 'CTR' . date('Ymd') . strtoupper(substr(uniqid(), -6));
            
            $stmt = $this->db->prepare("
                INSERT INTO currency_transaction_reports (
                    ctr_id, transfer_id, user_id, amount, reason, 
                    transaction_date, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?)
            ");
            
            $stmt->execute([
                $ctrId,
                $transferData['transfer_id'] ?? null,
                $transferData['sender_id'],
                $transferData['amount'],
                $reason,
                date('Y-m-d'),
                date('Y-m-d H:i:s')
            ]);
            
            return [
                'success' => true,
                'ctr_id' => $ctrId
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function checkSingleTransactionAmount($amount) {
        $threshold = $this->amlThresholds['single_transaction'];
        
        if ($amount >= $threshold) {
            return [
                'passed' => false,
                'risk_score' => 30,
                'message' => "Transaction amount ({$amount}) exceeds single transaction threshold ({$threshold})",
                'action_required' => 'Generate CTR and review'
            ];
        }
        
        return [
            'passed' => true,
            'risk_score' => 0,
            'message' => 'Single transaction amount within limits'
        ];
    }
    
    private function checkDailyCumulative($userId, $currentAmount) {
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as daily_total 
            FROM transfers 
            WHERE sender_id = ? AND DATE(created_at) = DATE('now')
        ");
        $stmt->execute([$userId]);
        $dailyTotal = $stmt->fetchColumn() ?: 0;
        
        $newTotal = $dailyTotal + $currentAmount;
        $threshold = $this->amlThresholds['daily_cumulative'];
        
        if ($newTotal >= $threshold) {
            return [
                'passed' => false,
                'risk_score' => 25,
                'message' => "Daily cumulative amount ({$newTotal}) exceeds threshold ({$threshold})",
                'daily_total' => $dailyTotal,
                'new_total' => $newTotal
            ];
        }
        
        return [
            'passed' => true,
            'risk_score' => 0,
            'message' => 'Daily cumulative amount within limits',
            'daily_total' => $dailyTotal,
            'new_total' => $newTotal
        ];
    }
    
    private function checkMonthlyCumulative($userId, $currentAmount) {
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as monthly_total 
            FROM transfers 
            WHERE sender_id = ? AND DATE(created_at) >= DATE('now', 'start of month')
        ");
        $stmt->execute([$userId]);
        $monthlyTotal = $stmt->fetchColumn() ?: 0;
        
        $newTotal = $monthlyTotal + $currentAmount;
        $threshold = $this->amlThresholds['monthly_cumulative'];
        
        if ($newTotal >= $threshold) {
            return [
                'passed' => false,
                'risk_score' => 20,
                'message' => "Monthly cumulative amount ({$newTotal}) exceeds threshold ({$threshold})",
                'monthly_total' => $monthlyTotal,
                'new_total' => $newTotal
            ];
        }
        
        return [
            'passed' => true,
            'risk_score' => 0,
            'message' => 'Monthly cumulative amount within limits',
            'monthly_total' => $monthlyTotal,
            'new_total' => $newTotal
        ];
    }
    
    private function checkSuspiciousPatterns($transferData) {
        $patterns = [];
        $riskScore = 0;
        
        // Check for round number amounts
        if ($transferData['amount'] % 1000 == 0 && $transferData['amount'] >= 5000) {
            $patterns[] = 'Round number amount';
            $riskScore += 5;
        }
        
        // Check for frequent transfers to same recipient
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as transfer_count 
            FROM transfers 
            WHERE sender_id = ? AND receiver_phone = ? 
            AND created_at >= DATE('now', '-7 days')
        ");
        $stmt->execute([$transferData['sender_id'], $transferData['receiver_phone']]);
        $recentTransfers = $stmt->fetchColumn();
        
        if ($recentTransfers >= 5) {
            $patterns[] = 'Frequent transfers to same recipient';
            $riskScore += 15;
        }
        
        // Check for unusual timing
        $hour = date('H');
        if ($hour < 6 || $hour > 22) {
            $patterns[] = 'Unusual transaction timing';
            $riskScore += 5;
        }
        
        return [
            'passed' => empty($patterns),
            'risk_score' => $riskScore,
            'patterns' => $patterns,
            'message' => empty($patterns) ? 'No suspicious patterns detected' : 'Suspicious patterns detected'
        ];
    }
    
    private function checkSanctionsList($transferData) {
        // This would integrate with OFAC or other sanctions databases
        // For now, simulate the check
        
        $sanctionedNames = ['John Doe', 'Jane Smith']; // Mock data
        $senderName = $transferData['sender_name'] ?? '';
        $receiverName = $transferData['receiver_name'] ?? '';
        
        $isSanctioned = in_array($senderName, $sanctionedNames) || in_array($receiverName, $sanctionedNames);
        
        if ($isSanctioned) {
            return [
                'passed' => false,
                'risk_score' => 100, // Maximum risk
                'message' => 'Party found on sanctions list',
                'action_required' => 'Block transaction immediately'
            ];
        }
        
        return [
            'passed' => true,
            'risk_score' => 0,
            'message' => 'No sanctions list matches found'
        ];
    }
    
    private function checkPEP($transferData) {
        // This would integrate with PEP databases
        // For now, simulate the check
        
        return [
            'passed' => true,
            'risk_score' => 0,
            'message' => 'No PEP matches found'
        ];
    }
    
    private function getRequiredKYCLevel($amount) {
        if ($amount >= $this->kycThresholds['full_kyc']) {
            return 'full';
        } elseif ($amount >= $this->kycThresholds['enhanced_kyc']) {
            return 'enhanced';
        } elseif ($amount >= $this->kycThresholds['basic_kyc']) {
            return 'basic';
        }
        
        return 'none';
    }
    
    private function getUserKYCStatus($userId) {
        $stmt = $this->db->prepare("
            SELECT kyc_status, kyc_level, kyc_verified_at 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: [
            'kyc_status' => 'pending',
            'kyc_level' => 'none',
            'kyc_verified_at' => null
        ];
    }
    
    private function getMissingDocuments($userId, $requiredLevel) {
        $requiredDocs = [
            'basic' => ['id_document'],
            'enhanced' => ['id_document', 'proof_of_address'],
            'full' => ['id_document', 'proof_of_address', 'proof_of_income', 'bank_statement']
        ];
        
        $stmt = $this->db->prepare("
            SELECT document_type 
            FROM kyc_documents 
            WHERE user_id = ? AND status = 'approved'
        ");
        $stmt->execute([$userId]);
        $existingDocs = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required = $requiredDocs[$requiredLevel] ?? [];
        
        return array_diff($required, $existingDocs);
    }
    
    private function getKYCNextSteps($requiredLevel) {
        $steps = [
            'basic' => ['Upload government-issued ID'],
            'enhanced' => ['Upload government-issued ID', 'Upload proof of address'],
            'full' => ['Upload government-issued ID', 'Upload proof of address', 'Upload proof of income', 'Upload bank statement']
        ];
        
        return $steps[$requiredLevel] ?? [];
    }
    
    private function logAMLCheck($transferData, $riskScore, $action, $checks) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO aml_checks (
                    transfer_id, user_id, risk_score, action, checks_data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $transferData['transfer_id'] ?? null,
                $transferData['sender_id'],
                $riskScore,
                $action,
                json_encode($checks),
                date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log AML check: " . $e->getMessage());
        }
    }
    
    private function getAMLRecommendations($checks, $riskScore) {
        $recommendations = [];
        
        if ($riskScore >= 80) {
            $recommendations[] = 'Block transaction and file SAR';
        } elseif ($riskScore >= 50) {
            $recommendations[] = 'Hold for manual review';
            $recommendations[] = 'Request additional documentation';
        } elseif ($riskScore >= 25) {
            $recommendations[] = 'Monitor future transactions';
        }
        
        return $recommendations;
    }
    
    private function storeKYCDocument($userId, $docType, $docData) {
        $stmt = $this->db->prepare("
            INSERT INTO kyc_documents (
                user_id, document_type, file_path, status, created_at
            ) VALUES (?, ?, ?, 'pending', ?)
        ");
        
        $stmt->execute([
            $userId,
            $docType,
            $docData['file_path'],
            date('Y-m-d H:i:s')
        ]);
    }
    
    private function updateUserPersonalInfo($userId, $personalInfo) {
        $fields = [];
        $values = [];
        
        foreach ($personalInfo as $field => $value) {
            $fields[] = "{$field} = ?";
            $values[] = $value;
        }
        
        $values[] = $userId;
        
        $stmt = $this->db->prepare("
            UPDATE users SET " . implode(', ', $fields) . " WHERE id = ?
        ");
        
        $stmt->execute($values);
    }
    
    private function triggerKYCVerification($userId) {
        // This would integrate with external KYC provider
        // For now, simulate the process
        
        return [
            'verification_id' => 'KYC_' . uniqid(),
            'status' => 'pending'
        ];
    }
    
    private function notifyComplianceTeam($subject, $data) {
        // This would send notification to compliance team
        error_log("Compliance Alert: {$subject} - " . json_encode($data));
    }
}
