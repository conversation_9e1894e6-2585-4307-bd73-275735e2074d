<?php

class StripePaymentProvider {
    private $secretKey;
    private $publicKey;
    private $webhookSecret;
    private $apiVersion = '2023-10-16';
    
    public function __construct($config) {
        $this->secretKey = $config['secret_key'];
        $this->publicKey = $config['public_key'];
        $this->webhookSecret = $config['webhook_secret'];
    }
    
    /**
     * Create payment intent
     */
    public function createPaymentIntent($amount, $currency, $options = []) {
        if (empty($this->secretKey)) {
            throw new Exception("Stripe secret key not configured");
        }
        
        $url = 'https://api.stripe.com/v1/payment_intents';
        
        // Convert amount to cents for Stripe
        $amountCents = intval($amount * 100);
        
        $data = [
            'amount' => $amountCents,
            'currency' => strtolower($currency),
            'automatic_payment_methods' => ['enabled' => true],
            'metadata' => $options['metadata'] ?? []
        ];
        
        $response = $this->makeStripeRequest('POST', $url, $data);
        
        if ($response['success']) {
            return [
                'success' => true,
                'payment_intent_id' => $response['data']['id'],
                'client_secret' => $response['data']['client_secret'],
                'status' => $response['data']['status']
            ];
        }
        
        return [
            'success' => false,
            'error' => $response['error'] ?? 'Failed to create payment intent'
        ];
    }
    
    /**
     * Confirm payment intent
     */
    public function confirmPayment($paymentIntentId) {
        $url = "https://api.stripe.com/v1/payment_intents/{$paymentIntentId}/confirm";
        
        $response = $this->makeStripeRequest('POST', $url, []);
        
        if ($response['success']) {
            return [
                'success' => true,
                'payment_intent_id' => $response['data']['id'],
                'status' => $response['data']['status'],
                'charges' => $response['data']['charges']['data'] ?? []
            ];
        }
        
        return [
            'success' => false,
            'error' => $response['error'] ?? 'Failed to confirm payment'
        ];
    }
    
    /**
     * Process direct payment
     */
    public function processPayment($amount, $currency, $paymentData) {
        try {
            // Create payment intent first
            $intentResult = $this->createPaymentIntent($amount, $currency, $paymentData);
            
            if (!$intentResult['success']) {
                return $intentResult;
            }
            
            // For direct processing, we would need payment method details
            // This is typically handled on the frontend with Stripe.js
            
            return [
                'success' => true,
                'payment_id' => $intentResult['payment_intent_id'],
                'transaction_id' => $intentResult['payment_intent_id'],
                'status' => 'requires_payment_method',
                'client_secret' => $intentResult['client_secret']
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'STRIPE_ERROR'
            ];
        }
    }
    
    /**
     * Process refund
     */
    public function processRefund($chargeId, $amount, $reason = 'requested_by_customer') {
        $url = 'https://api.stripe.com/v1/refunds';
        
        $data = [
            'charge' => $chargeId,
            'reason' => $reason
        ];
        
        if ($amount) {
            $data['amount'] = intval($amount * 100); // Convert to cents
        }
        
        $response = $this->makeStripeRequest('POST', $url, $data);
        
        if ($response['success']) {
            return [
                'success' => true,
                'refund_id' => $response['data']['id'],
                'amount' => $response['data']['amount'] / 100, // Convert back from cents
                'status' => $response['data']['status']
            ];
        }
        
        return [
            'success' => false,
            'error' => $response['error'] ?? 'Failed to process refund'
        ];
    }
    
    /**
     * Get payment details
     */
    public function getPaymentDetails($paymentIntentId) {
        $url = "https://api.stripe.com/v1/payment_intents/{$paymentIntentId}";
        
        $response = $this->makeStripeRequest('GET', $url);
        
        if ($response['success']) {
            $data = $response['data'];
            
            return [
                'success' => true,
                'payment_id' => $data['id'],
                'amount' => $data['amount'] / 100,
                'currency' => strtoupper($data['currency']),
                'status' => $data['status'],
                'created' => $data['created'],
                'charges' => $data['charges']['data'] ?? []
            ];
        }
        
        return [
            'success' => false,
            'error' => $response['error'] ?? 'Failed to get payment details'
        ];
    }
    
    /**
     * Verify webhook signature
     */
    public function verifyWebhook($payload, $signature) {
        if (empty($this->webhookSecret)) {
            throw new Exception("Stripe webhook secret not configured");
        }
        
        $computedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        // Extract timestamp and signature from header
        $elements = explode(',', $signature);
        $timestamp = null;
        $signatures = [];
        
        foreach ($elements as $element) {
            $item = explode('=', $element, 2);
            if ($item[0] === 't') {
                $timestamp = $item[1];
            } elseif ($item[0] === 'v1') {
                $signatures[] = $item[1];
            }
        }
        
        if (!$timestamp || empty($signatures)) {
            throw new Exception("Invalid webhook signature format");
        }
        
        // Check timestamp (should be within 5 minutes)
        if (abs(time() - $timestamp) > 300) {
            throw new Exception("Webhook timestamp too old");
        }
        
        // Verify signature
        $expectedSignature = hash_hmac('sha256', $timestamp . '.' . $payload, $this->webhookSecret);
        
        $signatureValid = false;
        foreach ($signatures as $sig) {
            if (hash_equals($expectedSignature, $sig)) {
                $signatureValid = true;
                break;
            }
        }
        
        if (!$signatureValid) {
            throw new Exception("Invalid webhook signature");
        }
        
        // Parse and return event
        $event = json_decode($payload, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON in webhook payload");
        }
        
        return $event;
    }
    
    /**
     * Create customer
     */
    public function createCustomer($email, $name = null, $metadata = []) {
        $url = 'https://api.stripe.com/v1/customers';
        
        $data = [
            'email' => $email,
            'metadata' => $metadata
        ];
        
        if ($name) {
            $data['name'] = $name;
        }
        
        $response = $this->makeStripeRequest('POST', $url, $data);
        
        if ($response['success']) {
            return [
                'success' => true,
                'customer_id' => $response['data']['id'],
                'email' => $response['data']['email'],
                'created' => $response['data']['created']
            ];
        }
        
        return [
            'success' => false,
            'error' => $response['error'] ?? 'Failed to create customer'
        ];
    }
    
    /**
     * Get customer payment methods
     */
    public function getCustomerPaymentMethods($customerId) {
        $url = "https://api.stripe.com/v1/customers/{$customerId}/payment_methods";
        
        $response = $this->makeStripeRequest('GET', $url);
        
        if ($response['success']) {
            return [
                'success' => true,
                'payment_methods' => $response['data']['data']
            ];
        }
        
        return [
            'success' => false,
            'error' => $response['error'] ?? 'Failed to get payment methods'
        ];
    }
    
    /**
     * Make request to Stripe API
     */
    private function makeStripeRequest($method, $url, $data = []) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->secretKey,
            'Content-Type: application/x-www-form-urlencoded',
            'Stripe-Version: ' . $this->apiVersion
        ]);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if (!empty($data)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            }
        } elseif ($method === 'GET' && !empty($data)) {
            curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'data' => $decodedResponse
            ];
        } else {
            return [
                'success' => false,
                'error' => $decodedResponse['error']['message'] ?? 'Unknown Stripe error',
                'error_code' => $decodedResponse['error']['code'] ?? 'unknown',
                'http_code' => $httpCode
            ];
        }
    }
    
    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies() {
        return [
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK',
            'SAR', 'AED', 'EGP', 'KWD', 'JOD', 'QAR', 'BHD', 'OMR'
        ];
    }
    
    /**
     * Get minimum charge amount for currency
     */
    public function getMinimumAmount($currency) {
        $minimums = [
            'USD' => 0.50,
            'EUR' => 0.50,
            'GBP' => 0.30,
            'SAR' => 2.00,
            'AED' => 2.00,
            'EGP' => 10.00,
            'KWD' => 0.15,
            'JOD' => 0.35
        ];
        
        return $minimums[strtoupper($currency)] ?? 0.50;
    }
    
    /**
     * Format amount for display
     */
    public function formatAmount($amount, $currency) {
        $zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP'];
        
        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return number_format($amount, 0);
        }
        
        return number_format($amount, 2);
    }
}
