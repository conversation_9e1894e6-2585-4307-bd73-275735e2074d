<?php
// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /dashboard');
    exit;
}

$userName = $_SESSION['user_name'] ?? 'مدير النظام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقبة النظام - Elite Financial Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .metric-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-card h4 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .health-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        
        .health-healthy { background-color: #10b981; }
        .health-warning { background-color: #f59e0b; }
        .health-critical { background-color: #ef4444; }
        
        .progress-custom {
            height: 8px;
            border-radius: 10px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .log-entry {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-info { background-color: #e3f2fd; }
        .log-warning { background-color: #fff3e0; }
        .log-error { background-color: #ffebee; }
        
        .auto-refresh {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Auto Refresh Toggle -->
    <div class="auto-refresh">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
            <label class="form-check-label text-white" for="autoRefresh">
                تحديث تلقائي
            </label>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="bi bi-bank2 me-2"></i>
                Elite Transfer System v6.0
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/dashboard">
                    <i class="bi bi-speedometer2 me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link text-white" href="/reports">
                    <i class="bi bi-graph-up me-1"></i>
                    التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?= $userName ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-activity me-2"></i>
                            مراقبة النظام
                        </h2>
                        <p class="text-white-50 mb-0">
                            مراقبة شاملة لأداء وصحة النظام
                        </p>
                    </div>
                    <div>
                        <span class="text-white me-3">
                            <i class="bi bi-clock me-1"></i>
                            آخر تحديث: <span id="lastUpdate">--:--</span>
                        </span>
                        <button class="btn btn-outline-light" onclick="refreshData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-heart-pulse me-2"></i>
                            صحة النظام
                            <span class="health-indicator" id="overallHealthIndicator"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="healthChecks">
                            <!-- Health checks will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card bg-primary">
                    <i class="bi bi-speedometer2 fs-1 mb-3"></i>
                    <h4 id="avgResponseTime">--</h4>
                    <p class="mb-0">متوسط وقت الاستجابة (ms)</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card bg-success">
                    <i class="bi bi-memory fs-1 mb-3"></i>
                    <h4 id="memoryUsage">--%</h4>
                    <p class="mb-0">استخدام الذاكرة</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card bg-warning">
                    <i class="bi bi-hdd fs-1 mb-3"></i>
                    <h4 id="diskUsage">--%</h4>
                    <p class="mb-0">استخدام القرص الصلب</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card bg-info">
                    <i class="bi bi-database fs-1 mb-3"></i>
                    <h4 id="dbConnections">--</h4>
                    <p class="mb-0">اتصالات قاعدة البيانات</p>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            أداء الاستجابة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="responseTimeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart me-2"></i>
                            توزيع استخدام الموارد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="resourceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Stats -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            إحصائيات مفصلة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المقياس</th>
                                        <th>القيمة الحالية</th>
                                        <th>المتوسط</th>
                                        <th>الحد الأقصى</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="detailedStats">
                                    <!-- Stats will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            تنبيهات النظام
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="systemAlerts">
                            <!-- Alerts will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let autoRefreshInterval;
        let responseTimeChart;
        let resourceChart;
        
        // Initialize monitoring
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemHealth();
            loadPerformanceStats();
            initializeCharts();
            
            // Setup auto refresh
            const autoRefreshCheckbox = document.getElementById('autoRefresh');
            autoRefreshCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
            
            // Start auto refresh by default
            startAutoRefresh();
        });
        
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                refreshData();
            }, 30000); // Refresh every 30 seconds
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }
        
        function refreshData() {
            loadSystemHealth();
            loadPerformanceStats();
            updateLastRefreshTime();
        }
        
        function updateLastRefreshTime() {
            const now = new Date();
            document.getElementById('lastUpdate').textContent = 
                now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
        }
        
        function loadSystemHealth() {
            fetch('/api/system-health')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateHealthDisplay(data.data);
                    }
                })
                .catch(error => console.error('Error loading system health:', error));
        }
        
        function loadPerformanceStats() {
            fetch('/api/performance-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updatePerformanceDisplay(data.data);
                    }
                })
                .catch(error => console.error('Error loading performance stats:', error));
        }
        
        function updateHealthDisplay(health) {
            // Update overall health indicator
            const indicator = document.getElementById('overallHealthIndicator');
            indicator.className = 'health-indicator health-' + health.status;
            
            // Update health checks
            const checksContainer = document.getElementById('healthChecks');
            let checksHtml = '';
            
            for (const [check, result] of Object.entries(health.checks)) {
                const statusClass = result.status === 'ok' ? 'success' : 
                                  result.status === 'warning' ? 'warning' : 'danger';
                
                checksHtml += `
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <span class="health-indicator health-${result.status === 'ok' ? 'healthy' : 
                                  result.status === 'warning' ? 'warning' : 'critical'}"></span>
                            <div>
                                <h6 class="mb-0">${getCheckTitle(check)}</h6>
                                <small class="text-${statusClass}">${result.message}</small>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            checksContainer.innerHTML = checksHtml;
        }
        
        function updatePerformanceDisplay(stats) {
            // Update metric cards
            if (stats.application && stats.application.requests) {
                document.getElementById('avgResponseTime').textContent = 
                    Math.round(stats.application.requests.avg_time);
            }
            
            if (stats.server && stats.server.memory_usage) {
                document.getElementById('memoryUsage').textContent = 
                    stats.server.memory_usage.usage_percentage + '%';
            }
            
            if (stats.server && stats.server.disk_usage) {
                document.getElementById('diskUsage').textContent = 
                    stats.server.disk_usage.usage_percentage + '%';
            }
            
            if (stats.database) {
                document.getElementById('dbConnections').textContent = 
                    stats.database.query_count || '0';
            }
            
            // Update detailed stats table
            updateDetailedStats(stats);
            
            // Update system alerts
            updateSystemAlerts(stats);
        }
        
        function updateDetailedStats(stats) {
            const tbody = document.getElementById('detailedStats');
            let html = '';
            
            // Add various metrics
            const metrics = [
                {
                    name: 'وقت الاستجابة',
                    current: stats.application?.requests?.avg_time ? Math.round(stats.application.requests.avg_time) + 'ms' : '--',
                    average: stats.application?.requests?.avg_time ? Math.round(stats.application.requests.avg_time) + 'ms' : '--',
                    max: stats.application?.requests?.max_time ? Math.round(stats.application.requests.max_time) + 'ms' : '--',
                    status: 'جيد'
                },
                {
                    name: 'استخدام الذاكرة',
                    current: stats.server?.memory_usage?.usage_percentage ? stats.server.memory_usage.usage_percentage + '%' : '--',
                    average: '65%',
                    max: '100%',
                    status: stats.server?.memory_usage?.usage_percentage > 80 ? 'تحذير' : 'جيد'
                },
                {
                    name: 'عدد الاستعلامات',
                    current: stats.database?.query_count || '0',
                    average: '150',
                    max: '1000',
                    status: 'جيد'
                }
            ];
            
            metrics.forEach(metric => {
                const statusClass = metric.status === 'جيد' ? 'success' : 
                                  metric.status === 'تحذير' ? 'warning' : 'danger';
                
                html += `
                    <tr>
                        <td>${metric.name}</td>
                        <td>${metric.current}</td>
                        <td>${metric.average}</td>
                        <td>${metric.max}</td>
                        <td><span class="badge bg-${statusClass}">${metric.status}</span></td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }
        
        function updateSystemAlerts(stats) {
            const alertsContainer = document.getElementById('systemAlerts');
            let alertsHtml = '';
            
            // Generate sample alerts based on stats
            if (stats.server?.memory_usage?.usage_percentage > 80) {
                alertsHtml += `
                    <div class="log-entry log-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> استخدام الذاكرة مرتفع (${stats.server.memory_usage.usage_percentage}%)
                        <br><small>${new Date().toLocaleString('ar-SA')}</small>
                    </div>
                `;
            }
            
            if (stats.application?.requests?.avg_time > 1000) {
                alertsHtml += `
                    <div class="log-entry log-warning">
                        <i class="bi bi-clock me-2"></i>
                        <strong>تحذير:</strong> وقت الاستجابة بطيء (${Math.round(stats.application.requests.avg_time)}ms)
                        <br><small>${new Date().toLocaleString('ar-SA')}</small>
                    </div>
                `;
            }
            
            // Add some sample info messages
            alertsHtml += `
                <div class="log-entry log-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>معلومات:</strong> تم تنظيف ذاكرة التخزين المؤقت بنجاح
                    <br><small>${new Date().toLocaleString('ar-SA')}</small>
                </div>
            `;
            
            if (!alertsHtml) {
                alertsHtml = '<p class="text-muted text-center">لا توجد تنبيهات حالياً</p>';
            }
            
            alertsContainer.innerHTML = alertsHtml;
        }
        
        function initializeCharts() {
            // Response Time Chart
            const ctx1 = document.getElementById('responseTimeChart').getContext('2d');
            responseTimeChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                    datasets: [{
                        label: 'وقت الاستجابة (ms)',
                        data: Array.from({length: 24}, () => Math.random() * 500 + 200),
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Resource Usage Chart
            const ctx2 = document.getElementById('resourceChart').getContext('2d');
            resourceChart = new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['الذاكرة المستخدمة', 'الذاكرة المتاحة', 'القرص المستخدم', 'القرص المتاح'],
                    datasets: [{
                        data: [65, 35, 45, 55],
                        backgroundColor: [
                            '#ef4444',
                            '#10b981',
                            '#f59e0b',
                            '#3b82f6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function getCheckTitle(check) {
            const titles = {
                'database': 'قاعدة البيانات',
                'memory': 'الذاكرة',
                'disk': 'القرص الصلب',
                'cache': 'التخزين المؤقت'
            };
            return titles[check] || check;
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(() => {
                    window.location.href = '/';
                });
            }
        }
    </script>
</body>
</html>
