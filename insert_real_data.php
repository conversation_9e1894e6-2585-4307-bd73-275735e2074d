<?php

echo "🌍 Inserting Real Data - Elite Transfer System\n\n";

try {
    // Connect to production database
    $db = new PDO('sqlite:database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to production database\n\n";
    
    // Insert real countries
    echo "🌍 Inserting countries...\n";
    
    $countries = [
        ['Saudi Arabia', 'SA', 'SAR', '﷼', '+966', 'Asia', 1, 2, 50000.00, 10000.00, 100000.00],
        ['United Arab Emirates', 'AE', 'AED', 'د.إ', '+971', 'Asia', 1, 2, 25000.00, 5000.00, 50000.00],
        ['Egypt', 'EG', 'EGP', '£', '+20', 'Africa', 1, 3, 15000.00, 3000.00, 30000.00],
        ['Jordan', 'JO', 'JOD', 'د.ا', '+962', 'Asia', 1, 2, 20000.00, 4000.00, 40000.00],
        ['Lebanon', 'LB', 'LBP', 'ل.ل', '+961', 'Asia', 1, 4, 10000.00, 2000.00, 20000.00],
        ['Kuwait', 'KW', 'KWD', 'د.ك', '+965', 'Asia', 1, 2, 30000.00, 6000.00, 60000.00],
        ['Qatar', 'QA', 'QAR', 'ر.ق', '+974', 'Asia', 1, 2, 35000.00, 7000.00, 70000.00],
        ['Bahrain', 'BH', 'BHD', '.د.ب', '+973', 'Asia', 1, 2, 25000.00, 5000.00, 50000.00],
        ['Oman', 'OM', 'OMR', 'ر.ع.', '+968', 'Asia', 1, 2, 20000.00, 4000.00, 40000.00],
        ['Pakistan', 'PK', 'PKR', '₨', '+92', 'Asia', 1, 3, 15000.00, 3000.00, 30000.00],
        ['India', 'IN', 'INR', '₹', '+91', 'Asia', 1, 3, 20000.00, 4000.00, 40000.00],
        ['Bangladesh', 'BD', 'BDT', '৳', '+880', 'Asia', 1, 3, 12000.00, 2500.00, 25000.00],
        ['Philippines', 'PH', 'PHP', '₱', '+63', 'Asia', 1, 3, 18000.00, 3500.00, 35000.00],
        ['Turkey', 'TR', 'TRY', '₺', '+90', 'Asia', 1, 3, 22000.00, 4500.00, 45000.00],
        ['United Kingdom', 'GB', 'GBP', '£', '+44', 'Europe', 1, 1, 50000.00, 10000.00, 100000.00],
        ['United States', 'US', 'USD', '$', '+1', 'Americas', 1, 1, 75000.00, 15000.00, 150000.00]
    ];
    
    $countryStmt = $db->prepare("
        INSERT INTO countries (name, code, currency, currency_symbol, phone_prefix, region, is_active, risk_level, max_transaction_limit, daily_limit, monthly_limit) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($countries as $country) {
        $countryStmt->execute($country);
    }
    
    echo "✅ Inserted " . count($countries) . " countries\n";
    
    // Insert exchange rates
    echo "💱 Inserting exchange rates...\n";
    
    $exchangeRates = [
        ['USD', 'SAR', 3.7500, 3.7400, 3.7600, 'xe.com', 0.0200],
        ['USD', 'AED', 3.6725, 3.6700, 3.6750, 'xe.com', 0.0150],
        ['USD', 'EGP', 30.9000, 30.8500, 30.9500, 'xe.com', 0.0300],
        ['USD', 'JOD', 0.7090, 0.7085, 0.7095, 'xe.com', 0.0100],
        ['USD', 'LBP', 89500.0000, 89000.0000, 90000.0000, 'manual', 0.0500],
        ['USD', 'KWD', 0.3070, 0.3065, 0.3075, 'xe.com', 0.0100],
        ['USD', 'QAR', 3.6400, 3.6380, 3.6420, 'xe.com', 0.0150],
        ['USD', 'BHD', 0.3760, 0.3755, 0.3765, 'xe.com', 0.0100],
        ['USD', 'OMR', 0.3845, 0.3840, 0.3850, 'xe.com', 0.0100],
        ['USD', 'PKR', 278.5000, 278.0000, 279.0000, 'xe.com', 0.0300],
        ['USD', 'INR', 83.2500, 83.2000, 83.3000, 'xe.com', 0.0200],
        ['USD', 'BDT', 109.7500, 109.5000, 110.0000, 'xe.com', 0.0250],
        ['USD', 'PHP', 56.2500, 56.2000, 56.3000, 'xe.com', 0.0200],
        ['USD', 'TRY', 32.1500, 32.1000, 32.2000, 'xe.com', 0.0400],
        ['USD', 'GBP', 0.7850, 0.7845, 0.7855, 'xe.com', 0.0100],
        ['SAR', 'EGP', 8.2400, 8.2200, 8.2600, 'manual', 0.0250],
        ['SAR', 'JOD', 0.1891, 0.1890, 0.1892, 'manual', 0.0150],
        ['SAR', 'PKR', 74.2667, 74.1000, 74.4000, 'manual', 0.0300],
        ['SAR', 'INR', 22.2000, 22.1500, 22.2500, 'manual', 0.0200],
        ['AED', 'EGP', 8.4150, 8.4000, 8.4300, 'manual', 0.0250],
        ['AED', 'PKR', 75.8163, 75.6000, 76.0000, 'manual', 0.0300],
        ['AED', 'INR', 22.6531, 22.6000, 22.7000, 'manual', 0.0200]
    ];
    
    $rateStmt = $db->prepare("
        INSERT INTO exchange_rates (from_currency, to_currency, rate, buy_rate, sell_rate, provider, margin_percentage, valid_from, valid_until) 
        VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now', '+1 day'))
    ");
    
    foreach ($exchangeRates as $rate) {
        $rateStmt->execute($rate);
    }
    
    echo "✅ Inserted " . count($exchangeRates) . " exchange rates\n";
    
    // Insert system settings
    echo "⚙️ Inserting system settings...\n";
    
    $settings = [
        ['general', 'company_name', 'Elite Transfer System', 'string', 'Company name'],
        ['general', 'company_email', '<EMAIL>', 'string', 'Company email'],
        ['general', 'support_email', '<EMAIL>', 'string', 'Support email'],
        ['fees', 'default_fee_percentage', '2.50', 'decimal', 'Default fee percentage'],
        ['fees', 'fixed_fee_amount', '5.00', 'decimal', 'Fixed fee amount'],
        ['limits', 'min_transfer_amount', '10.00', 'decimal', 'Minimum transfer amount'],
        ['limits', 'max_transfer_amount', '50000.00', 'decimal', 'Maximum transfer amount'],
        ['limits', 'daily_transfer_limit', '10000.00', 'decimal', 'Daily transfer limit'],
        ['kyc', 'kyc_required_amount', '1000.00', 'decimal', 'KYC required amount'],
        ['security', 'session_timeout_minutes', '30', 'integer', 'Session timeout'],
        ['security', 'max_login_attempts', '5', 'integer', 'Max login attempts']
    ];
    
    $settingStmt = $db->prepare("
        INSERT INTO system_settings (category, key, value, data_type, description) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    foreach ($settings as $setting) {
        $settingStmt->execute($setting);
    }
    
    echo "✅ Inserted " . count($settings) . " system settings\n";
    
    // Insert admin users
    echo "👥 Creating admin users...\n";
    
    $users = [
        ['USR001', 'System Administrator', '<EMAIL>', '+966501234567', password_hash('admin123', PASSWORD_DEFAULT), 'admin', 'active', 'verified', 5, 1],
        ['USR002', 'Ahmed Al-Rashid', '<EMAIL>', '+966502345678', password_hash('manager123', PASSWORD_DEFAULT), 'manager', 'active', 'verified', 4, 1],
        ['USR003', 'Fatima Al-Zahra', '<EMAIL>', '+966503456789', password_hash('agent123', PASSWORD_DEFAULT), 'agent', 'active', 'verified', 3, 1],
        ['USR004', 'Omar Hassan', '<EMAIL>', '+966504567890', password_hash('compliance123', PASSWORD_DEFAULT), 'compliance', 'active', 'verified', 4, 1],
        ['USR005', 'Sarah Johnson', '<EMAIL>', '+966505678901', password_hash('customer123', PASSWORD_DEFAULT), 'customer', 'active', 'verified', 2, 1]
    ];
    
    $userStmt = $db->prepare("
        INSERT INTO users (user_code, name, email, phone, password_hash, role, status, kyc_status, kyc_level, country_id) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($users as $user) {
        $userStmt->execute($user);
    }
    
    echo "✅ Created " . count($users) . " users\n";
    
    // Get statistics
    echo "\n📊 Database Statistics:\n";
    echo "Countries: " . $db->query("SELECT COUNT(*) FROM countries")->fetchColumn() . "\n";
    echo "Exchange Rates: " . $db->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn() . "\n";
    echo "System Settings: " . $db->query("SELECT COUNT(*) FROM system_settings")->fetchColumn() . "\n";
    echo "Users: " . $db->query("SELECT COUNT(*) FROM users")->fetchColumn() . "\n";
    echo "Transfers: " . $db->query("SELECT COUNT(*) FROM transfers")->fetchColumn() . "\n";
    
    echo "\n🎉 Real data insertion completed successfully!\n";
    echo "📊 Production database is ready for use\n";
    echo "\n👥 Admin Accounts:\n";
    echo "  - <EMAIL> / admin123 (System Admin)\n";
    echo "  - <EMAIL> / manager123 (Manager)\n";
    echo "  - <EMAIL> / agent123 (Agent)\n";
    echo "  - <EMAIL> / compliance123 (Compliance)\n";
    echo "  - <EMAIL> / customer123 (Customer)\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
