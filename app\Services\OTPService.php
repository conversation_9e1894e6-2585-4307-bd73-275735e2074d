<?php

class OTPService {
    private $db;
    private $notificationService;
    
    public function __construct($database, $notificationService) {
        $this->db = $database;
        $this->notificationService = $notificationService;
    }
    
    /**
     * Generate and send OTP
     */
    public function generateOTP($phone, $purpose = 'verification', $userId = null) {
        // Generate 6-digit OTP
        $otp = sprintf('%06d', mt_rand(0, 999999));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+10 minutes'));
        
        try {
            // Store OTP in database
            $stmt = $this->db->prepare("
                INSERT INTO otps (phone, otp_code, purpose, user_id, expires_at, created_at) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$phone, $otp, $purpose, $userId, $expiresAt, date('Y-m-d H:i:s')]);
            
            // Send OTP via SMS
            $result = $this->notificationService->sendOTP($phone, $otp);
            
            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'تم إرسال رمز التحقق بنجاح',
                    'expires_in' => 600 // 10 minutes in seconds
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إرسال رمز التحقق'
                ];
            }
            
        } catch (Exception $e) {
            error_log("OTP generation failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء رمز التحقق'
            ];
        }
    }
    
    /**
     * Verify OTP
     */
    public function verifyOTP($phone, $otp, $purpose = 'verification') {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM otps 
                WHERE phone = ? AND otp_code = ? AND purpose = ? 
                AND expires_at > ? AND verified_at IS NULL 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$phone, $otp, $purpose, date('Y-m-d H:i:s')]);
            $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$otpRecord) {
                return [
                    'success' => false,
                    'message' => 'رمز التحقق غير صحيح أو منتهي الصلاحية'
                ];
            }
            
            // Mark OTP as verified
            $stmt = $this->db->prepare("
                UPDATE otps SET verified_at = ? WHERE id = ?
            ");
            $stmt->execute([date('Y-m-d H:i:s'), $otpRecord['id']]);
            
            return [
                'success' => true,
                'message' => 'تم التحقق بنجاح',
                'user_id' => $otpRecord['user_id']
            ];
            
        } catch (Exception $e) {
            error_log("OTP verification failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق من الرمز'
            ];
        }
    }
    
    /**
     * Check if phone number needs OTP verification
     */
    public function needsOTPVerification($phone, $purpose = 'verification') {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM otps 
                WHERE phone = ? AND purpose = ? 
                AND verified_at IS NOT NULL 
                AND created_at > ?
            ");
            $stmt->execute([$phone, $purpose, date('Y-m-d H:i:s', strtotime('-24 hours'))]);
            $verifiedCount = $stmt->fetchColumn();
            
            return $verifiedCount == 0;
            
        } catch (Exception $e) {
            error_log("OTP check failed: " . $e->getMessage());
            return true; // Default to requiring verification
        }
    }
    
    /**
     * Clean expired OTPs
     */
    public function cleanExpiredOTPs() {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM otps 
                WHERE expires_at < ? OR created_at < ?
            ");
            $stmt->execute([
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s', strtotime('-7 days'))
            ]);
            
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            error_log("OTP cleanup failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get OTP attempts for rate limiting
     */
    public function getOTPAttempts($phone, $timeWindow = 3600) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM otps 
                WHERE phone = ? AND created_at > ?
            ");
            $stmt->execute([$phone, date('Y-m-d H:i:s', strtotime("-{$timeWindow} seconds"))]);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("OTP attempts check failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Check rate limit for OTP requests
     */
    public function checkRateLimit($phone, $maxAttempts = 5, $timeWindow = 3600) {
        $attempts = $this->getOTPAttempts($phone, $timeWindow);
        
        if ($attempts >= $maxAttempts) {
            return [
                'success' => false,
                'message' => 'تم تجاوز الحد الأقصى لطلبات رمز التحقق. يرجى المحاولة لاحقاً.',
                'retry_after' => $timeWindow
            ];
        }
        
        return [
            'success' => true,
            'remaining_attempts' => $maxAttempts - $attempts
        ];
    }
    
    /**
     * Resend OTP
     */
    public function resendOTP($phone, $purpose = 'verification', $userId = null) {
        // Check rate limit
        $rateLimit = $this->checkRateLimit($phone, 3, 300); // 3 attempts per 5 minutes for resend
        
        if (!$rateLimit['success']) {
            return $rateLimit;
        }
        
        // Check if there's a recent OTP that hasn't expired
        $stmt = $this->db->prepare("
            SELECT * FROM otps 
            WHERE phone = ? AND purpose = ? 
            AND expires_at > ? AND verified_at IS NULL 
            AND created_at > ?
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([
            $phone, 
            $purpose, 
            date('Y-m-d H:i:s'), 
            date('Y-m-d H:i:s', strtotime('-2 minutes'))
        ]);
        $recentOTP = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($recentOTP) {
            // Resend the same OTP
            $result = $this->notificationService->sendOTP($phone, $recentOTP['otp_code']);
            
            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'تم إعادة إرسال رمز التحقق',
                    'expires_in' => strtotime($recentOTP['expires_at']) - time()
                ];
            }
        }
        
        // Generate new OTP
        return $this->generateOTP($phone, $purpose, $userId);
    }
    
    /**
     * Enable 2FA for user
     */
    public function enable2FA($userId, $phone) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users SET two_factor_enabled = 1, two_factor_phone = ? WHERE id = ?
            ");
            $stmt->execute([$phone, $userId]);
            
            return [
                'success' => true,
                'message' => 'تم تفعيل التحقق بخطوتين بنجاح'
            ];
            
        } catch (Exception $e) {
            error_log("2FA enable failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'فشل في تفعيل التحقق بخطوتين'
            ];
        }
    }
    
    /**
     * Disable 2FA for user
     */
    public function disable2FA($userId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users SET two_factor_enabled = 0, two_factor_phone = NULL WHERE id = ?
            ");
            $stmt->execute([$userId]);
            
            return [
                'success' => true,
                'message' => 'تم إلغاء التحقق بخطوتين بنجاح'
            ];
            
        } catch (Exception $e) {
            error_log("2FA disable failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'فشل في إلغاء التحقق بخطوتين'
            ];
        }
    }
    
    /**
     * Check if user has 2FA enabled
     */
    public function has2FAEnabled($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT two_factor_enabled, two_factor_phone FROM users WHERE id = ?
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'enabled' => (bool)($user['two_factor_enabled'] ?? false),
                'phone' => $user['two_factor_phone'] ?? null
            ];
            
        } catch (Exception $e) {
            error_log("2FA check failed: " . $e->getMessage());
            return ['enabled' => false, 'phone' => null];
        }
    }
}
