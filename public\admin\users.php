<?php
// Load session helper
require_once __DIR__ . '/../includes/session_helper.php';

// Require admin access
require_admin();

// Connect to production database
try {
    $db = new PDO('sqlite:' . __DIR__ . '/../../database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Enable foreign keys and performance optimizations
    $db->exec("PRAGMA foreign_keys = ON");
    $db->exec("PRAGMA journal_mode = WAL");
    $db->exec("PRAGMA synchronous = NORMAL");
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Handle user actions
$message = '';
$messageType = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_user':
                try {
                    $stmt = $db->prepare("INSERT INTO users (name, email, phone, password, role, kyc_status, kyc_level) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $hashedPassword = password_hash($_POST['password'], PASSWORD_DEFAULT);
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['email'],
                        $_POST['phone'],
                        $hashedPassword,
                        $_POST['role'],
                        $_POST['kyc_status'],
                        $_POST['kyc_level']
                    ]);
                    $message = 'تم إضافة المستخدم بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'خطأ في إضافة المستخدم: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'update_user':
                try {
                    $sql = "UPDATE users SET name = ?, email = ?, phone = ?, role = ?, kyc_status = ?, kyc_level = ?, is_active = ? WHERE id = ?";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['email'],
                        $_POST['phone'],
                        $_POST['role'],
                        $_POST['kyc_status'],
                        $_POST['kyc_level'],
                        isset($_POST['is_active']) ? 1 : 0,
                        $_POST['user_id']
                    ]);
                    $message = 'تم تحديث المستخدم بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'خطأ في تحديث المستخدم: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'delete_user':
                try {
                    $stmt = $db->prepare("DELETE FROM users WHERE id = ? AND id != ?");
                    $stmt->execute([$_POST['user_id'], $_SESSION['user_id']]);
                    $message = 'تم حذف المستخدم بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'خطأ في حذف المستخدم: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get users with pagination
$page = $_GET['page'] ?? 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';

$whereClause = '';
$params = [];

if ($search) {
    $whereClause .= " WHERE (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $params = ["%$search%", "%$search%", "%$search%"];
}

if ($role_filter) {
    $whereClause .= $whereClause ? " AND role = ?" : " WHERE role = ?";
    $params[] = $role_filter;
}

// Get total count
$countSql = "SELECT COUNT(*) FROM users" . $whereClause;
$countStmt = $db->prepare($countSql);
$countStmt->execute($params);
$totalUsers = $countStmt->fetchColumn();
$totalPages = ceil($totalUsers / $limit);

// Get users
$sql = "SELECT * FROM users" . $whereClause . " ORDER BY created_at DESC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$stmt = $db->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [
    'total' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'admins' => $db->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn(),
    'customers' => $db->query("SELECT COUNT(*) FROM users WHERE role = 'customer'")->fetchColumn(),
    'agents' => $db->query("SELECT COUNT(*) FROM users WHERE role = 'agent'")->fetchColumn(),
    'verified' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'verified'")->fetchColumn(),
    'pending' => $db->query("SELECT COUNT(*) FROM users WHERE kyc_status = 'pending'")->fetchColumn()
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4><i class="bi bi-bank me-2"></i>Elite Transfer</h4>
                    <small>نظام إدارة التحويلات</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                    </a>
                    <a class="nav-link active" href="/admin/users">
                        <i class="bi bi-people me-2"></i>إدارة المستخدمين
                    </a>
                    <a class="nav-link" href="/admin/transfers">
                        <i class="bi bi-arrow-left-right me-2"></i>إدارة التحويلات
                    </a>
                    <a class="nav-link" href="/admin/reports">
                        <i class="bi bi-graph-up me-2"></i>التقارير
                    </a>
                    <a class="nav-link" href="/admin/settings">
                        <i class="bi bi-gear me-2"></i>الإعدادات
                    </a>
                    <a class="nav-link" href="/compliance/dashboard">
                        <i class="bi bi-shield-check me-2"></i>الامتثال
                    </a>
                    <a class="nav-link" href="/admin/monitoring">
                        <i class="bi bi-activity me-2"></i>المراقبة
                    </a>
                </nav>
                
                <div class="mt-auto pt-4">
                    <div class="text-center">
                        <small>مرحباً، <?= htmlspecialchars(get_user_data()['name']) ?></small>
                        <br>
                        <a href="/logout" class="btn btn-outline-light btn-sm mt-2">
                            <i class="bi bi-box-arrow-right me-1"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-people me-2"></i>إدارة المستخدمين</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مستخدم جديد
                    </button>
                </div>
                
                <!-- Alert Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-people fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['total'] ?></h4>
                                    <small>إجمالي المستخدمين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card success">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-check fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['verified'] ?></h4>
                                    <small>مستخدمين موثقين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card warning">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-x fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['pending'] ?></h4>
                                    <small>في الانتظار</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card info">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-badge fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['admins'] ?></h4>
                                    <small>مديرين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['customers'] ?></h4>
                                    <small>عملاء</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card info">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-workspace fs-2 me-3"></i>
                                <div>
                                    <h4 class="mb-0"><?= $stats['agents'] ?></h4>
                                    <small>وكلاء</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="البحث بالاسم أو البريد أو الهاتف" 
                                       value="<?= htmlspecialchars($search) ?>">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" name="role">
                                    <option value="">جميع الأدوار</option>
                                    <option value="admin" <?= $role_filter === 'admin' ? 'selected' : '' ?>>مدير</option>
                                    <option value="customer" <?= $role_filter === 'customer' ? 'selected' : '' ?>>عميل</option>
                                    <option value="agent" <?= $role_filter === 'agent' ? 'selected' : '' ?>>وكيل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i>بحث
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="/admin/users" class="btn btn-outline-secondary w-100">
                                    <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Users Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة المستخدمين (<?= $totalUsers ?> مستخدم)</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الدور</th>
                                        <th>حالة KYC</th>
                                        <th>مستوى KYC</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                                    </div>
                                                    <?= htmlspecialchars($user['name']) ?>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($user['email']) ?></td>
                                            <td><?= htmlspecialchars($user['phone'] ?? 'غير محدد') ?></td>
                                            <td>
                                                <?php
                                                $roleColors = ['admin' => 'danger', 'customer' => 'primary', 'agent' => 'success'];
                                                $roleNames = ['admin' => 'مدير', 'customer' => 'عميل', 'agent' => 'وكيل'];
                                                ?>
                                                <span class="badge bg-<?= $roleColors[$user['role']] ?? 'secondary' ?>">
                                                    <?= $roleNames[$user['role']] ?? $user['role'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $kycColors = ['verified' => 'success', 'pending' => 'warning', 'rejected' => 'danger'];
                                                $kycNames = ['verified' => 'موثق', 'pending' => 'في الانتظار', 'rejected' => 'مرفوض'];
                                                ?>
                                                <span class="badge bg-<?= $kycColors[$user['kyc_status']] ?? 'secondary' ?>">
                                                    <?= $kycNames[$user['kyc_status']] ?? $user['kyc_status'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $levelNames = ['none' => 'لا يوجد', 'basic' => 'أساسي', 'enhanced' => 'محسن', 'full' => 'كامل'];
                                                ?>
                                                <small class="text-muted"><?= $levelNames[$user['kyc_level']] ?? $user['kyc_level'] ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                                    <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('Y-m-d', strtotime($user['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-outline-primary btn-sm" 
                                                            onclick="editUser(<?= htmlspecialchars(json_encode($user)) ?>)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                        <button class="btn btn-outline-danger btn-sm" 
                                                                onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['name']) ?>')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($role_filter) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_user">
                        
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الدور</label>
                                    <select class="form-select" name="role" required>
                                        <option value="customer">عميل</option>
                                        <option value="agent">وكيل</option>
                                        <option value="admin">مدير</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة KYC</label>
                                    <select class="form-select" name="kyc_status">
                                        <option value="pending">في الانتظار</option>
                                        <option value="verified">موثق</option>
                                        <option value="rejected">مرفوض</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">مستوى KYC</label>
                            <select class="form-select" name="kyc_level">
                                <option value="none">لا يوجد</option>
                                <option value="basic">أساسي</option>
                                <option value="enhanced">محسن</option>
                                <option value="full">كامل</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المستخدم</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editUserForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_user">
                        <input type="hidden" name="user_id" id="edit_user_id">
                        
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" name="name" id="edit_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" id="edit_email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone" id="edit_phone">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الدور</label>
                                    <select class="form-select" name="role" id="edit_role" required>
                                        <option value="customer">عميل</option>
                                        <option value="agent">وكيل</option>
                                        <option value="admin">مدير</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة KYC</label>
                                    <select class="form-select" name="kyc_status" id="edit_kyc_status">
                                        <option value="pending">في الانتظار</option>
                                        <option value="verified">موثق</option>
                                        <option value="rejected">مرفوض</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">مستوى KYC</label>
                            <select class="form-select" name="kyc_level" id="edit_kyc_level">
                                <option value="none">لا يوجد</option>
                                <option value="basic">أساسي</option>
                                <option value="enhanced">محسن</option>
                                <option value="full">كامل</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    المستخدم نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="deleteUserForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete_user">
                        <input type="hidden" name="user_id" id="delete_user_id">
                        
                        <div class="text-center">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف هذا المستخدم؟</h5>
                            <p class="text-muted">سيتم حذف المستخدم <strong id="delete_user_name"></strong> نهائياً ولا يمكن التراجع عن هذا الإجراء.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function editUser(user) {
            document.getElementById('edit_user_id').value = user.id;
            document.getElementById('edit_name').value = user.name;
            document.getElementById('edit_email').value = user.email;
            document.getElementById('edit_phone').value = user.phone || '';
            document.getElementById('edit_role').value = user.role;
            document.getElementById('edit_kyc_status').value = user.kyc_status;
            document.getElementById('edit_kyc_level').value = user.kyc_level;
            document.getElementById('edit_is_active').checked = user.is_active == 1;
            
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }
        
        function deleteUser(userId, userName) {
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_user_name').textContent = userName;
            
            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }
    </script>
</body>
</html>
