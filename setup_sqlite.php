#!/usr/bin/env php
<?php

/**
 * Elite Transfer System - SQLite Setup (Fallback)
 * This script sets up SQLite database as a fallback when MySQL is not available
 */

echo "\n";
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║           Elite Transfer System - SQLite Setup              ║\n";
echo "║                    (MySQL Fallback)                         ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";
echo "\n";

// Create database directory
$dbDir = __DIR__ . '/database';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
    echo "✅ Created database directory\n";
}

$dbPath = $dbDir . '/elite_transfer.db';

try {
    // Connect to SQLite
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database: {$dbPath}\n\n";
    
    // Create tables
    $tables = [
        'countries' => "
            CREATE TABLE IF NOT EXISTS countries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT NOT NULL UNIQUE,
                currency TEXT NOT NULL,
                flag_url TEXT,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ",
        
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                phone TEXT,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'customer',
                kyc_status TEXT DEFAULT 'pending',
                kyc_level TEXT DEFAULT 'none',
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ",
        
        'transfers' => "
            CREATE TABLE IF NOT EXISTS transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transfer_code TEXT UNIQUE NOT NULL,
                pickup_code TEXT NOT NULL,
                sender_id INTEGER NOT NULL,
                sender_name TEXT NOT NULL,
                sender_phone TEXT NOT NULL,
                sender_country_id INTEGER NOT NULL,
                receiver_name TEXT NOT NULL,
                receiver_phone TEXT NOT NULL,
                receiver_country_id INTEGER NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                converted_amount DECIMAL(15,2) NOT NULL,
                exchange_rate DECIMAL(10,6) NOT NULL,
                fee_amount DECIMAL(15,2) NOT NULL,
                total_amount DECIMAL(15,2) NOT NULL,
                sender_currency TEXT NOT NULL,
                receiver_currency TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                payment_method TEXT DEFAULT 'cash',
                pickup_method TEXT DEFAULT 'cash',
                agent_id INTEGER NULL,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users(id),
                FOREIGN KEY (sender_country_id) REFERENCES countries(id),
                FOREIGN KEY (receiver_country_id) REFERENCES countries(id)
            )
        ",
        
        'payments' => "
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transfer_id INTEGER NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                currency TEXT DEFAULT 'USD',
                payment_method TEXT NOT NULL,
                payment_provider TEXT,
                transaction_id TEXT,
                status TEXT DEFAULT 'pending',
                response_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (transfer_id) REFERENCES transfers(id)
            )
        ",
        
        'exchange_rates' => "
            CREATE TABLE IF NOT EXISTS exchange_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                from_currency TEXT NOT NULL,
                to_currency TEXT NOT NULL,
                rate DECIMAL(15,6) NOT NULL,
                provider TEXT DEFAULT 'fallback',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ",
        
        'notifications' => "
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                transfer_id INTEGER NULL,
                type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                sent_at DATETIME NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ",
        
        'system_settings' => "
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ",
        
        'aml_checks' => "
            CREATE TABLE IF NOT EXISTS aml_checks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transfer_id INTEGER NULL,
                user_id INTEGER NOT NULL,
                risk_score INTEGER DEFAULT 0,
                action TEXT NOT NULL,
                checks_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ",
        
        'kyc_documents' => "
            CREATE TABLE IF NOT EXISTS kyc_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                document_type TEXT NOT NULL,
                file_path TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ",
        
        'external_api_logs' => "
            CREATE TABLE IF NOT EXISTS external_api_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service_name TEXT NOT NULL,
                provider TEXT NOT NULL,
                endpoint TEXT,
                request_data TEXT,
                response_data TEXT,
                status_code INTEGER,
                response_time DECIMAL(10,3),
                success INTEGER DEFAULT 1,
                error_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        "
    ];
    
    // Create each table
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            echo "✅ Created table: {$tableName}\n";
        } catch (PDOException $e) {
            echo "⚠️  Table {$tableName}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📊 Inserting initial data...\n";
    
    // Insert countries
    $countries = [
        ['Saudi Arabia', 'SA', 'SAR'],
        ['United Arab Emirates', 'AE', 'AED'],
        ['Egypt', 'EG', 'EGP'],
        ['Kuwait', 'KW', 'KWD'],
        ['Jordan', 'JO', 'JOD'],
        ['United States', 'US', 'USD'],
        ['United Kingdom', 'GB', 'GBP'],
        ['European Union', 'EU', 'EUR']
    ];
    
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO countries (name, code, currency) VALUES (?, ?, ?)");
    foreach ($countries as $country) {
        $stmt->execute($country);
    }
    echo "✅ Inserted countries data (" . count($countries) . " countries)\n";
    
    // Insert admin user
    $adminPassword = password_hash('password', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO users (name, email, password, role, kyc_status, kyc_level) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['System Administrator', '<EMAIL>', $adminPassword, 'admin', 'verified', 'full']);
    echo "✅ Created admin user: <EMAIL> / password\n";
    
    // Insert demo customer
    $customerPassword = password_hash('customer123', PASSWORD_DEFAULT);
    $stmt->execute(['Demo Customer', '<EMAIL>', $customerPassword, 'customer', 'verified', 'basic']);
    echo "✅ Created demo customer: <EMAIL> / customer123\n";
    
    // Insert system settings
    $settings = [
        ['app_name', 'Elite Transfer System', 'Application name'],
        ['app_version', '7.0', 'Application version'],
        ['max_transfer_amount', '50000', 'Maximum transfer amount'],
        ['min_transfer_amount', '1', 'Minimum transfer amount'],
        ['default_fee_percentage', '2.5', 'Default fee percentage'],
        ['default_fee_fixed', '5.00', 'Default fixed fee'],
        ['aml_threshold_amount', '10000', 'AML threshold amount'],
        ['kyc_required_amount', '3000', 'KYC required amount'],
        ['sms_provider', 'local', 'SMS provider'],
        ['email_provider', 'local', 'Email provider'],
        ['exchange_rate_provider', 'fallback', 'Exchange rate provider']
    ];
    
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "✅ Inserted system settings (" . count($settings) . " settings)\n";
    
    // Insert exchange rates
    $rates = [
        ['USD', 'SAR', 3.7500],
        ['USD', 'AED', 3.6725],
        ['USD', 'EGP', 30.9000],
        ['USD', 'KWD', 0.3000],
        ['USD', 'JOD', 0.7090],
        ['USD', 'GBP', 0.8050],
        ['USD', 'EUR', 0.9250],
        ['SAR', 'EGP', 8.2400],
        ['AED', 'EGP', 8.4200],
        ['EUR', 'USD', 1.0800],
        ['GBP', 'USD', 1.2400]
    ];
    
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO exchange_rates (from_currency, to_currency, rate) VALUES (?, ?, ?)");
    foreach ($rates as $rate) {
        $stmt->execute($rate);
    }
    echo "✅ Inserted exchange rates (" . count($rates) . " rates)\n";
    
    // Create indexes for better performance
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX IF NOT EXISTS idx_transfers_code ON transfers(transfer_code)",
        "CREATE INDEX IF NOT EXISTS idx_transfers_status ON transfers(status)",
        "CREATE INDEX IF NOT EXISTS idx_transfers_sender ON transfers(sender_id)",
        "CREATE INDEX IF NOT EXISTS idx_payments_transfer ON payments(transfer_id)",
        "CREATE INDEX IF NOT EXISTS idx_exchange_rates_currencies ON exchange_rates(from_currency, to_currency)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $pdo->exec($index);
        } catch (PDOException $e) {
            // Index might already exist
        }
    }
    echo "✅ Created database indexes\n";
    
    // Verify setup
    echo "\n📋 Verifying database setup...\n";
    
    $tableCount = $pdo->query("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")->fetchColumn();
    echo "✅ Total tables created: {$tableCount}\n";
    
    $countryCount = $pdo->query("SELECT COUNT(*) FROM countries")->fetchColumn();
    echo "✅ Countries: {$countryCount}\n";
    
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "✅ Users: {$userCount}\n";
    
    $settingsCount = $pdo->query("SELECT COUNT(*) FROM system_settings")->fetchColumn();
    echo "✅ Settings: {$settingsCount}\n";
    
    $ratesCount = $pdo->query("SELECT COUNT(*) FROM exchange_rates")->fetchColumn();
    echo "✅ Exchange rates: {$ratesCount}\n";
    
    // Get database size
    $dbSize = round(filesize($dbPath) / 1024 / 1024, 2);
    echo "✅ Database size: {$dbSize} MB\n";
    
    echo "\n🎉 SQLite Database setup completed successfully!\n";
    echo "📁 Database file: {$dbPath}\n";
    echo "🔧 Database type: SQLite (Fallback mode)\n";
    echo "\n🚀 Start the application: php -S localhost:8000 -t public\n";
    echo "🌐 Visit: http://localhost:8000\n";
    echo "👤 Admin login: <EMAIL> / password\n";
    echo "👤 Customer login: <EMAIL> / customer123\n";
    
    echo "\n💡 Note: This is using SQLite as a fallback.\n";
    echo "   For production, please set up MySQL and update your .env file.\n";
    echo "   The system will automatically switch to MySQL when available.\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

?>
