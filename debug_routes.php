<?php

echo "🔍 Route Debug - Elite Transfer System\n\n";

// Test specific paths
$testPaths = [
    'transfers/create',
    'transfers/track',
    'create-transfer',
    'track-transfer',
    'dashboard',
    'login'
];

// Define routes (same as in index.php)
$routes = [
    '' => 'home.php',
    'home' => 'home.php',
    'login' => 'login.php',
    'logout' => 'logout.php',
    'register' => 'register.php',
    'dashboard' => 'dashboard.php',
    'create-transfer' => 'transfers/create.php',
    'track-transfer' => 'transfers/track.php',
    'admin/users' => 'admin/users.php',
    'admin/transfers' => 'admin/transfers.php',
    'admin/reports' => 'admin/reports.php',
    'admin/settings' => 'admin/settings.php',
    'admin/monitoring' => 'admin/monitoring.php',
    'compliance/dashboard' => 'compliance/dashboard.php',
    'api/health' => 'api/health.php',
    'api' => 'api.php',
    'api-test' => 'api-test.php',
    'health' => 'health.php',
    'stats' => 'stats.php',
    'countries' => 'countries.php',
    'users' => 'users.php',
    'transfers' => 'transfers.php',
    'transfers/create' => 'transfers/create.php',
    'transfers/track' => 'transfers/track.php',
    'transfers/view' => 'transfers/view.php'
];

foreach ($testPaths as $testPath) {
    echo "Testing path: '$testPath'\n";
    
    if (array_key_exists($testPath, $routes)) {
        $file = __DIR__ . '/public/' . $routes[$testPath];
        echo "  Route found: {$routes[$testPath]}\n";
        echo "  Full path: $file\n";
        echo "  File exists: " . (file_exists($file) ? "YES" : "NO") . "\n";
        
        if (file_exists($file)) {
            echo "  File size: " . filesize($file) . " bytes\n";
            echo "  File readable: " . (is_readable($file) ? "YES" : "NO") . "\n";
        }
    } else {
        echo "  Route NOT found in routes array\n";
    }
    echo "\n";
}

// Test server simulation
echo "🌐 Testing server simulation:\n";

function simulateRequest($path) {
    // Simulate the router logic
    $routes = [
        '' => 'home.php',
        'home' => 'home.php',
        'login' => 'login.php',
        'logout' => 'logout.php',
        'register' => 'register.php',
        'dashboard' => 'dashboard.php',
        'create-transfer' => 'transfers/create.php',
        'track-transfer' => 'transfers/track.php',
        'admin/users' => 'admin/users.php',
        'admin/transfers' => 'admin/transfers.php',
        'admin/reports' => 'admin/reports.php',
        'admin/settings' => 'admin/settings.php',
        'admin/monitoring' => 'admin/monitoring.php',
        'compliance/dashboard' => 'compliance/dashboard.php',
        'api/health' => 'api/health.php',
        'api' => 'api.php',
        'api-test' => 'api-test.php',
        'health' => 'health.php',
        'stats' => 'stats.php',
        'countries' => 'countries.php',
        'users' => 'users.php',
        'transfers' => 'transfers.php',
        'transfers/create' => 'transfers/create.php',
        'transfers/track' => 'transfers/track.php',
        'transfers/view' => 'transfers/view.php'
    ];
    
    if (array_key_exists($path, $routes)) {
        $file = __DIR__ . '/public/' . $routes[$path];
        if (file_exists($file)) {
            return "SUCCESS - File found and exists";
        } else {
            return "ERROR - Route found but file missing: $file";
        }
    } else {
        return "ERROR - Route not found";
    }
}

foreach ($testPaths as $path) {
    echo "Simulate '$path': " . simulateRequest($path) . "\n";
}

// Test actual HTTP request
echo "\n🌐 Testing actual HTTP requests:\n";

$testUrls = [
    'http://localhost:8000/transfers/create',
    'http://localhost:8000/create-transfer',
    'http://localhost:8000/dashboard',
    'http://localhost:8000/login'
];

foreach ($testUrls as $url) {
    echo "Testing: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  Error: $error\n";
    } else {
        echo "  HTTP Code: $httpCode\n";
        if ($httpCode == 200) {
            echo "  Status: SUCCESS ✅\n";
        } else {
            echo "  Status: FAILED ❌\n";
        }
    }
    echo "\n";
}

// Check directory structure
echo "📁 Directory structure:\n";
echo "public/ exists: " . (is_dir(__DIR__ . '/public') ? "YES" : "NO") . "\n";
echo "public/transfers/ exists: " . (is_dir(__DIR__ . '/public/transfers') ? "YES" : "NO") . "\n";

if (is_dir(__DIR__ . '/public/transfers')) {
    $files = scandir(__DIR__ . '/public/transfers');
    echo "Files in public/transfers/:\n";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo "  - $file\n";
        }
    }
}

?>
