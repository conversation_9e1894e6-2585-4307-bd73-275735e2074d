<?php

echo "\n";
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║                Elite Transfer System - Quick Test            ║\n";
echo "║                     System Validation                        ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";
echo "\n";

$tests = [
    'PHP Version Check' => function() {
        return version_compare(PHP_VERSION, '7.4.0', '>=');
    },
    'Database Connection' => function() {
        try {
            $db = new PDO('sqlite::memory:');
            $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $db->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    },
    'Required Extensions' => function() {
        $required = ['pdo', 'curl', 'json', 'mbstring'];
        foreach ($required as $ext) {
            if (!extension_loaded($ext)) {
                return false;
            }
        }
        return true;
    },
    'File Permissions' => function() {
        return is_writable('.') && is_readable('.');
    },
    'Memory Limit' => function() {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') return true;
        
        $bytes = 0;
        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);
        
        switch ($unit) {
            case 'g': $bytes = $value * 1024 * 1024 * 1024; break;
            case 'm': $bytes = $value * 1024 * 1024; break;
            case 'k': $bytes = $value * 1024; break;
            default: $bytes = (int) $limit;
        }
        
        return $bytes >= (128 * 1024 * 1024); // At least 128MB
    },
    'Security Functions' => function() {
        return function_exists('password_hash') && 
               function_exists('random_bytes') && 
               function_exists('hash_hmac');
    }
];

$passed = 0;
$total = count($tests);

foreach ($tests as $name => $test) {
    echo "🧪 Testing: " . str_pad($name, 25) . " ";
    
    try {
        $result = $test();
        if ($result) {
            echo "✅ PASSED\n";
            $passed++;
        } else {
            echo "❌ FAILED\n";
        }
    } catch (Exception $e) {
        echo "💥 ERROR: " . $e->getMessage() . "\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 Test Results Summary\n";
echo str_repeat("=", 60) . "\n";
echo "Total Tests: {$total}\n";
echo "✅ Passed: {$passed}\n";
echo "❌ Failed: " . ($total - $passed) . "\n";

$successRate = round(($passed / $total) * 100, 1);
echo "🎯 Success Rate: {$successRate}%\n";

if ($successRate >= 95) {
    echo "🎉 Excellent! System is ready.\n";
} elseif ($successRate >= 80) {
    echo "⚠️  Good, but some issues need attention.\n";
} else {
    echo "🚨 Critical issues found. System needs fixes.\n";
}

echo "\n📋 System Information:\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Operating System: " . PHP_OS . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "Current Memory Usage: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";

echo "\n🔧 Elite Transfer System Components:\n";

// Test basic application structure
$components = [
    'Bootstrap File' => file_exists('bootstrap/simple-app.php'),
    'Public Directory' => is_dir('public'),
    'Services Directory' => is_dir('app/Services'),
    'Tests Directory' => is_dir('tests'),
    'Docker Configuration' => file_exists('Dockerfile'),
    'Kubernetes Configuration' => file_exists('k8s/deployment.yaml'),
    'Environment Example' => file_exists('.env.example')
];

foreach ($components as $component => $exists) {
    echo "  " . str_pad($component, 25) . " " . ($exists ? "✅ Found" : "❌ Missing") . "\n";
}

echo "\n🚀 Elite Transfer System is ready for deployment!\n";
echo "   - Docker: docker-compose up -d\n";
echo "   - Kubernetes: kubectl apply -f k8s/\n";
echo "   - Local: php -S localhost:8000 -t public\n";
echo "\n";

?>
