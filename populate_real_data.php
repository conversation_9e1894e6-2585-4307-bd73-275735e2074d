<?php

echo "🌍 Populating Real Data - Elite Transfer System\n\n";

try {
    // Connect to production database
    $db = new PDO('sqlite:database/elite_transfer_production.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to production database\n\n";
    
    // Real countries data with comprehensive information
    echo "🌍 Inserting real countries data...\n";
    
    $countries = [
        ['Saudi Arabia', 'SA', 'SAU', 'SAR', '﷼', '+966', 'https://flagcdn.com/sa.svg', 'Asia', 'Western Asia', 34813867, 2149690.00, 833541000000.00, 'Arabic', 'Asia/Riyadh', 1, 2, 'Enhanced KYC required', 50000.00, 10000.00, 100000.00, 1, 2],
        ['United Arab Emirates', 'AE', 'ARE', 'AED', 'د.إ', '+971', 'https://flagcdn.com/ae.svg', 'Asia', 'Western Asia', 9890400, 83600.00, 507539000000.00, 'Arabic,English', 'Asia/Dubai', 1, 2, 'Standard KYC', 25000.00, 5000.00, 50000.00, 1, 1],
        ['Egypt', 'EG', 'EGY', 'EGP', '£', '+20', 'https://flagcdn.com/eg.svg', 'Africa', 'Northern Africa', 104258327, 1001450.00, 469440000000.00, 'Arabic', 'Africa/Cairo', 1, 3, 'Enhanced due diligence', 15000.00, 3000.00, 30000.00, 1, 4],
        ['Jordan', 'JO', 'JOR', 'JOD', 'د.ا', '+962', 'https://flagcdn.com/jo.svg', 'Asia', 'Western Asia', 10203140, 89342.00, 47450000000.00, 'Arabic', 'Asia/Amman', 1, 2, 'Standard KYC', 20000.00, 4000.00, 40000.00, 1, 3],
        ['Lebanon', 'LB', 'LBN', 'LBP', 'ل.ل', '+961', 'https://flagcdn.com/lb.svg', 'Asia', 'Western Asia', 6825442, 10452.00, 18730000000.00, 'Arabic,French', 'Asia/Beirut', 1, 4, 'Enhanced monitoring', 10000.00, 2000.00, 20000.00, 1, 6],
        ['Kuwait', 'KW', 'KWT', 'KWD', 'د.ك', '+965', 'https://flagcdn.com/kw.svg', 'Asia', 'Western Asia', 4270563, 17818.00, 134760000000.00, 'Arabic', 'Asia/Kuwait', 1, 2, 'Standard KYC', 30000.00, 6000.00, 60000.00, 1, 2],
        ['Qatar', 'QA', 'QAT', 'QAR', 'ر.ق', '+974', 'https://flagcdn.com/qa.svg', 'Asia', 'Western Asia', 2881060, 11586.00, 146400000000.00, 'Arabic', 'Asia/Qatar', 1, 2, 'Standard KYC', 35000.00, 7000.00, 70000.00, 1, 1],
        ['Bahrain', 'BH', 'BHR', 'BHD', '.د.ب', '+973', 'https://flagcdn.com/bh.svg', 'Asia', 'Western Asia', 1701583, 765.00, 38870000000.00, 'Arabic', 'Asia/Bahrain', 1, 2, 'Standard KYC', 25000.00, 5000.00, 50000.00, 1, 2],
        ['Oman', 'OM', 'OMN', 'OMR', 'ر.ع.', '+968', 'https://flagcdn.com/om.svg', 'Asia', 'Western Asia', 5106622, 309500.00, 76280000000.00, 'Arabic', 'Asia/Muscat', 1, 2, 'Standard KYC', 20000.00, 4000.00, 40000.00, 1, 3],
        ['Morocco', 'MA', 'MAR', 'MAD', 'د.م.', '+212', 'https://flagcdn.com/ma.svg', 'Africa', 'Northern Africa', 36910558, 446550.00, 132730000000.00, 'Arabic,French', 'Africa/Casablanca', 1, 3, 'Enhanced KYC', 12000.00, 2500.00, 25000.00, 1, 5],
        ['Tunisia', 'TN', 'TUN', 'TND', 'د.ت', '+216', 'https://flagcdn.com/tn.svg', 'Africa', 'Northern Africa', 11818618, 163610.00, 39650000000.00, 'Arabic,French', 'Africa/Tunis', 1, 3, 'Standard KYC', 8000.00, 1500.00, 15000.00, 1, 4],
        ['Algeria', 'DZ', 'DZA', 'DZD', 'د.ج', '+213', 'https://flagcdn.com/dz.svg', 'Africa', 'Northern Africa', 44700000, 2381741.00, 151460000000.00, 'Arabic,French', 'Africa/Algiers', 1, 4, 'Enhanced monitoring', 10000.00, 2000.00, 20000.00, 1, 6],
        ['Pakistan', 'PK', 'PAK', 'PKR', '₨', '+92', 'https://flagcdn.com/pk.svg', 'Asia', 'Southern Asia', 225200000, 881913.00, ************.00, 'Urdu,English', 'Asia/Karachi', 1, 3, 'Enhanced KYC', 15000.00, 3000.00, 30000.00, 1, 4],
        ['India', 'IN', 'IND', 'INR', '₹', '+91', 'https://flagcdn.com/in.svg', 'Asia', 'Southern Asia', 1380004385, 3287263.00, 3730000000000.00, 'Hindi,English', 'Asia/Kolkata', 1, 3, 'Enhanced due diligence', 20000.00, 4000.00, 40000.00, 1, 3],
        ['Bangladesh', 'BD', 'BGD', 'BDT', '৳', '+880', 'https://flagcdn.com/bd.svg', 'Asia', 'Southern Asia', 164689383, 147570.00, ************.00, 'Bengali', 'Asia/Dhaka', 1, 3, 'Enhanced KYC', 12000.00, 2500.00, 25000.00, 1, 5],
        ['Philippines', 'PH', 'PHL', 'PHP', '₱', '+63', 'https://flagcdn.com/ph.svg', 'Asia', 'South-Eastern Asia', 109581085, 300000.00, ************.00, 'Filipino,English', 'Asia/Manila', 1, 3, 'Standard KYC', 18000.00, 3500.00, 35000.00, 1, 4],
        ['Indonesia', 'ID', 'IDN', 'IDR', 'Rp', '+62', 'https://flagcdn.com/id.svg', 'Asia', 'South-Eastern Asia', 273523621, 1904569.00, 1319100000000.00, 'Indonesian', 'Asia/Jakarta', 1, 3, 'Enhanced KYC', 16000.00, 3200.00, 32000.00, 1, 4],
        ['Turkey', 'TR', 'TUR', 'TRY', '₺', '+90', 'https://flagcdn.com/tr.svg', 'Asia', 'Western Asia', 84339067, 783562.00, 819040000000.00, 'Turkish', 'Europe/Istanbul', 1, 3, 'Enhanced monitoring', 22000.00, 4500.00, 45000.00, 1, 3],
        ['United Kingdom', 'GB', 'GBR', 'GBP', '£', '+44', 'https://flagcdn.com/gb.svg', 'Europe', 'Northern Europe', 67886004, 242495.00, 3131000000000.00, 'English', 'Europe/London', 1, 1, 'Standard KYC', 50000.00, 10000.00, 100000.00, 1, 1],
        ['United States', 'US', 'USA', 'USD', '$', '+1', 'https://flagcdn.com/us.svg', 'Americas', 'Northern America', 331002647, 9833517.00, 25460000000000.00, 'English', 'America/New_York', 1, 1, 'Enhanced compliance', 75000.00, 15000.00, 150000.00, 1, 1]
    ];
    
    $countryStmt = $db->prepare("
        INSERT INTO countries (name, code, iso3_code, currency, currency_symbol, phone_prefix, flag_url, region, subregion, population, area_km2, gdp_usd, languages, timezone, is_active, risk_level, compliance_requirements, max_transaction_limit, daily_limit, monthly_limit, requires_kyc, processing_time_hours) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($countries as $country) {
        $countryStmt->execute($country);
    }
    
    echo "✅ Inserted " . count($countries) . " countries\n";
    
    // Real exchange rates
    echo "💱 Inserting real exchange rates...\n";
    
    $exchangeRates = [
        // Major currency pairs with realistic rates
        ['USD', 'SAR', 3.7500, 3.7400, 3.7600, 3.7500, 'xe.com', 'https://xe.com', 0.0200],
        ['USD', 'AED', 3.6725, 3.6700, 3.6750, 3.6725, 'xe.com', 'https://xe.com', 0.0150],
        ['USD', 'EGP', 30.9000, 30.8500, 30.9500, 30.9000, 'xe.com', 'https://xe.com', 0.0300],
        ['USD', 'JOD', 0.7090, 0.7085, 0.7095, 0.7090, 'xe.com', 'https://xe.com', 0.0100],
        ['USD', 'LBP', 89500.0000, 89000.0000, 90000.0000, 89500.0000, 'manual', null, 0.0500],
        ['USD', 'KWD', 0.3070, 0.3065, 0.3075, 0.3070, 'xe.com', 'https://xe.com', 0.0100],
        ['USD', 'QAR', 3.6400, 3.6380, 3.6420, 3.6400, 'xe.com', 'https://xe.com', 0.0150],
        ['USD', 'BHD', 0.3760, 0.3755, 0.3765, 0.3760, 'xe.com', 'https://xe.com', 0.0100],
        ['USD', 'OMR', 0.3845, 0.3840, 0.3850, 0.3845, 'xe.com', 'https://xe.com', 0.0100],
        ['USD', 'MAD', 10.1500, 10.1200, 10.1800, 10.1500, 'xe.com', 'https://xe.com', 0.0250],
        ['USD', 'TND', 3.1200, 3.1100, 3.1300, 3.1200, 'xe.com', 'https://xe.com', 0.0200],
        ['USD', 'DZD', 134.5000, 134.0000, 135.0000, 134.5000, 'xe.com', 'https://xe.com', 0.0400],
        ['USD', 'PKR', 278.5000, 278.0000, 279.0000, 278.5000, 'xe.com', 'https://xe.com', 0.0300],
        ['USD', 'INR', 83.2500, 83.2000, 83.3000, 83.2500, 'xe.com', 'https://xe.com', 0.0200],
        ['USD', 'BDT', 109.7500, 109.5000, 110.0000, 109.7500, 'xe.com', 'https://xe.com', 0.0250],
        ['USD', 'PHP', 56.2500, 56.2000, 56.3000, 56.2500, 'xe.com', 'https://xe.com', 0.0200],
        ['USD', 'IDR', 15750.0000, 15700.0000, 15800.0000, 15750.0000, 'xe.com', 'https://xe.com', 0.0300],
        ['USD', 'TRY', 32.1500, 32.1000, 32.2000, 32.1500, 'xe.com', 'https://xe.com', 0.0400],
        ['USD', 'GBP', 0.7850, 0.7845, 0.7855, 0.7850, 'xe.com', 'https://xe.com', 0.0100],
        
        // Cross rates
        ['SAR', 'EGP', 8.2400, 8.2200, 8.2600, 8.2400, 'manual', null, 0.0250],
        ['SAR', 'JOD', 0.1891, 0.1890, 0.1892, 0.1891, 'manual', null, 0.0150],
        ['SAR', 'PKR', 74.2667, 74.1000, 74.4000, 74.2667, 'manual', null, 0.0300],
        ['SAR', 'INR', 22.2000, 22.1500, 22.2500, 22.2000, 'manual', null, 0.0200],
        ['SAR', 'BDT', 29.2667, 29.2000, 29.3000, 29.2667, 'manual', null, 0.0250],
        ['SAR', 'PHP', 15.0000, 14.9500, 15.0500, 15.0000, 'manual', null, 0.0200],
        
        ['AED', 'EGP', 8.4150, 8.4000, 8.4300, 8.4150, 'manual', null, 0.0250],
        ['AED', 'PKR', 75.8163, 75.6000, 76.0000, 75.8163, 'manual', null, 0.0300],
        ['AED', 'INR', 22.6531, 22.6000, 22.7000, 22.6531, 'manual', null, 0.0200],
        ['AED', 'BDT', 29.8776, 29.8000, 29.9500, 29.8776, 'manual', null, 0.0250]
    ];
    
    $rateStmt = $db->prepare("
        INSERT INTO exchange_rates (from_currency, to_currency, rate, buy_rate, sell_rate, mid_rate, provider, source_url, margin_percentage, valid_from, valid_until) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now', '+1 day'))
    ");
    
    foreach ($exchangeRates as $rate) {
        $rateStmt->execute($rate);
    }
    
    echo "✅ Inserted " . count($exchangeRates) . " exchange rates\n";
    
    // System settings
    echo "⚙️ Inserting system settings...\n";
    
    $settings = [
        ['general', 'company_name', 'Elite Transfer System', 'string', 'Company name displayed in the system'],
        ['general', 'company_email', '<EMAIL>', 'string', 'Main company email address'],
        ['general', 'company_phone', '+966-11-234-5678', 'string', 'Main company phone number'],
        ['general', 'company_address', 'Riyadh, Saudi Arabia', 'string', 'Company headquarters address'],
        ['general', 'support_email', '<EMAIL>', 'string', 'Customer support email'],
        ['general', 'timezone', 'Asia/Riyadh', 'string', 'Default system timezone'],
        ['general', 'default_language', 'ar', 'string', 'Default system language'],
        ['general', 'currency_display_decimals', '2', 'integer', 'Number of decimal places for currency display'],
        
        ['fees', 'default_fee_percentage', '2.50', 'decimal', 'Default fee percentage for transfers'],
        ['fees', 'fixed_fee_amount', '5.00', 'decimal', 'Fixed fee amount added to all transfers'],
        ['fees', 'minimum_fee', '5.00', 'decimal', 'Minimum fee amount'],
        ['fees', 'maximum_fee', '500.00', 'decimal', 'Maximum fee amount'],
        ['fees', 'express_fee_multiplier', '1.50', 'decimal', 'Multiplier for express transfers'],
        ['fees', 'weekend_fee_multiplier', '1.25', 'decimal', 'Additional fee for weekend processing'],
        
        ['limits', 'min_transfer_amount', '10.00', 'decimal', 'Minimum transfer amount'],
        ['limits', 'max_transfer_amount', '50000.00', 'decimal', 'Maximum transfer amount'],
        ['limits', 'daily_transfer_limit', '10000.00', 'decimal', 'Daily transfer limit per user'],
        ['limits', 'monthly_transfer_limit', '100000.00', 'decimal', 'Monthly transfer limit per user'],
        ['limits', 'max_transfers_per_day', '10', 'integer', 'Maximum number of transfers per day per user'],
        
        ['kyc', 'kyc_required_amount', '1000.00', 'decimal', 'Amount above which KYC is required'],
        ['kyc', 'enhanced_kyc_amount', '5000.00', 'decimal', 'Amount above which enhanced KYC is required'],
        ['kyc', 'kyc_document_expiry_days', '365', 'integer', 'Days after which KYC documents expire'],
        ['kyc', 'auto_approve_verified_users', '1', 'boolean', 'Auto approve transfers for verified users'],
        
        ['notifications', 'email_notifications', '1', 'boolean', 'Enable email notifications'],
        ['notifications', 'sms_notifications', '1', 'boolean', 'Enable SMS notifications'],
        ['notifications', 'push_notifications', '1', 'boolean', 'Enable push notifications'],
        ['notifications', 'admin_notification_email', '<EMAIL>', 'string', 'Email for admin notifications'],
        
        ['security', 'session_timeout_minutes', '30', 'integer', 'Session timeout in minutes'],
        ['security', 'max_login_attempts', '5', 'integer', 'Maximum login attempts before lockout'],
        ['security', 'lockout_duration_minutes', '15', 'integer', 'Account lockout duration in minutes'],
        ['security', 'password_min_length', '8', 'integer', 'Minimum password length'],
        ['security', 'require_2fa_for_agents', '1', 'boolean', 'Require 2FA for agent accounts'],
        ['security', 'require_2fa_amount', '10000.00', 'decimal', 'Amount above which 2FA is required'],
        
        ['compliance', 'aml_screening_enabled', '1', 'boolean', 'Enable AML screening'],
        ['compliance', 'sanctions_screening_enabled', '1', 'boolean', 'Enable sanctions list screening'],
        ['compliance', 'pep_screening_enabled', '1', 'boolean', 'Enable PEP screening'],
        ['compliance', 'transaction_monitoring_enabled', '1', 'boolean', 'Enable transaction monitoring'],
        ['compliance', 'suspicious_amount_threshold', '25000.00', 'decimal', 'Amount above which transactions are flagged as suspicious'],
        
        ['api', 'rate_limit_per_minute', '60', 'integer', 'API rate limit per minute'],
        ['api', 'enable_webhook_notifications', '1', 'boolean', 'Enable webhook notifications'],
        ['api', 'webhook_timeout_seconds', '30', 'integer', 'Webhook timeout in seconds'],
        
        ['maintenance', 'backup_enabled', '1', 'boolean', 'Enable automatic backups'],
        ['maintenance', 'backup_frequency_hours', '24', 'integer', 'Backup frequency in hours'],
        ['maintenance', 'log_retention_days', '90', 'integer', 'Log retention period in days'],
        ['maintenance', 'cleanup_old_data_days', '365', 'integer', 'Days after which old data is cleaned up']
    ];
    
    $settingStmt = $db->prepare("
        INSERT INTO system_settings (category, key, value, data_type, description) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    foreach ($settings as $setting) {
        $settingStmt->execute($setting);
    }
    
    echo "✅ Inserted " . count($settings) . " system settings\n";
    
    // Create admin users
    echo "👥 Creating admin users...\n";

    $adminUsers = [
        [
            'USR001', 'System Administrator', '<EMAIL>', '+966501234567',
            password_hash('admin123', PASSWORD_DEFAULT), 'admin', 'active', 'verified', 5,
            1, 'Riyadh, Saudi Arabia', '1985-01-15', 'SA123456789', null, 'System Administrator',
            0.00, 'Employment', 0, null, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), 1, null, 0, null
        ],
        [
            'USR002', 'Ahmed Al-Rashid', '<EMAIL>', '+966502345678',
            password_hash('manager123', PASSWORD_DEFAULT), 'manager', 'active', 'verified', 4,
            1, 'Riyadh, Saudi Arabia', '1988-03-20', 'SA234567890', null, 'Operations Manager',
            15000.00, 'Employment', 15, null, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), 1, null, 0, null
        ],
        [
            'USR003', 'Fatima Al-Zahra', '<EMAIL>', '+966503456789',
            password_hash('agent123', PASSWORD_DEFAULT), 'agent', 'active', 'verified', 3,
            1, 'Jeddah, Saudi Arabia', '1992-07-10', 'SA345678901', null, 'Transfer Agent',
            8000.00, 'Employment', 25, null, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), 1, null, 0, null
        ],
        [
            'USR004', 'Omar Hassan', '<EMAIL>', '+966504567890',
            password_hash('compliance123', PASSWORD_DEFAULT), 'compliance', 'active', 'verified', 4,
            1, 'Dammam, Saudi Arabia', '1987-11-25', 'SA456789012', null, 'Compliance Officer',
            12000.00, 'Employment', 10, null, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), 1, null, 0, null
        ],
        [
            'USR005', 'Sarah Johnson', '<EMAIL>', '+966505678901',
            password_hash('customer123', PASSWORD_DEFAULT), 'customer', 'active', 'verified', 2,
            1, 'Riyadh, Saudi Arabia', '1990-05-15', 'SA567890123', 'P123456789', 'Software Engineer',
            10000.00, 'Employment', 35, null, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), 0, null, 0, null
        ]
    ];

    $userStmt = $db->prepare("
        INSERT INTO users (user_code, name, email, phone, password_hash, role, status, kyc_status, kyc_level, country_id, address, date_of_birth, national_id, passport_number, occupation, monthly_income, source_of_funds, risk_score, last_login_at, email_verified_at, phone_verified_at, two_factor_enabled, two_factor_secret, login_attempts, locked_until)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    foreach ($adminUsers as $user) {
        $userStmt->execute($user);
    }

    echo "✅ Created " . count($adminUsers) . " admin users\n";

    echo "\n🎉 Real data population completed successfully!\n";
    echo "📊 Database now contains comprehensive real-world data\n";
    echo "🔧 Ready for production use with large-scale operations\n";
    echo "\n👥 Admin Accounts Created:\n";
    echo "  - <EMAIL> / admin123 (System Admin)\n";
    echo "  - <EMAIL> / manager123 (Manager)\n";
    echo "  - <EMAIL> / agent123 (Agent)\n";
    echo "  - <EMAIL> / compliance123 (Compliance)\n";
    echo "  - <EMAIL> / customer123 (Customer)\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
