<?php
header('Content-Type: application/json');

try {
    // Check database connection
    $db = new PDO('sqlite:' . __DIR__ . '/../../database/elite_transfer.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test query
    $result = $db->query("SELECT COUNT(*) as count FROM users")->fetch();
    
    $response = [
        'status' => 'healthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'database' => 'connected',
        'users_count' => $result['count'],
        'version' => '7.0',
        'system' => 'Elite Transfer System'
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'status' => 'unhealthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'error' => $e->getMessage(),
        'version' => '7.0',
        'system' => 'Elite Transfer System'
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
