<?php

/**
 * Session Helper Class
 * Provides safe session management functions
 */
class SessionHelper
{
    /**
     * Start session safely
     * Only starts if no session is already active
     */
    public static function start()
    {
        if (session_status() === PHP_SESSION_NONE) {
            // Check if headers have been sent
            if (headers_sent()) {
                // If headers are sent, we can't start a session
                // But we can still work with $_SESSION if it exists
                return false;
            }

            // Start session with error suppression for edge cases
            @session_start();
        }
        return true;
    }
    
    /**
     * Check if session is active
     */
    public static function isActive()
    {
        return session_status() === PHP_SESSION_ACTIVE;
    }
    
    /**
     * Get session status as string
     */
    public static function getStatus()
    {
        switch (session_status()) {
            case PHP_SESSION_DISABLED:
                return 'disabled';
            case PHP_SESSION_NONE:
                return 'none';
            case PHP_SESSION_ACTIVE:
                return 'active';
            default:
                return 'unknown';
        }
    }
    
    /**
     * Get session value safely
     */
    public static function get($key, $default = null)
    {
        self::start();
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * Set session value safely
     */
    public static function set($key, $value)
    {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn()
    {
        self::start();
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Get current user ID
     */
    public static function getUserId()
    {
        return self::get('user_id');
    }
    
    /**
     * Get current user role
     */
    public static function getUserRole()
    {
        return self::get('user_role', 'customer');
    }
    
    /**
     * Get current user name
     */
    public static function getUserName()
    {
        return self::get('user_name', 'مستخدم');
    }
    
    /**
     * Get current user email
     */
    public static function getUserEmail()
    {
        return self::get('user_email');
    }
    
    /**
     * Check if user has specific role
     */
    public static function hasRole($role)
    {
        return self::getUserRole() === $role;
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin()
    {
        return self::hasRole('admin') || self::hasRole('super_admin');
    }
    
    /**
     * Check if user is agent
     */
    public static function isAgent()
    {
        return self::hasRole('agent');
    }
    
    /**
     * Check if user is customer
     */
    public static function isCustomer()
    {
        return self::hasRole('customer');
    }
    
    /**
     * Login user
     */
    public static function login($user)
    {
        self::start();
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_phone'] = $user['phone'] ?? '';
        $_SESSION['login_time'] = time();
        
        // Regenerate session ID for security
        session_regenerate_id(true);
    }
    
    /**
     * Logout user
     */
    public static function logout()
    {
        self::start();
        
        // Clear all session variables
        $_SESSION = array();
        
        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Flash message functions
     */
    public static function setFlash($type, $message)
    {
        self::start();
        $_SESSION['flash'][$type] = $message;
    }
    
    public static function getFlash($type)
    {
        self::start();
        $message = $_SESSION['flash'][$type] ?? null;
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    
    public static function hasFlash($type)
    {
        self::start();
        return isset($_SESSION['flash'][$type]);
    }
    
    /**
     * CSRF Token functions
     */
    public static function generateCSRFToken()
    {
        self::start();
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    public static function validateCSRFToken($token)
    {
        self::start();
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Session timeout functions
     */
    public static function checkTimeout($timeout = 3600) // 1 hour default
    {
        self::start();
        
        if (isset($_SESSION['login_time'])) {
            if (time() - $_SESSION['login_time'] > $timeout) {
                self::logout();
                return false;
            }
            // Update last activity time
            $_SESSION['last_activity'] = time();
        }
        
        return true;
    }
    
    /**
     * Get session info for debugging
     */
    public static function getInfo()
    {
        return [
            'status' => self::getStatus(),
            'id' => session_id(),
            'name' => session_name(),
            'is_logged_in' => self::isLoggedIn(),
            'user_id' => self::getUserId(),
            'user_role' => self::getUserRole(),
            'user_name' => self::getUserName(),
            'login_time' => self::get('login_time'),
            'last_activity' => self::get('last_activity')
        ];
    }
    
    /**
     * Clean old sessions (for maintenance)
     */
    public static function cleanup()
    {
        // This would typically be called by a cron job
        // For now, just clean current session if expired
        if (!self::checkTimeout()) {
            return 'Session expired and cleaned';
        }
        return 'Session is valid';
    }
}
?>
