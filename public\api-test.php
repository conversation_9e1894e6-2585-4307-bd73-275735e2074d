<?php
// Load session helper
require_once __DIR__ . '/includes/session_helper.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Elite Transfer System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .api-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .api-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
        }
        
        .api-body {
            padding: 20px;
        }
        
        .response-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-4">
                <i class="bi bi-code-slash me-3"></i>
                API Test Center
            </h1>
            <p class="lead text-muted">اختبار جميع APIs في نظام Elite Transfer</p>
        </div>
        
        <!-- Navigation -->
        <div class="text-center mb-4">
            <a href="/" class="btn btn-outline-primary me-2">
                <i class="bi bi-house me-1"></i>الرئيسية
            </a>
            <a href="/dashboard" class="btn btn-outline-success me-2">
                <i class="bi bi-speedometer2 me-1"></i>لوحة التحكم
            </a>
            <?php if (is_logged_in()): ?>
                <span class="badge bg-success">
                    <i class="bi bi-person-check me-1"></i>
                    مسجل دخول: <?= htmlspecialchars(get_user_data()['name']) ?>
                </span>
            <?php else: ?>
                <a href="/login" class="btn btn-outline-warning">
                    <i class="bi bi-box-arrow-in-right me-1"></i>تسجيل الدخول
                </a>
            <?php endif; ?>
        </div>
        
        <!-- API Tests -->
        <div class="row">
            <!-- Health Check API -->
            <div class="col-lg-6 mb-4">
                <div class="api-card">
                    <div class="api-header">
                        <h5 class="mb-0">
                            <i class="bi bi-heart-pulse me-2"></i>
                            Health Check API
                        </h5>
                    </div>
                    <div class="api-body">
                        <p class="text-muted">فحص حالة النظام وقاعدة البيانات</p>
                        
                        <div class="mb-3">
                            <strong>Endpoints:</strong>
                            <ul class="list-unstyled ms-3">
                                <li><code>GET /health</code></li>
                                <li><code>GET /api?endpoint=health</code></li>
                            </ul>
                        </div>
                        
                        <button class="btn btn-test" onclick="testAPI('health', '/health')">
                            <i class="bi bi-play-circle me-2"></i>
                            اختبار Health API
                        </button>
                        
                        <div class="mt-3">
                            <div id="health-status" class="status-badge status-loading d-none">
                                <i class="bi bi-hourglass-split me-1"></i>جاري التحميل...
                            </div>
                            <div id="health-response" class="response-box mt-2 d-none"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Stats API -->
            <div class="col-lg-6 mb-4">
                <div class="api-card">
                    <div class="api-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            Statistics API
                        </h5>
                    </div>
                    <div class="api-body">
                        <p class="text-muted">إحصائيات مفصلة عن النظام</p>
                        
                        <div class="mb-3">
                            <strong>Endpoint:</strong>
                            <ul class="list-unstyled ms-3">
                                <li><code>GET /api?endpoint=stats</code></li>
                            </ul>
                        </div>
                        
                        <button class="btn btn-test" onclick="testAPI('stats', '/api?endpoint=stats')">
                            <i class="bi bi-play-circle me-2"></i>
                            اختبار Stats API
                        </button>
                        
                        <div class="mt-3">
                            <div id="stats-status" class="status-badge status-loading d-none">
                                <i class="bi bi-hourglass-split me-1"></i>جاري التحميل...
                            </div>
                            <div id="stats-response" class="response-box mt-2 d-none"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Countries API -->
            <div class="col-lg-6 mb-4">
                <div class="api-card">
                    <div class="api-header">
                        <h5 class="mb-0">
                            <i class="bi bi-globe me-2"></i>
                            Countries API
                        </h5>
                    </div>
                    <div class="api-body">
                        <p class="text-muted">قائمة الدول المتاحة للتحويل</p>
                        
                        <div class="mb-3">
                            <strong>Endpoint:</strong>
                            <ul class="list-unstyled ms-3">
                                <li><code>GET /api?endpoint=countries</code></li>
                            </ul>
                        </div>
                        
                        <button class="btn btn-test" onclick="testAPI('countries', '/api?endpoint=countries')">
                            <i class="bi bi-play-circle me-2"></i>
                            اختبار Countries API
                        </button>
                        
                        <div class="mt-3">
                            <div id="countries-status" class="status-badge status-loading d-none">
                                <i class="bi bi-hourglass-split me-1"></i>جاري التحميل...
                            </div>
                            <div id="countries-response" class="response-box mt-2 d-none"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Users API (Admin Only) -->
            <div class="col-lg-6 mb-4">
                <div class="api-card">
                    <div class="api-header">
                        <h5 class="mb-0">
                            <i class="bi bi-people me-2"></i>
                            Users API
                            <span class="badge bg-warning ms-2">Admin Only</span>
                        </h5>
                    </div>
                    <div class="api-body">
                        <p class="text-muted">قائمة المستخدمين (يتطلب صلاحيات المدير)</p>
                        
                        <div class="mb-3">
                            <strong>Endpoint:</strong>
                            <ul class="list-unstyled ms-3">
                                <li><code>GET /api?endpoint=users</code></li>
                            </ul>
                        </div>
                        
                        <button class="btn btn-test" onclick="testAPI('users', '/api?endpoint=users')">
                            <i class="bi bi-play-circle me-2"></i>
                            اختبار Users API
                        </button>
                        
                        <div class="mt-3">
                            <div id="users-status" class="status-badge status-loading d-none">
                                <i class="bi bi-hourglass-split me-1"></i>جاري التحميل...
                            </div>
                            <div id="users-response" class="response-box mt-2 d-none"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Transfers API -->
            <div class="col-lg-6 mb-4">
                <div class="api-card">
                    <div class="api-header">
                        <h5 class="mb-0">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            Transfers API
                            <span class="badge bg-info ms-2">Login Required</span>
                        </h5>
                    </div>
                    <div class="api-body">
                        <p class="text-muted">قائمة التحويلات (يتطلب تسجيل الدخول)</p>
                        
                        <div class="mb-3">
                            <strong>Endpoint:</strong>
                            <ul class="list-unstyled ms-3">
                                <li><code>GET /api?endpoint=transfers</code></li>
                            </ul>
                        </div>
                        
                        <button class="btn btn-test" onclick="testAPI('transfers', '/api?endpoint=transfers')">
                            <i class="bi bi-play-circle me-2"></i>
                            اختبار Transfers API
                        </button>
                        
                        <div class="mt-3">
                            <div id="transfers-status" class="status-badge status-loading d-none">
                                <i class="bi bi-hourglass-split me-1"></i>جاري التحميل...
                            </div>
                            <div id="transfers-response" class="response-box mt-2 d-none"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Test All APIs -->
            <div class="col-12 mb-4">
                <div class="api-card">
                    <div class="api-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            Test All APIs
                        </h5>
                    </div>
                    <div class="api-body text-center">
                        <p class="text-muted">اختبار جميع APIs دفعة واحدة</p>
                        
                        <button class="btn btn-test btn-lg" onclick="testAllAPIs()">
                            <i class="bi bi-rocket me-2"></i>
                            اختبار جميع APIs
                        </button>
                        
                        <div class="mt-3">
                            <div id="all-status" class="status-badge status-loading d-none">
                                <i class="bi bi-hourglass-split me-1"></i>جاري اختبار جميع APIs...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        async function testAPI(name, endpoint) {
            const statusEl = document.getElementById(name + '-status');
            const responseEl = document.getElementById(name + '-response');
            
            // Show loading
            statusEl.className = 'status-badge status-loading';
            statusEl.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>جاري التحميل...';
            statusEl.classList.remove('d-none');
            responseEl.classList.add('d-none');
            
            try {
                const response = await fetch(endpoint);
                const data = await response.text();
                
                if (response.ok) {
                    statusEl.className = 'status-badge status-success';
                    statusEl.innerHTML = '<i class="bi bi-check-circle me-1"></i>نجح الاختبار';
                } else {
                    statusEl.className = 'status-badge status-error';
                    statusEl.innerHTML = '<i class="bi bi-x-circle me-1"></i>فشل الاختبار';
                }
                
                // Try to format JSON
                try {
                    const jsonData = JSON.parse(data);
                    responseEl.textContent = JSON.stringify(jsonData, null, 2);
                } catch (e) {
                    responseEl.textContent = data;
                }
                
                responseEl.classList.remove('d-none');
                
            } catch (error) {
                statusEl.className = 'status-badge status-error';
                statusEl.innerHTML = '<i class="bi bi-x-circle me-1"></i>خطأ في الشبكة';
                
                responseEl.textContent = 'Network Error: ' + error.message;
                responseEl.classList.remove('d-none');
            }
        }
        
        async function testAllAPIs() {
            const statusEl = document.getElementById('all-status');
            statusEl.classList.remove('d-none');
            
            const apis = [
                { name: 'health', endpoint: '/health' },
                { name: 'stats', endpoint: '/api?endpoint=stats' },
                { name: 'countries', endpoint: '/api?endpoint=countries' },
                { name: 'users', endpoint: '/api?endpoint=users' },
                { name: 'transfers', endpoint: '/api?endpoint=transfers' }
            ];
            
            for (const api of apis) {
                await testAPI(api.name, api.endpoint);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            }
            
            statusEl.className = 'status-badge status-success';
            statusEl.innerHTML = '<i class="bi bi-check-circle me-1"></i>تم اختبار جميع APIs';
        }
    </script>
</body>
</html>
